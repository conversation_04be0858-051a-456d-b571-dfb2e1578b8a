<?php

namespace Modules\RoleManagement\Controllers;

use App\Controllers\AdminController;
use Modules\RoleManagement\Models\RoleModel;
use Modules\RoleManagement\Models\PermissionModel;
use Modules\RoleManagement\Models\RolePermissionModel;
use Modules\RoleManagement\Models\UserRoleModel;
use Modules\Authentication\Services\PermissionService;
use Modules\RoleManagement\Config\Validation;
use \Hermawan\DataTables\DataTable;

class RoleController extends AdminController
{
    protected $roleModel;
    protected $permissionModel;
    protected $rolePermissionModel;
    protected $userRoleModel;
    protected $permissionService;

    public function __construct()
    {
        $this->roleModel = new RoleModel();
        $this->permissionModel = new PermissionModel();
        $this->rolePermissionModel = new RolePermissionModel();
        $this->userRoleModel = new UserRoleModel();
        $this->permissionService = new PermissionService();
    }

    public function index()
    {
        // if (!hasPermission('manage_roles')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }
        $data['title'] = 'Role Management';
        $data['page_title'] = 'Role Management';
        $data['statistics'] = $this->roleModel->getStatistics();
        $data['roles'] = $this->roleModel->findAll();
        return view('Modules\RoleManagement\Views\roles\index', $data);
    }

    public function create()
    {
        // if (!hasPermission('role.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }

        $data = [
            // Pass grouped permissions to the view
            // 'permissions_grouped' => $this->permissionService->getAllPermissionsGroupedByCategory(),
            'title'       => 'Create Role',
            'page_title'  => 'Create New Role',
        ];
        return view('Modules\RoleManagement\Views\roles\create', $data);
    }

    public function store()
    {
        // if (!hasPermission('role.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }
        
        $rules = [
            'name'        => 'required|min_length[3]|max_length[50]|is_unique[roles.name]',
            'description' => 'permit_empty|max_length[255]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'        => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
        ];

        if ($this->roleModel->save($data)) {
            return redirect()->to(site_url('admin/roles'))->with('success', 'Role created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create role.');
        }
    }

    public function edit($id=null)
    {
        // if (!hasPermission('role.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }


        $role = $this->roleModel->find($id);
        // Fetch permissions currently assigned to this role
        // $assignedPermissionIds = $this->permissionService->getPermissionsByRoleId($id);

        if (empty($role)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Role not found: ' . $id);
        }

        $data = [
            'role'                => $role,
            //'permissions'         => $this->permissionService->getAllPermissions(),
            // 'permissions_grouped' => $this->permissionService->getAllPermissionsGroupedByCategory(),
            // 'assignedPermissions' => $assignedPermissionIds,
            'title'               => 'Manage Role',
            'page_title'          => 'Role Management',
        ];

        // $data['title'] = 'Manage Roles';
        // $data['page_title'] = 'Role Management';
        
        // $permissionService = new PermissionService();
        // $data['permissions'] = $permissionService->getPermissionsByRoleId($id);
        return view('Modules\RoleManagement\Views\roles\edit', $data);
    }

    public function update($id)
    {
        // if (!hasPermission('role.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }

        // // Load validation rules and add the ID for unique validation
        // $validation = new Validation();
        // $rules = $validation->update_role;

        // // Replace {id} placeholder in the unique rule
        // $rules['name'] = str_replace('{id}', $id, $rules['name']);
        
        // if(!$this->validate($rules, $validation->update_role_errors)){
        //     return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        // }

        // $roleModel = new RoleModel();
        // $data = [
        //     'name' => $this->request->getPost('name'),
        //     'description' => $this->request->getPost('description'),
        // ];
        
        // // Handle permissions first
        // $permissions = $this->request->getVar('permissions') ?? [];
        // $roleModel->setRolePermissions($id, $permissions);
        
        // if(!$roleModel->update($id, $data)){
        //     echo 'Failed to update role.';exit();
        //     return redirect()->back()->withInput()->with('error', 'Failed to update role.');
        // }
        // return redirect()->route('roles.index')->with('success', 'Role updated successfully.');

        $rules = [
            'name'        => "required|min_length[3]|max_length[50]|is_unique[roles.name,id,{$id}]",
            'description' => 'permit_empty|max_length[255]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'id'          => $id,
            'name'        => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
        ];

        if ($this->roleModel->save($data)) {
            return redirect()->to(site_url('admin/roles'))->with('success', 'Role updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update role.');
        }
    }

    public function delete($id)
    {
        // if (!hasPermission('role.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage roles.');
        // }

        // Start a transaction for atomicity (even with MyISAM, good practice)
        $this->roleModel->db->transBegin();

        try {
            // Delete associated role_permissions
            // $this->rolePermissionModel->where('role_id', $id)->delete();
            
            // Delete associated user_roles
            $this->userRoleModel->where('role_id', $id)->delete(); // Use userRoleModel here

            // Finally, delete the role itself
            $this->roleModel->delete($id);

            $this->roleModel->db->transCommit();
            return redirect()->to(site_url('admin/roles'))->with('success', 'Role deleted successfully.');
        } catch (\Exception $e) {
            $this->roleModel->db->transRollback();
            return redirect()->to(site_url('admin/roles'))->with('error', 'Failed to delete role: ' . $e->getMessage());
        }
    }


    public function assignPermissions($roleId)
    {
        $role = $this->roleModel->find($roleId);
        if (empty($role)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Role not found: ' . $roleId);
        }

        $allPermissions = $this->permissionModel->findAll();
        // Group permissions by category for better display
        $categorizedPermissions = [];
        foreach ($allPermissions as $perm) {
            $category = $perm['category'] ?: 'Uncategorized';
            $categorizedPermissions[$category][] = $perm;
        }

        $assignedPermissions = $this->roleModel->getPermissions($roleId);
        $assignedPermissionIds = array_column($assignedPermissions, 'id');

        $data = [
            'title' => 'Assign Permissions',
            'page_title' => 'Assign Permissions',
            'role' => $role,
            'categorizedPermissions' => $categorizedPermissions,
            'assignedPermissionIds' => $assignedPermissionIds,
        ];

        return view('Modules\RoleManagement\Views\roles\assign_permissions', $data);
    }

    public function savePermissions($roleId)
    {
        // dd($this->request->getPost());exit();
        $role = $this->roleModel->find($roleId);
        // dd($role);exit();
        if (empty($role)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Role not found: ' . $roleId);
        }

        $permissionIds = $this->request->getPost('permissions') ?? [];
        // dd($permissionIds);exit();

        if ($this->roleModel->assignPermissions($roleId, $permissionIds)) {
            return redirect()->to(site_url('admin/roles'))->with('success', 'Permissions assigned successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to assign permissions.');
        }
    }










    /**
     * Show role details for modal view
     */
    public function show($id)
    {
        // if (!hasPermission('role.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $role = $this->roleModel->find($id);
        if (empty($role)) {
            return $this->response->setJSON(['error' => 'Role not found']);
        }

        // Get role permissions
        $permissions = $this->roleModel->getPermissions($id);

        // Get user count for this role
        $userCount = $this->roleModel->db->table('user_roles')
                                        ->where('role_id', $id)
                                        ->where('is_active', 1)
                                        ->countAllResults();

        $data = [
            'role' => $role,
            'permissions' => $permissions,
            'user_count' => $userCount
        ];

        return view('Modules\RoleManagement\Views\roles\show', $data);
    }

    /**
     * AJAX delete role
     */
    public function ajaxDelete()
    {
        if (!hasPermission('role.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $id = $this->request->getPost('id');

        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid role ID']);
        }

        // Check if role exists
        $role = $this->roleModel->find($id);
        if (!$role) {
            return $this->response->setJSON(['success' => false, 'message' => 'Role not found']);
        }

        // Check if role is being used by users
        $userCount = $this->roleModel->getUserCountByRole($id);
        if ($userCount > 0) {
            return $this->response->setJSON(['success' => false, 'message' => "Cannot delete role. It is assigned to $userCount user(s)."]);
        }

        if ($this->roleModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Role deleted successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete role']);
    }


    /**
     * DataTables AJAX endpoint
     */
    public function datatable()
    {
        // if (!hasPermission('role.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $builder = $this->roleModel->db->table('roles');
        //print_r debug
        // print_r($builder->get()->getResultArray());exit();

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex')
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->edit('name', function($row) {
                return '<strong>' . esc($row->name) . '</strong>';
            })
            ->edit('description', function($row) {
                return !empty($row->description) ? esc($row->description) : '<span class="text-muted">No description</span>';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->edit('created_at', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                // Temporarily disable permission check for debugging
                // if (hasPermission('role.manage')) {
                    $actions .= '<button type="button" class="btn btn-info btn-sm me-1" onclick="viewRole(' . $row->id . ')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>';
                    $actions .= '<a href="' . site_url('admin/roles/edit/' . $row->id) . '" class="btn btn-warning btn-sm me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    $actions .= '<a href="' . site_url('admin/roles/assign-permissions/' . $row->id) . '" class="btn btn-secondary btn-sm me-1" title="Assign Permissions">
                        <i class="fas fa-key"></i>
                    </a>';
                    $actions .= '<button id="deleteRole" type="button" class="btn btn-danger btn-sm" title="Delete" data-id="'. $row->id.'">
                        <i class="fas fa-trash"></i>
                    </button>';
                // }
                return $actions;
            })
            ->toJson(true);
    }

    /**
     * Bulk delete roles
     */
    public function bulkDelete()
    {
        // if (!hasPermission('role.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No roles selected']);
        }

        // Check if any of the roles are system roles that shouldn't be deleted
        $systemRoles = $this->roleModel->whereIn('id', $ids)
                                      ->where('is_system', 1)
                                      ->findAll();

        if (!empty($systemRoles)) {
            $systemRoleNames = array_column($systemRoles, 'name');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot delete system roles: ' . implode(', ', $systemRoleNames)
            ]);
        }

        $result = $this->roleModel->bulkDelete($ids);

        if (isset($result['error'])) {
            return $this->response->setJSON(['success' => false, 'message' => $result['error']]);
        }

        return $this->response->setJSON(['success' => true, 'message' => 'Selected roles deleted successfully']);
    }

    /**
     * Bulk activate roles
     */
    public function bulkActivate()
    {
        // if (!hasPermission('role.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No roles selected']);
        }

        if ($this->roleModel->bulkUpdateStatus($ids, 1)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected roles activated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate roles']);
    }

    /**
     * Bulk deactivate roles
     */
    public function bulkDeactivate()
    {
        // if (!hasPermission('role.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No roles selected']);
        }

        if ($this->roleModel->bulkUpdateStatus($ids, 0)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected roles deactivated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate roles']);
    }

}
