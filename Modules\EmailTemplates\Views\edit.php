<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/email-templates') ?>">Email Templates</a></li>
<li class="breadcrumb-item active">Edit Template</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<style>
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99999 !important;
        border-radius: 8px;
        flex-direction: column;
    }
    
    .loading-overlay.hidden {
        display: none !important;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    #email-editor {
        height: 600px;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    .placeholder-tag {
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .placeholder-tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Edit Email Template: <?= esc($template['name']) ?></h3>
    </div>
    <form id="templateForm">
        <?= csrf_field() ?>
        <input type="hidden" id="templateId" value="<?= $template['id'] ?>">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateName">Template Name *</label>
                        <input type="text" class="form-control" id="templateName" name="name" 
                               value="<?= esc($template['name']) ?>" required>
                    </div>
                </div>
                <!-- <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateSlug">Slug</label>
                        <input type="text" class="form-control" id="templateSlug" name="slug" 
                               value="<?//= esc($template['slug']) ?>" readonly>
                    </div>
                </div> -->


                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateSlug">Slug <span class="text-danger">*</span><small class="text-muted">(Unique identifier for this template)</small></label>
                        <input type="text" class="form-control" id="templateSlug" name="slug" value="<?= esc($template['slug']) ?>"><small class="form-text text-muted">Use only lowercase letters, numbers, and underscores. No spaces allowed.</small>
                    </div>
                </div>


            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateCategory">Category</label>
                        <select class="form-control" id="templateCategory" name="category_name">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= $category ?>" 
                                    <?= $template['category_name'] == $category ? 'selected' : '' ?>>
                                <?= esc($category) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateStatus">Status</label>
                        <select class="form-control" id="templateStatus" name="is_active">
                            <option value="1" <?= $template['is_active'] ? 'selected' : '' ?>>Active</option>
                            <option value="0" <?= !$template['is_active'] ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="templateDescription">Description</label>
                <textarea class="form-control" id="templateDescription" name="description" 
                          rows="3"><?= esc($template['description']) ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="templateSubject">Email Subject *</label>
                <input type="text" class="form-control" id="templateSubject" name="subject" 
                       value="<?= esc($template['subject']) ?>" required>
            </div>
        </div>
    </form>
</div>

<!-- Email Editor Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Email Design</h3>
        <div class="card-tools">
            <button type="button" class="btn btn-tool" id="undoBtn" title="Undo">
                <i class="fas fa-undo"></i>
            </button>
            <button type="button" class="btn btn-tool" id="redoBtn" title="Redo">
                <i class="fas fa-redo"></i>
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="position-relative">
            <div id="email-editor"></div>
            <div id="editor-loading" class="loading-overlay" style="display: none;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 15px; color: #666; font-size: 14px;">Loading Email Editor...</p>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <button type="submit" class="btn btn-primary" id="updateTemplateBtn">
            <i class="fas fa-save mr-2"></i>Update Template
        </button>
        <a href="<?= base_url('admin/email-templates') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to List
        </a>
        <button type="button" class="btn btn-info float-right" id="previewBtn">
            <i class="fas fa-eye mr-2"></i>Preview
        </button>
    </div>
</div>

<!-- Placeholders Card -->
<!-- <div class="card">
    <div class="card-header">
        <h3 class="card-title">Available Placeholders</h3>
    </div>
    <div class="card-body">
        <p class="text-muted small mb-3">Click on any placeholder to copy it to clipboard.</p>
        
        <h6>Default Placeholders:</h6>
        <div class="mb-3">
            <?php //foreach ($config->defaultPlaceholders as $key => $placeholder): ?>
            <span class="badge badge-secondary mr-1 mb-1 placeholder-tag" 
                  data-placeholder="{{<?//= $key ?>}}" 
                  title="<?//= $placeholder['description'] ?>" 
                  style="cursor: pointer;">
                {{<?//= $key ?>}}
            </span>
            <?php //endforeach; ?>
        </div>
    </div>
</div> -->

<!-- Template Info Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Template Information</h3>
    </div>
    <div class="card-body">
        <p><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($template['created_at'])) ?></p>
        <p><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($template['updated_at'])) ?></p>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script src="https://editor.unlayer.com/embed.js"></script>

<script>
$(document).ready(function() {
    let unlayerEditor;
    let isEditorReady = false;

    // Show notification function
    function showNotification(type, title, message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: type,
                title: title,
                text: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        } else {
            alert(`${title}: ${message}`);
        }
    }

    // Make sure the Unlayer library is loaded
    if (typeof unlayer === 'undefined') {
        showNotification('error', 'Editor Error', 'Email editor failed to load. Please refresh the page.');
        return;
    }
    
    // Initialize Unlayer editor
    if (document.getElementById('email-editor')) {
        if (typeof unlayer === 'undefined') {
            console.error('Unlayer is not loaded.');
        } else {
            console.log('Unlayer is loaded.');
        }
        $('#editor-loading').show().removeClass('hidden');
        
        const loadingTimeout = setTimeout(function() {
            $('#editor-loading').hide().addClass('hidden');
        }, 10000);


        unlayerEditor = unlayer.createEditor({
            id: 'email-editor',
            displayMode: 'email',
            designMode: 'edit',
            projectId: 1,
            tools: {},
            appearance: {
                theme: 'modern_light',
                panels: {
                    tools: { dock: 'right' }
                }
            },
            callbacks: {
                onReady: function() {
                    console.log('Editor ready');
                    clearTimeout(loadingTimeout);
                    isEditorReady = true;
                    $('#editor-loading').hide().addClass('hidden');
                    // Load existing design if available
                    <?php if (!empty($template['design_json'])): ?>
                    try {
                        const design = JSON.parse(<?= json_encode($template['design_json']) ?>);
                        console.log(design);
                        unlayerEditor.loadDesign(design);
                    } catch (e) {
                        console.error('Error loading design:', e);
                        unlayerEditor.loadBlank();
                    }
                    <?php else: ?>
                    unlayerEditor.loadBlank();
                    <?php endif; ?>
                    
                    showNotification('success', 'Editor Ready', 'Email editor loaded successfully');
                },
                onDesignLoad: function(data) {
                    // $('#editor-loading').hide().addClass('hidden');
                },
                onDesignUpdated: function(data) {
                    window.formDirty = true;
                }
            },
            onDesignLoad: function(data) {
                    // $('#editor-loading').hide().addClass('hidden');
                },
                onDesignUpdated: function(data) {
                    window.formDirty = true;
                    console.log('Design updated');
                }
        });

        unlayerEditor.addEventListener('editor:ready', function() {
    console.log('Editor is ready!');
    // Hide loading spinner, etc.
});



unlayerEditor.addEventListener('design:load', (data) => {
    console.log('Design loaded:', data);
    $('#editor-loading').hide().addClass('hidden');
});

        // Listen for design updated event
unlayerEditor.addEventListener('design:updated', function(data) {
    console.log('Design updated!', data);
    window.formDirty = true;
    console.log('Design updated');
    // $('#updateTemplateBtn').prop('disabled', false);  // Enable save button
});

// Step 2: Update button state on change:
// onDesignUpdated: function(data) {
//     window.formDirty = true;
//     $('#updateTemplateBtn').prop('disabled', false);
// }

// After a successful save:
// window.formDirty = false;
// $('#updateTemplateBtn').prop('disabled', true);


        

        const waitForUnlayer = setInterval(() => {
            if (typeof unlayer !== 'undefined' && typeof unlayerEditor !== 'undefined') {
                console.log('✅ Unlayer editor seems initialized (via polling)');
                clearInterval(waitForUnlayer);
                isEditorReady = true;
                $('#editor-loading').hide().addClass('hidden');
                showNotification('success', 'Editor Ready', 'Email editor loaded successfully');
            }
        }, 500);






        try {
            const design = JSON.parse(<?= json_encode($template['design_json']) ?>);
            console.log(design);
            unlayerEditor.loadDesign(design);
        } catch (e) {
            console.error('Error loading design:', e);
            unlayerEditor.loadBlank();
        }
        
        setTimeout(function() {
            if ($('#editor-loading').is(':visible')) {
                $('#editor-loading').hide().addClass('hidden');
            }
        }, 3000);
    }

    // Handle form submission
    $('#updateTemplateBtn').on('click', function(e) {
        e.preventDefault();
        
        if (!isEditorReady) {
            showNotification('error', 'Editor Not Ready', 'Please wait for the editor to load');
            return;
        }

        const $saveBtn = $('#updateTemplateBtn');
        const originalHtml = $saveBtn.html();
        $saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Updating...');

        unlayerEditor.exportHtml(function(data) {
            const formData = {
                name: $('#templateName').val(),
                slug: $('#templateSlug').val(),
                description: $('#templateDescription').val(),
                category_name: $('#templateCategory').val(),
                subject: $('#templateSubject').val(),
                is_active: $('#templateStatus').val(),
                html_content: data.html,
                design_json: JSON.stringify(data.design),
                <?= csrf_token() ?>: $('input[name="<?= csrf_token() ?>"]').val()
            };

            $.ajax({
                url: '<?= base_url('admin/email-templates/update/' . $template['id']) ?>',
                type: 'POST',
                data: formData,
                success: function(response) {
                    $saveBtn.prop('disabled', false).html(originalHtml);
                    
                    if (response.success) {
                        showNotification('success', 'Success', 'Template updated successfully');
                        window.formDirty = false;
                    } else {
                        showNotification('error', 'Error', response.message || 'Failed to update template');
                    }
                },
                error: function(xhr) {
                    $saveBtn.prop('disabled', false).html(originalHtml);
                    showNotification('error', 'Error', 'Failed to update template');
                }
            });
        });
    });

    // Placeholder click to copy
    // $('.placeholder-tag').on('click', function() {
    //     const placeholder = $(this).data('placeholder');
    //     if (navigator.clipboard) {
    //         navigator.clipboard.writeText(placeholder).then(function() {
    //             showNotification('success', 'Copied', `Placeholder "${placeholder}" copied to clipboard`);
    //         });
    //     }
    // });
});


    window.addEventListener('beforeunload', function (e) {
        if (window.formDirty) {
            e.preventDefault();
            e.returnValue = ''; // Modern browsers require this
            return ''; // For older compatibility
        }
    });



</script>
<?= $this->endSection() ?>
