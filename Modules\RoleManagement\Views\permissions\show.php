<div class="row">
    <div class="col-md-6">
        <h6><strong>Permission Information</strong></h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Name:</strong></td>
                <td><?= esc($permission['name']) ?></td>
            </tr>
            <tr>
                <td><strong>Description:</strong></td>
                <td><?= esc($permission['description'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Category:</strong></td>
                <td>
                    <?php if (!empty($permission['category'])): ?>
                        <span class="badge badge-info"><?= esc($permission['category']) ?></span>
                    <?php else: ?>
                        <span class="text-muted">No category</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Status:</strong></td>
                <td>
                    <?php if ($permission['is_active']): ?>
                        <span class="badge badge-success">Active</span>
                    <?php else: ?>
                        <span class="badge badge-danger">Inactive</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Created:</strong></td>
                <td><?= date('M d, Y H:i', strtotime($permission['created_at'])) ?></td>
            </tr>
            <tr>
                <td><strong>Updated:</strong></td>
                <td><?= date('M d, Y H:i', strtotime($permission['updated_at'])) ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6><strong>Assigned to Roles (<?= count($roles) ?>)</strong></h6>
        <?php if (!empty($roles)): ?>
            <div class="roles-list" style="max-height: 300px; overflow-y: auto;">
                <?php foreach ($roles as $role): ?>
                    <div class="mb-2">
                        <span class="badge badge-primary mr-1"><?= esc($role['name']) ?></span>
                        <?php if (!empty($role['description'])): ?>
                            <small class="text-muted"><?= esc($role['description']) ?></small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p class="text-muted">This permission is not assigned to any roles.</p>
        <?php endif; ?>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="btn-group">
            <a href="<?= site_url('admin/permissions/edit/' . $permission['id']) ?>" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit Permission
            </a>
        </div>
    </div>
</div>
