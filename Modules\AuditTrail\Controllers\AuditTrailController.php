<?php

namespace Modules\AuditTrail\Controllers;

use App\Controllers\AdminController;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;
use Modules\AuditTrail\Models\AuditLogModel;
use Hermawan\DataTables\DataTable;

class AuditTrailController extends AdminController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    /**
     * Display audit trail index page
     */
    public function index()
    {
        if (!hasPermission('audit.view')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to view audit trails.');
        }

        $data['title'] = 'Audit Trail';
        $data['page_title'] = 'Audit Trail Management';
        $data['stats'] = $this->auditLogModel->getStatistics();

        return view('Modules\AuditTrail\Views\audit\index', $data);
    }

    /**
     * DataTables AJAX endpoint
     */
    public function datatable()
    {
        if (!hasPermission('audit.view')) {
            return $this->response->setJSON(['error' => 'Permission denied']);
        }

        $builder = $this->auditLogModel->db->table('audit_logs a')
                                          ->select('a.id, a.user_id, a.event, a.auditable_type, a.auditable_id, a.old_values, a.new_values, a.ip_address, a.user_agent, a.url, a.method, a.created_at,u.username, u.first_name, u.last_name')
                                          ->join('users u', 'u.id = a.user_id', 'left');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex')
            ->add('user_info', function($row) {
                if ($row->username) {
                    $name = trim($row->first_name . ' ' . $row->last_name);
                    return '<div><strong>' . esc($row->username) . '</strong>' .
                           ($name ? '<br><small class="text-muted">' . esc($name) . '</small>' : '') . '</div>';
                }
                return '<span class="text-muted">System</span>';
            })
            ->add('entity_info', function($row) {
                $info = '<span class="badge badge-info">' . esc($row->auditable_type ?? 'N/A') . '</span>';
                if ($row->auditable_id) {
                    $info .= '<br><small class="text-muted">ID: ' . $row->auditable_id . '</small>';
                }
                return $info;
            })
            ->add('event_formatted', function($row) {
                $eventColors = [
                    'create' => 'success',
                    'update' => 'warning',
                    'delete' => 'danger',
                    'login' => 'info',
                    'logout' => 'secondary',
                    'view' => 'light'
                ];

                $event = strtolower($row->event);
                $color = $eventColors[$event] ?? 'primary';

                return '<span class="badge badge-' . $color . '">' . esc($row->event) . '</span>';
            })
            ->add('created_at_formatted', function($row) {
                return date('M d, Y H:i:s', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                if (hasPermission('audit.view')) {
                    $actions .= '<button type="button" class="btn btn-info btn-sm view-details"
                                        data-id="' . $row->id . '"
                                        title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>';
                }
                return $actions;
            })
            ->toJson(true);
    }

    /**
     * Get audit trail details for AJAX
     */
    public function show($id)
    {
        if (!hasPermission('audit.view')) {
            return $this->response->setJSON(['error' => 'Permission denied']);
        }

        $auditTrail = $this->auditLogModel->db->table('audit_logs a')
                                             ->select('a.*, u.username, u.first_name, u.last_name')
                                             ->join('users u', 'u.id = a.user_id', 'left')
                                             ->where('a.id', $id)
                                             ->get()->getRowArray();

        if (!$auditTrail) {
            return $this->response->setJSON(['error' => 'Audit trail entry not found']);
        }

        // Parse JSON values
        $auditTrail['old_values_parsed'] = $auditTrail['old_values'] ? json_decode($auditTrail['old_values'], true) : null;
        $auditTrail['new_values_parsed'] = $auditTrail['new_values'] ? json_decode($auditTrail['new_values'], true) : null;

        return $this->response->setJSON(['success' => true, 'data' => $auditTrail]);
    }

    /**
     * Get statistics for AJAX
     */
    public function stats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->auditLogModel->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Clean old audit logs
     */
    public function cleanOldLogs()
    {
        if (!hasPermission('audit.purge')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $daysToKeep = $this->request->getPost('days') ?? 365;

        try {
            $deleted = $this->auditLogModel->cleanOldLogs($daysToKeep);
            return $this->response->setJSON([
                'success' => true,
                'message' => "Cleaned {$deleted} old audit trail entries"
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to clean old logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export audit trails
     */
    public function export()
    {
        if (!hasPermission('audit.export')) {
            return redirect()->back()->with('error', 'You do not have permission to export audit trails.');
        }

        $format = $this->request->getGet('format') ?? 'csv';
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        $builder = $this->auditLogModel->db->table('audit_logs a')
                                      ->select('a.id, a.user_id, a.event, a.auditable_type, a.auditable_id, a.old_values, a.new_values, a.ip_address, a.user_agent, a.url, a.method, a.created_at,u.username, u.first_name, u.last_name')
                                      ->join('users u', 'u.id = a.user_id', 'left')
                                      ->orderBy('a.created_at', 'DESC');

        if ($dateFrom) {
            $builder->where('a.created_at >=', $dateFrom . ' 00:00:00');
        }
        if ($dateTo) {
            $builder->where('a.created_at <=', $dateTo . ' 23:59:59');
        }

        $logs = $builder->get()->getResultArray();

        if ($format === 'csv') {
            return $this->exportToCsv($logs);
        }

        return redirect()->back()->with('error', 'Invalid export format.');
    }

    /**
     * Export logs to CSV
     */
    private function exportToCsv($logs)
    {
        $filename = 'audit_trail_' . date('Y-m-d_H-i-s') . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, [
            'ID', 'Date/Time', 'User', 'Event', 'Entity Type',
            'Entity ID', 'IP Address', 'URL', 'Method', 'User Agent'
        ]);

        foreach ($logs as $log) {
            $user = $log['username'] ?? 'System';
            if ($log['first_name'] || $log['last_name']) {
                $user .= ' (' . trim($log['first_name'] . ' ' . $log['last_name']) . ')';
            }

            fputcsv($output, [
                $log['id'],
                $log['created_at'],
                $user,
                $log['event'],
                $log['auditable_type'] ?? '',
                $log['auditable_id'] ?? '',
                $log['ip_address'] ?? '',
                $log['url'] ?? '',
                $log['method'] ?? '',
                $log['user_agent'] ?? ''
            ]);
        }

        fclose($output);
        exit;
    }
}
