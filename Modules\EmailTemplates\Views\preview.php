<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preview: <?= esc($template['name']) ?></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f4f6f9;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .preview-header {
            /* background: linear-gradient(135deg, #000000ff 0%, #764ba2 100%); */
            color: black;
            padding: 20px;
            text-align: center;
        }
        
        .preview-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .preview-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .email-subject {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .email-subject strong {
            color: #495057;
            font-size: 16px;
        }
        
        .email-content {
            padding: 0;
            background: #ffffff;
        }
        
        .preview-actions {
            background: #f8f9fa;
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .template-info {
            background: #e9ecef;
            padding: 10px 20px;
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .preview-header h1 {
                font-size: 20px;
            }
            
            .btn {
                display: block;
                margin: 5px 0;
                width: 100%;
            }
        }
        
        /* Email content styling */
        .email-content table {
            width: 100%;
        }
        
        .email-content img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- Preview Header -->
        <div class="preview-header">
            <h1>Email Preview</h1>
            <p>Template: <?= esc($template['name']) ?></p>
        </div>
        
        <!-- Email Subject -->
        <div class="email-subject">
            <strong>Subject:</strong> <?= esc($subject) ?>
        </div>
        
        <!-- Email Content -->
        <div class="email-content">
            <?= $html_content ?>
        </div>
        
        <!-- Template Info -->
        <div class="template-info">
            Template ID: <?= $template['id'] ?> | 
            Slug: <?= esc($template['slug']) ?> | 
            Category: <?= esc($template['category_name'] ?? 'Uncategorized') ?> |
            Last Updated: <?= date('M j, Y g:i A', strtotime($template['updated_at'])) ?>
        </div>
        
        <!-- Preview Actions -->
        <div class="preview-actions">
            <button class="btn btn-primary" onclick="window.print()">
                🖨️ Print Preview
            </button>
            <button class="btn btn-success" onclick="openInNewWindow()">
                🔗 Open in New Window
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                ❌ Close Preview
            </button>
        </div>
    </div>

    <script>
        function openInNewWindow() {
            const emailContent = document.querySelector('.email-content').innerHTML;
            const subject = <?= json_encode($subject) ?>;
            
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${subject}</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            background-color: #f4f4f4;
                            font-family: Arial, sans-serif;
                        }
                        table {
                            width: 100%;
                        }
                        img {
                            max-width: 100%;
                            height: auto;
                        }
                    </style>
                </head>
                <body>
                    ${emailContent}
                </body>
                </html>
            `);
            newWindow.document.close();
        }
        
        // Auto-focus for better UX
        document.addEventListener('DOMContentLoaded', function() {
            // Add some interactive features
            console.log('Email preview loaded successfully');
            
            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'p':
                            e.preventDefault();
                            window.print();
                            break;
                        case 'w':
                            e.preventDefault();
                            window.close();
                            break;
                    }
                }
                
                if (e.key === 'Escape') {
                    window.close();
                }
            });
        });
        
        // Add some visual feedback
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>
