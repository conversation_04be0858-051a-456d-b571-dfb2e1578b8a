<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/email-queue') ?>">Email Queue</a></li>
<li class="breadcrumb-item active">List</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?= $count['total'] ?></h3>
                <p>Total Emails</p>
            </div>
            <div class="icon">
                <i class="fas fa-envelope"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?= $count['pending'] ?></h3>
                <p>Pending Emails</p>
            </div>
            <div class="icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?= $count['sent'] ?></h3>
                <p>Sent Emails</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?= $count['failed'] ?></h3>
                <p>Failed Emails</p>
            </div>
            <div class="icon">
                <i class="fas fa-times-circle"></i>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Email Queue Management</h3>
        <div class="card-tools">
            <button type="button" class="btn btn-primary btn-sm" id="run-queue">
                <i class="fas fa-play"></i> Run Queue
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Flash Messages -->
        <?php if (session()->getFlashdata('message')): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-check"></i> <?= session()->getFlashdata('message') ?>
            </div>
        <?php endif; ?>
        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-ban"></i> <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-info btn-sm" id="bulk-resend" disabled>
                        <i class="fas fa-paper-plane"></i> Resend Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-retry" disabled>
                        <i class="fas fa-redo"></i> Retry Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <span class="ml-2 text-muted" id="selected-count">0 items selected</span>
            </div>
        </div>

        <!-- DataTable -->
        <div class="table-responsive">
            <table id="emailQueueTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th style="width: 30px">
                            <input type="checkbox" id="select-all">
                        </th>
                        <th>To Email</th>
                        <th>Subject</th>
                        <th>Template</th>
                        <th>Status</th>
                        <th>Attempts</th>
                        <th>Error</th>
                        <th>Created At</th>
                        <th>Sent At</th>
                        <th style="width: 120px">Actions</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<?= $this->section('script') ?>
 <!-- DataTables  & Plugins -->
<script src="<?= base_url("assets/plugins/datatables/jquery.dataTables.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/jszip/jszip.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/pdfmake.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/vfs_fonts.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js") ?>"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#emailQueueTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= site_url('admin/email-queue/datatable') ?>',
            type: 'POST'
        },
        columns: [
            { data: 'checkbox', orderable: false, searchable: false },
            { data: 'to_email', name: 'to_email' },
            { data: 'subject', name: 'subject' },
            { data: 'template', name: 'template' },
            { data: 'status', name: 'status' },
            { data: 'attempts', name: 'attempts' },
            { data: 'error', name: 'error' },
            { data: 'created_at', name: 'created_at' },
            { data: 'sent_at', name: 'sent_at' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        order: [[7, 'desc']], // Order by created_at desc
        pageLength: 25,
        responsive: true,
        language: {
            processing: '<i class="fas fa-spinner fa-spin"></i> Loading...'
        }
    });

    // Select all checkbox
    $('#select-all').on('click', function() {
        var checked = this.checked;
        $('.select-item').prop('checked', checked);
        updateBulkButtons();
    });

    // Individual checkbox
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select all checkbox
        var totalItems = $('.select-item').length;
        var checkedItems = $('.select-item:checked').length;
        $('#select-all').prop('indeterminate', checkedItems > 0 && checkedItems < totalItems);
        $('#select-all').prop('checked', checkedItems === totalItems);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedItems = $('.select-item:checked').length;
        var bulkButtons = $('#bulk-resend, #bulk-retry, #bulk-delete');

        if (selectedItems > 0) {
            bulkButtons.prop('disabled', false);
            $('#selected-count').text(selectedItems + ' item' + (selectedItems > 1 ? 's' : '') + ' selected');
        } else {
            bulkButtons.prop('disabled', true);
            $('#selected-count').text('0 items selected');
        }
    }

    // Run Queue
    $('#run-queue').on('click', function() {
        Swal.fire({
            title: 'Run Email Queue',
            text: 'This will process pending emails in the queue. Continue?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, run queue!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/email-queue/run-queue') ?>';
            }
        });
    });

    // Bulk resend
    $('#bulk-resend').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to resend', 'warning');
            return;
        }

        Swal.fire({
            title: 'Resend Emails',
            text: 'Are you sure you want to resend ' + selectedIds.length + ' selected email(s)?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#17a2b8',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, resend!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/email-queue/bulk-resend') ?>', selectedIds, 'resend');
            }
        });
    });

    // Bulk retry
    $('#bulk-retry').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to retry', 'warning');
            return;
        }

        Swal.fire({
            title: 'Retry Emails',
            text: 'Are you sure you want to requeue ' + selectedIds.length + ' selected email(s) for retry?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, retry!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/email-queue/bulk-retry') ?>', selectedIds, 'retry');
            }
        });
    });

    // Bulk delete
    $('#bulk-delete').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to delete', 'warning');
            return;
        }

        Swal.fire({
            title: 'Delete Emails',
            text: 'Are you sure you want to delete ' + selectedIds.length + ' selected email(s)? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/email-queue/bulk-delete') ?>', selectedIds, 'delete');
            }
        });
    });

    // Individual actions
    $(document).on('click', '.btn-resend-now', function() {
        var emailId = $(this).data('id');

        Swal.fire({
            title: 'Resend Email',
            text: 'Are you sure you want to resend this email now?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#17a2b8',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, resend!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/email-queue/retry-now/') ?>/' + emailId;
            }
        });
    });

    $(document).on('click', '.btn-requeue', function() {
        var emailId = $(this).data('id');

        Swal.fire({
            title: 'Requeue Email',
            text: 'Are you sure you want to requeue this email for retry?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, requeue!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/email-queue/retry-later/') ?>/' + emailId;
            }
        });
    });

    $(document).on('click', '.btn-delete', function() {
        var emailId = $(this).data('id');

        Swal.fire({
            title: 'Delete Email',
            text: 'Are you sure you want to delete this email? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/email-queue/delete/') ?>/' + emailId;
            }
        });
    });

    // Perform bulk action
    function performBulkAction(url, ids, action) {
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                ids: ids,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            beforeSend: function() {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while we ' + action + ' the selected items.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire('Success!', response.message, 'success');
                    table.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while processing your request.', 'error');
            }
        });
    }

    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>
<?= $this->endSection() ?>

</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
    $(document).ready(function() {
        $('#checkAll').click(function() {
            $('input[name="ids[]"]').prop('checked', this.checked);
        });
    });
</script>
<?= $this->endSection() ?>