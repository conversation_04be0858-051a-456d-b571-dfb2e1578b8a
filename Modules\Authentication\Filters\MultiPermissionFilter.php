<?php

namespace Modules\Authentication\Filters;

use CodeIgniter\HTTP\RequestInterface;
use <PERSON><PERSON>gniter\HTTP\ResponseInterface;
use <PERSON>Igniter\Filters\FilterInterface;
use App\Services\PermissionService;

/**
 * MultiPermissionFilter - Check if user has any of the specified permissions
 * 
 * Usage in routes:
 * $routes->get('admin/users', 'UserController::index', ['filter' => 'multipermission:manage_users,view_users']);
 */
class MultiPermissionFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $userId = session()->get('user_id');

        if (!$userId) {
            return redirect()->to('/auth/login')->with('error', 'Access denied. Please login first.');
        }

        // Parse the required permissions from arguments
        $requiredPermissions = [];
        if ($arguments) {
            // Arguments come as a single string with comma-separated permissions
            $requiredPermissions = array_map('trim', explode(',', $arguments[0]));
        }

        if (empty($requiredPermissions)) {
            return redirect()->to('/')->with('error', 'No permissions specified for access control.');
        }

        // Use the PermissionService to check if user has any of the required permissions
        $permissionService = new PermissionService();

        if (!$permissionService->userHasAnyPermission($userId, $requiredPermissions)) {
            $permissionsList = implode(', ', $requiredPermissions);
            return redirect()->to('/')->with('error', "You need one of the following permissions to access this page: {$permissionsList}");
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Optional post-processing
    }
}
