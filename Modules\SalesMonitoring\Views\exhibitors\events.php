<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exhibitor Portal | Trade Expo</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
</head>
<body class="">
    <div class="page">
        <!-- Header -->
        <header class="navbar navbar-expand-md navbar-dark d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href=".">
                        <h2>Sales Monitoring</h2>
                    </a>
                </h1>
                <div class="navbar-nav flex-row order-md-last">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                            <span class="avatar avatar-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="icon icon-tabler icon-tabler-user">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0" />
                                    <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                                </svg>
                            </span>
                            <div class="d-none d-xl-block ps-2">
                                <div><?= session()->get('company_name') ?? 'User'; ?></div>
                                <div class="mt-1 small text-muted">Trade Expo</div>
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <!-- <a href="#" class="dropdown-item">Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <div class="dropdown-divider"></div> -->
                            <a href="<?= route_to('cinauth.logout');?>" class="dropdown-item">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page content -->
        <div class="page-wrapper">
            <div class="container-xl">
                <!-- Page title -->
                <div class="page-header d-print-none">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="page-title">
                                Exhibitor Sales
                            </h2>
                            <div class="text-muted mt-1">Welcome back to your trade expo dashboard</div>
                        </div>
                    </div>
                </div>

                <!-- Events List -->
                <div class="row row-cards">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Your Events</h3>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    
                                     <?php foreach($events as $event):?>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="h3 mb-3"><?= $event->description;?></div>
                                                    <div class="text-muted mb-3">
                                                        <i data-feather="square" class="icon me-2"></i>
                                                        <?= $event->theme;?>
                                                    </div>
                                                    <div class="text-muted mb-3">
                                                        <i data-feather="calendar" class="icon me-2"></i>
                                                        <?= $event->formatted_date;?>
                                                    </div>
                                                    <div class="text-muted mb-3">
                                                        <i data-feather="map-pin" class="icon me-2"></i>
                                                        World Trade Center
                                                    </div>
                                                    <div class="d-flex mt-4">
                                                        <a href="<?= site_url('exhibitor/event/'.$event->event_id.'/sales');?>" class="btn btn-primary me-2 <?= ($event->enable_during_event_sales == 0)? 'disabled':'';?>">
                                                            <i data-feather="plus" class="icon me-1"></i>
                                                            Add Sales
                                                        </a>
                                                        <a href="<?= site_url('exhibitor/event/'.$event->event_id.'/sales/updates');?>" class="btn btn-outline-primary <?= ($event->enable_after_event_sales == 0)? 'disabled':'';?>">
                                                            <i data-feather="refresh-cw" class="icon me-1"></i>
                                                            Update Sales
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    <?php endforeach;?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <!-- <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="h1 m-0">$152,300</div>
                                <div class="text-muted mb-3">TOTAL SALES ACROSS ALL EVENTS</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="h1 m-0">42</div>
                                <div class="text-muted mb-3">TOTAL BUYERS MET</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="h1 m-0">156</div>
                                <div class="text-muted mb-3">TOTAL INQUIRIES</div>
                            </div>
                        </div>
                    </div>
                </div> -->

            </div>
        </div>
    </div>

    <script>
        feather.replace();
    </script>
</body>
</html>