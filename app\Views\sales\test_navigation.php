<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Top Navigation Test - Sales Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</head>
<body>
    <div class="page">
        <!-- Test Top Navigation with Dynamic Menu -->
        <?= renderTablerTopNavbar([
            'brand_text' => 'Sales Dashboard',
            'brand_url' => base_url('sales'),
            'brand_image' => 'https://tabler.io/static/logo.svg',
            'user_name' => 'Test User',
            'user_role' => 'Sales Manager',
            'user_avatar' => 'https://ui-avatars.com/api/?name=Test+User&background=206bc4&color=fff'
        ]) ?>

        <!-- Page wrapper -->
        <div class="page-wrapper">
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <div class="page-pretitle">Navigation Test</div>
                            <h2 class="page-title">Top Navigation Menu Test</h2>
                            <div class="text-muted mt-1">Testing dynamic top navigation menu system</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    <div class="row row-deck row-cards">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Top Navigation Menu Test</h3>
                                </div>
                                <div class="card-body">
                                    <h4>Menu System Status</h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5>Helper Functions</h5>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    renderTablerTopNavMenu()
                                                    <span class="badge bg-<?= function_exists('renderTablerTopNavMenu') ? 'green' : 'red' ?>">
                                                        <?= function_exists('renderTablerTopNavMenu') ? 'Available' : 'Missing' ?>
                                                    </span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    renderTablerTopNavbar()
                                                    <span class="badge bg-<?= function_exists('renderTablerTopNavbar') ? 'green' : 'red' ?>">
                                                        <?= function_exists('renderTablerTopNavbar') ? 'Available' : 'Missing' ?>
                                                    </span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    buildTablerTopNavHtml()
                                                    <span class="badge bg-<?= function_exists('buildTablerTopNavHtml') ? 'green' : 'red' ?>">
                                                        <?= function_exists('buildTablerTopNavHtml') ? 'Available' : 'Missing' ?>
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Menu Service</h5>
                                            <?php
                                            try {
                                                $menuService = new \Modules\MenuManagement\Services\MenuService();
                                                $topMenus = $menuService->getTopNavigationMenus();
                                                $menuCount = count($topMenus);
                                                $serviceStatus = 'Available';
                                                $serviceClass = 'green';
                                            } catch (Exception $e) {
                                                $menuCount = 0;
                                                $serviceStatus = 'Error: ' . $e->getMessage();
                                                $serviceClass = 'red';
                                            }
                                            ?>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    MenuService
                                                    <span class="badge bg-<?= $serviceClass ?>"><?= $serviceStatus ?></span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Top Navigation Menus
                                                    <span class="badge bg-blue"><?= $menuCount ?> items</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <hr>

                                    <h4>Raw Menu Data</h4>
                                    <?php if (isset($topMenus) && !empty($topMenus)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Label</th>
                                                        <th>URL</th>
                                                        <th>Icon</th>
                                                        <th>Position</th>
                                                        <th>Style</th>
                                                        <th>Parent</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($topMenus as $menu): ?>
                                                        <tr>
                                                            <td><?= esc($menu['label']) ?></td>
                                                            <td><code><?= esc($menu['url']) ?></code></td>
                                                            <td><?= $menu['icon'] ? '<i class="' . $menu['icon'] . '"></i>' : '-' ?></td>
                                                            <td><span class="badge bg-blue"><?= $menu['menu_position'] ?? 'sidebar' ?></span></td>
                                                            <td><span class="badge bg-green"><?= $menu['display_style'] ?? 'default' ?></span></td>
                                                            <td><?= $menu['parent_id'] ? 'ID: ' . $menu['parent_id'] : 'Root' ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <h4 class="alert-title">No Top Navigation Menus Found</h4>
                                            <div class="text-muted">
                                                No menus with position 'top' or 'both' were found in the database.
                                                You may need to:
                                                <ul>
                                                    <li>Run the database migration to add menu_position and display_style fields</li>
                                                    <li>Create some top navigation menus through the admin interface</li>
                                                    <li>Run the TopNavigationMenuSeeder</li>
                                                </ul>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <hr>

                                    <h4>Rendered Menu HTML</h4>
                                    <div class="card">
                                        <div class="card-body">
                                            <pre><code><?= esc(renderTablerTopNavMenu()) ?></code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
