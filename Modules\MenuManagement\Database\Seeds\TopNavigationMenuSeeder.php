<?php

namespace Modules\MenuManagement\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TopNavigationMenuSeeder extends Seeder
{
    public function run()
    {
        $menuModel = new \Modules\MenuManagement\Models\MenuModel();
        
        // Sample top navigation menus for sales dashboard
        $topMenus = [
            [
                'label' => 'Dashboard',
                'url' => 'sales/dashboard',
                'icon' => 'ti ti-home',
                'css_class' => '',
                'target' => '_self',
                'parent_id' => null,
                'sort_order' => 1,
                'active' => 1,
                'menu_position' => 'top',
                'display_style' => 'default',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'label' => 'Sales',
                'url' => '',
                'icon' => 'ti ti-currency-dollar',
                'css_class' => '',
                'target' => '_self',
                'parent_id' => null,
                'sort_order' => 2,
                'active' => 1,
                'menu_position' => 'top',
                'display_style' => 'dropdown',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'label' => 'Reports',
                'url' => '',
                'icon' => 'ti ti-chart-bar',
                'css_class' => '',
                'target' => '_self',
                'parent_id' => null,
                'sort_order' => 3,
                'active' => 1,
                'menu_position' => 'top',
                'display_style' => 'dropdown',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'label' => 'Settings',
                'url' => 'sales/settings',
                'icon' => 'ti ti-settings',
                'css_class' => '',
                'target' => '_self',
                'parent_id' => null,
                'sort_order' => 4,
                'active' => 1,
                'menu_position' => 'top',
                'display_style' => 'default',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert parent menus first
        foreach ($topMenus as $menu) {
            $menuId = $menuModel->insert($menu);
            
            // Add submenus for Sales
            if ($menu['label'] === 'Sales' && $menuId) {
                $salesSubmenus = [
                    [
                        'label' => 'New Sale',
                        'url' => 'sales/create',
                        'icon' => 'ti ti-plus',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 1,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'label' => 'Sales List',
                        'url' => 'sales/list',
                        'icon' => 'ti ti-list',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 2,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'label' => 'Export Sales',
                        'url' => 'sales/export',
                        'icon' => 'ti ti-world',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 3,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'label' => 'Domestic Sales',
                        'url' => 'sales/domestic',
                        'icon' => 'ti ti-home',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 4,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ];
                
                foreach ($salesSubmenus as $submenu) {
                    $menuModel->insert($submenu);
                }
            }
            
            // Add submenus for Reports
            if ($menu['label'] === 'Reports' && $menuId) {
                $reportsSubmenus = [
                    [
                        'label' => 'Sales Report',
                        'url' => 'reports/sales',
                        'icon' => 'ti ti-chart-line',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 1,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'label' => 'Performance Report',
                        'url' => 'reports/performance',
                        'icon' => 'ti ti-trending-up',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 2,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'label' => 'Analytics',
                        'url' => 'reports/analytics',
                        'icon' => 'ti ti-chart-pie',
                        'css_class' => '',
                        'target' => '_self',
                        'parent_id' => $menuId,
                        'sort_order' => 3,
                        'active' => 1,
                        'menu_position' => 'top',
                        'display_style' => 'default',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ];
                
                foreach ($reportsSubmenus as $submenu) {
                    $menuModel->insert($submenu);
                }
            }
        }
        
        echo "Top navigation menus seeded successfully!\n";
    }
}
