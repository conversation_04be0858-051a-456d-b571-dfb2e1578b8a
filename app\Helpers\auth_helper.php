<?php

if (!function_exists('hasPermission')) {
    /**
     * Check if the current logged-in user has a specific permission
     *
     * @param string $permissionName The permission name to check
     * @return bool True if user has the permission, false otherwise
     */
    function hasPermission(string $permissionName): bool
    {
        $session = \Config\Services::session();
        $userId = $session->get('user_id');

        if (!$userId) {
            return false; // Not logged in
        }

        $permissionService = new \Modules\Authentication\Services\PermissionService();
        return $permissionService->userHasPermission($userId, $permissionName);
    }
}

if (!function_exists('hasAnyPermission')) {
    /**
     * Check if the current logged-in user has any of the specified permissions
     *
     * @param array $permissionNames Array of permission names to check
     * @return bool True if user has any of the permissions, false otherwise
     */
    function hasAnyPermission(array $permissionNames): bool
    {
        $session = \Config\Services::session();
        $userId = $session->get('user_id');

        if (!$userId) {
            return false; // Not logged in
        }

        $permissionService = new \Modules\Authentication\Services\PermissionService();
        return $permissionService->userHasAnyPermission($userId, $permissionNames);
    }
}

if (!function_exists('hasAllPermissions')) {
    /**
     * Check if the current logged-in user has all of the specified permissions
     *
     * @param array $permissionNames Array of permission names to check
     * @return bool True if user has all permissions, false otherwise
     */
    function hasAllPermissions(array $permissionNames): bool
    {
        $session = \Config\Services::session();
        $userId = $session->get('user_id');

        if (!$userId) {
            return false; // Not logged in
        }

        $permissionService = new \Modules\Authentication\Services\PermissionService();
        return $permissionService->userHasAllPermissions($userId, $permissionNames);
    }
}

if (!function_exists('hasRole')) {
    /**
     * Check if the current logged-in user has a specific role
     *
     * @param string $roleName The role name to check
     * @return bool True if user has the role, false otherwise
     */
    function hasRole(string $roleName): bool
    {
        $session = \Config\Services::session();
        $userRoles = $session->get('user_roles');

        if (!$userRoles) {
            return false; // Not logged in or roles not set
        }

        return in_array($roleName, $userRoles);
    }
}

if (!function_exists('hasAnyRole')) {
    /**
     * Check if the current logged-in user has any of the specified roles
     *
     * @param array $roleNames Array of role names to check
     * @return bool True if user has any of the roles, false otherwise
     */
    function hasAnyRole(array $roleNames): bool
    {
        $session = \Config\Services::session();
        $userRoles = $session->get('user_roles');

        if (!$userRoles) {
            return false; // Not logged in or roles not set
        }

        return !empty(array_intersect($userRoles, $roleNames));
    }
}

if (!function_exists('getCurrentUserId')) {
    /**
     * Get the current logged-in user's ID
     *
     * @return int|null User ID or null if not logged in
     */
    function getCurrentUserId(): ?int
    {
        $session = \Config\Services::session();
        return $session->get('user_id');
    }
}

if (!function_exists('getCurrentUserRoles')) {
    /**
     * Get the current logged-in user's roles
     *
     * @return array Array of role names
     */
    function getCurrentUserRoles(): array
    {
        $session = \Config\Services::session();
        return $session->get('user_roles') ?? [];
    }
}

if (!function_exists('getCurrentUserPermissions')) {
    /**
     * Get all permissions for the current logged-in user
     *
     * @return array Array of permission names
     */
    function getCurrentUserPermissions(): array
    {
        $session = \Config\Services::session();
        $userId = $session->get('user_id');

        if (!$userId) {
            return []; // Not logged in
        }

        $permissionService = new \Modules\Authentication\Services\PermissionService();
        return $permissionService->getPermissionNamesByUserId($userId);
    }
}

if (!function_exists('isSuperAdmin')) {
    /**
     * Check if the current logged-in user is a super admin
     *
     * @return bool True if user is a super admin, false otherwise
     */
    function isSuperAdmin(): bool
    {
        $session = \Config\Services::session();
        $userId = $session->get('user_id');

        if (!$userId) {
            return false; // Not logged in
        }

        $permissionService = new \Modules\Authentication\Services\PermissionService();
        return $permissionService->isSuperAdmin($userId);
    }
}

if (!function_exists('canAccess')) {
    /**
     * Check if user can access a resource based on permission or role
     * This is a convenience function that checks both permissions and roles
     *
     * @param string|array $permissionOrRole Permission name, role name, or array of either
     * @param string $type 'permission', 'role', or 'auto' (default: 'auto' - tries permission first, then role)
     * @return bool True if user has access, false otherwise
     */
    function canAccess($permissionOrRole, string $type = 'auto'): bool
    {
        if (is_array($permissionOrRole)) {
            // Handle array of permissions or roles
            if ($type === 'permission' || $type === 'auto') {
                if (hasAnyPermission($permissionOrRole)) {
                    return true;
                }
            }
            if ($type === 'role' || ($type === 'auto' && !hasAnyPermission($permissionOrRole))) {
                return hasAnyRole($permissionOrRole);
            }
            return false;
        }

        // Handle single permission or role
        if ($type === 'permission' || $type === 'auto') {
            if (hasPermission($permissionOrRole)) {
                return true;
            }
        }
        if ($type === 'role' || ($type === 'auto' && !hasPermission($permissionOrRole))) {
            return hasRole($permissionOrRole);
        }
        return false;
    }
}

if (!function_exists('requirePermission')) {
    /**
     * Require a specific permission or redirect to access denied page
     *
     * @param string $permissionName The permission name required
     * @param string $redirectUrl URL to redirect to if access denied (default: '/')
     * @param string $message Error message to show (default: 'Access denied')
     * @return void
     */
    function requirePermission(string $permissionName, string $redirectUrl = '/', string $message = 'Access denied'): void
    {
        if (!hasPermission($permissionName)) {
            session()->setFlashdata('error', $message);
            header('Location: ' . $redirectUrl);
            exit;
        }
    }
}

if (!function_exists('requireRole')) {
    /**
     * Require a specific role or redirect to access denied page
     *
     * @param string $roleName The role name required
     * @param string $redirectUrl URL to redirect to if access denied (default: '/')
     * @param string $message Error message to show (default: 'Access denied')
     * @return void
     */
    function requireRole(string $roleName, string $redirectUrl = '/', string $message = 'Access denied'): void
    {
        if (!hasRole($roleName)) {
            session()->setFlashdata('error', $message);
            header('Location: ' . $redirectUrl);
            exit;
        }
    }
}

if (!function_exists('requireLogin')) {
    /**
     * Require user to be logged in or redirect to login page
     *
     * @param string $loginUrl URL to redirect to for login (default: '/auth/login')
     * @param string $message Message to show (default: 'Please login first')
     * @return void
     */
    function requireLogin(string $loginUrl = '/auth/login', string $message = 'Please login first'): void
    {
        if (!getCurrentUserId()) {
            session()->setFlashdata('warning', $message);
            header('Location: ' . $loginUrl);
            exit;
        }
    }


    
if (!function_exists('honeypot_field')) {
    function honeypot_field() {
        $honeypot = config('Honeypot');
        $template = str_replace('{label}', $honeypot->label, $honeypot->template);
        $template = str_replace('{name}', $honeypot->name, $template);
        $container = str_replace('{template}', $template, $honeypot->container);
        $container = str_replace('{id}', $honeypot->containerId, $container);
        
        return $container;
    }
}



}