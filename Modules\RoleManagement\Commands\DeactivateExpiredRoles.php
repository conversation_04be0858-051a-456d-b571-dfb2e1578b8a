<?php

namespace Modules\RoleManagement\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Modules\RoleManagement\Models\UserRoleModel;

/**
 * Command to deactivate expired user roles
 * 
 * Usage:
 * php spark roles:deactivate-expired
 * 
 * This command should be run regularly (e.g., daily) via cron job:
 * 0 2 * * * cd /path/to/your/app && php spark roles:deactivate-expired
 */
class DeactivateExpiredRoles extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Roles';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'roles:deactivate-expired';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Deactivate expired user roles automatically';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'roles:deactivate-expired [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--dry-run' => 'Show what would be deactivated without making changes',
        '--force'   => 'Force deactivation without confirmation',
        '--verbose' => 'Show detailed output',
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $dryRun = CLI::getOption('dry-run');
        $force = CLI::getOption('force');
        $verbose = CLI::getOption('verbose');

        CLI::write('Checking for expired user roles...', 'yellow');

        try {
            $userRoleModel = new UserRoleModel();

            // Get expired roles that are still active
            $expiredRoles = $this->getExpiredActiveRoles($userRoleModel);

            if (empty($expiredRoles)) {
                CLI::write('No expired roles found that need deactivation.', 'green');
                return;
            }

            $count = count($expiredRoles);
            CLI::write("Found {$count} expired role(s) that need deactivation:", 'yellow');

            if ($verbose || $dryRun) {
                $this->displayExpiredRoles($expiredRoles);
            }

            if ($dryRun) {
                CLI::write('DRY RUN: No changes were made.', 'blue');
                return;
            }

            // Confirm action unless forced
            if (!$force) {
                $confirm = CLI::prompt("Deactivate {$count} expired role(s)?", ['y', 'n']);
                if ($confirm !== 'y') {
                    CLI::write('Operation cancelled.', 'yellow');
                    return;
                }
            }

            // Deactivate expired roles
            $deactivatedCount = $userRoleModel->deactivateExpiredRoles();

            if ($deactivatedCount > 0) {
                CLI::write("Successfully deactivated {$deactivatedCount} expired role(s).", 'green');
                
                // Log the action
                log_message('info', "Deactivated {$deactivatedCount} expired user roles via CLI command.");
                
                // Check for users who might now have no active roles
                $this->checkUsersWithoutActiveRoles($userRoleModel, $verbose);
            } else {
                CLI::write('No roles were deactivated. They may have been processed already.', 'yellow');
            }

        } catch (\Exception $e) {
            CLI::write('Error: ' . $e->getMessage(), 'red');
            log_message('error', 'Failed to deactivate expired roles: ' . $e->getMessage());
            return EXIT_ERROR;
        }
    }

    /**
     * Get expired roles that are still active
     *
     * @param UserRoleModel $userRoleModel
     * @return array
     */
    private function getExpiredActiveRoles(UserRoleModel $userRoleModel): array
    {
        return $userRoleModel->db->table('user_roles ur')
                                ->select('ur.*, u.username, r.name as role_name')
                                ->join('users u', 'u.id = ur.user_id')
                                ->join('roles r', 'r.id = ur.role_id')
                                ->where('ur.expires_at IS NOT NULL')
                                ->where('ur.expires_at <= NOW()')
                                ->where('ur.is_active', 1)
                                ->orderBy('ur.expires_at', 'ASC')
                                ->get()
                                ->getResultArray();
    }

    /**
     * Display expired roles in a formatted table
     *
     * @param array $expiredRoles
     */
    private function displayExpiredRoles(array $expiredRoles): void
    {
        $table = [];
        $table[] = ['User ID', 'Username', 'Role', 'Expired Date', 'Days Expired'];
        $table[] = ['-------', '--------', '----', '------------', '------------'];

        foreach ($expiredRoles as $role) {
            $expiredDate = strtotime($role['expires_at']);
            $daysExpired = floor((time() - $expiredDate) / (60 * 60 * 24));
            
            $table[] = [
                $role['user_id'],
                $role['username'],
                $role['role_name'],
                date('Y-m-d H:i', $expiredDate),
                $daysExpired . ' days'
            ];
        }

        CLI::table($table);
    }

    /**
     * Check for users who might have no active roles after deactivation
     *
     * @param UserRoleModel $userRoleModel
     * @param bool $verbose
     */
    private function checkUsersWithoutActiveRoles(UserRoleModel $userRoleModel, bool $verbose): void
    {
        // Find users with no active, non-expired roles
        $usersWithoutRoles = $userRoleModel->db->table('users u')
                                              ->select('u.id, u.username, u.email')
                                              ->where('u.is_active', 1)
                                              ->where('u.id NOT IN (
                                                  SELECT DISTINCT ur.user_id 
                                                  FROM user_roles ur 
                                                  WHERE ur.is_active = 1 
                                                  AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
                                              )')
                                              ->get()
                                              ->getResultArray();

        if (!empty($usersWithoutRoles)) {
            $count = count($usersWithoutRoles);
            CLI::write("WARNING: {$count} active user(s) now have no active roles:", 'red');
            
            if ($verbose) {
                foreach ($usersWithoutRoles as $user) {
                    CLI::write("  - {$user['username']} (ID: {$user['id']}, Email: {$user['email']})", 'yellow');
                }
            }
            
            CLI::write('These users may need role reassignment or account deactivation.', 'yellow');
            
            // Log this as a warning
            $userIds = array_column($usersWithoutRoles, 'id');
            log_message('warning', 'Users with no active roles after expired role cleanup: ' . implode(', ', $userIds));
        }
    }
}
