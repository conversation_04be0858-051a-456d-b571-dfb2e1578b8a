<?php

namespace Modules\Authentication\Services;

use Modules\UserManagement\Models\UserModel; 
use Modules\RoleManagement\Models\RoleModel;
// use Modules\RoleManagement\Models\UserRoleModel;
use Modules\RoleManagement\Models\PermissionModel;

/**
 * Class PermissionService
 * Provides reusable methods for fetching permission data.
 */
class PermissionService
{
    protected RoleModel $roleModel;
    protected PermissionModel $permissionModel;
    protected UserModel $userModel;

    public function __construct()
    {
        $this->roleModel = new RoleModel();
        $this->permissionModel = new PermissionModel();
        $this->userModel = new UserModel();
    }

    /**
     * Fetches all permission IDs assigned to a specific role.
     *
     * @param int $roleId The ID of the role.
     * @return array An array of permission IDs.
     */
    public function getPermissionsByRoleId(int $roleId): array
    {
        $result = $this->roleModel->db->table('role_permissions')
                                    ->select('permission_id')
                                    ->where('role_id', $roleId)
                                    ->get()
                                    ->getResultArray();

        return array_column($result, 'permission_id');
    }

    /**
     * Fetches all permission names (slugs) for a given role ID.
     *
     * @param int $roleId The ID of the role.
     * @return array An array of permission names.
     */
    public function getPermissionNamesByRoleId(int $roleId): array
    {
        $permissionIds = $this->getPermissionsByRoleId($roleId);

        if (empty($permissionIds)) {
            return [];
        }

        $permissions = $this->permissionModel->select('name')
                                             ->whereIn('id', $permissionIds)
                                             ->findAll();

        return array_column($permissions, 'name');
    }

    /**
     * Fetches all permission names (slugs) for a specific user ID.
     * This combines getting all user's roles and then aggregating their permissions.
     *
     * @param int $userId The ID of the user.
     * @return array An array of unique permission names.
     */
    public function getPermissionNamesByUserId(int $userId): array
    {
        // Get all active roles for the user
        $userRoles = $this->userModel->db->table('user_roles')
                                        ->select('role_id')
                                        ->where('user_id', $userId)
                                        ->where('is_active', 1)
                                        ->where('(expires_at IS NULL OR expires_at > NOW())')
                                        ->get()
                                        ->getResultArray();

        if (empty($userRoles)) {
            return [];
        }

        $allPermissions = [];
        foreach ($userRoles as $userRole) {
            $rolePermissions = $this->getPermissionNamesByRoleId($userRole['role_id']);
            $allPermissions = array_merge($allPermissions, $rolePermissions);
        }

        // Return unique permissions
        return array_unique($allPermissions);
    }

    /**
     * Get all role IDs assigned to a user
     *
     * @param int $userId The ID of the user.
     * @return array An array of role IDs.
     */
    public function getUserRoleIds(int $userId): array
    {
        $userRoles = $this->userModel->db->table('user_roles')
                                        ->select('role_id')
                                        ->where('user_id', $userId)
                                        ->where('is_active', 1)
                                        ->where('(expires_at IS NULL OR expires_at > NOW())')
                                        ->get()
                                        ->getResultArray();

        return array_column($userRoles, 'role_id');
    }

    /**
     * Check if a user has a specific permission
     *
     * @param int $userId The ID of the user.
     * @param string $permissionName The permission name to check.
     * @return bool True if user has the permission, false otherwise.
     */
    public function userHasPermission(int $userId, string $permissionName): bool
    {
        $userPermissions = $this->getPermissionNamesByUserId($userId);
        return in_array($permissionName, $userPermissions);
    }

    /**
     * Check if a user has any of the specified permissions
     *
     * @param int $userId The ID of the user.
     * @param array $permissionNames Array of permission names to check.
     * @return bool True if user has any of the permissions, false otherwise.
     */
    public function userHasAnyPermission(int $userId, array $permissionNames): bool
    {
        $userPermissions = $this->getPermissionNamesByUserId($userId);
        return !empty(array_intersect($userPermissions, $permissionNames));
    }

    /**
     * Check if a user has all of the specified permissions
     *
     * @param int $userId The ID of the user.
     * @param array $permissionNames Array of permission names to check.
     * @return bool True if user has all permissions, false otherwise.
     */
    public function userHasAllPermissions(int $userId, array $permissionNames): bool
    {
        $userPermissions = $this->getPermissionNamesByUserId($userId);
        return empty(array_diff($permissionNames, $userPermissions));
    }

    /**
     * Fetches all available permissions in the system.
     *
     * @return array An array of all permission records.
     */
    public function getAllPermissions(): array
    {
        return $this->permissionModel->findAll();
    }

    /**
     * Fetches all available permissions grouped by their category.
     *
     * @return array An associative array where keys are categories and values are arrays of permission records.
     */
    public function getAllPermissionsGroupedByCategory(): array
    {
        // Order by category for consistent grouping, then by name within each category
        $permissions = $this->permissionModel->orderBy('category', 'ASC')->orderBy('name', 'ASC')->findAll();
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $category = $permission['category'] ?? 'Uncategorized'; // Provide a fallback
            if (!isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            $groupedPermissions[$category][] = $permission;
        }

        return $groupedPermissions;
    }

    /**
     * Check if a user has a specific role
     *
     * @param int $userId The ID of the user.
     * @param string $roleName The role name to check.
     * @return bool True if user has the role, false otherwise.
     */
    public function userHasRole(int $userId, string $roleName): bool
    {
        $userRoles = $this->userModel->db->table('user_roles ur')
                                        ->select('r.name')
                                        ->join('roles r', 'r.id = ur.role_id')
                                        ->where('ur.user_id', $userId)
                                        ->where('ur.is_active', 1)
                                        ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                                        ->where('r.name', $roleName)
                                        ->get()
                                        ->getResultArray();

        return !empty($userRoles);
    }

    /**
     * Check if a user has any of the specified roles
     *
     * @param int $userId The ID of the user.
     * @param array $roleNames Array of role names to check.
     * @return bool True if user has any of the roles, false otherwise.
     */
    public function userHasAnyRole(int $userId, array $roleNames): bool
    {
        $userRoles = $this->userModel->db->table('user_roles ur')
                                        ->select('r.name')
                                        ->join('roles r', 'r.id = ur.role_id')
                                        ->where('ur.user_id', $userId)
                                        ->where('ur.is_active', 1)
                                        ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                                        ->whereIn('r.name', $roleNames)
                                        ->get()
                                        ->getResultArray();

        return !empty($userRoles);
    }

    /**
     * Check if a user has all of the specified roles
     *
     * @param int $userId The ID of the user.
     * @param array $roleNames Array of role names to check.
     * @return bool True if user has all roles, false otherwise.
     */
    public function userHasAllRoles(int $userId, array $roleNames): bool
    {
        $userRoles = $this->userModel->db->table('user_roles ur')
                                        ->select('r.name')
                                        ->join('roles r', 'r.id = ur.role_id')
                                        ->where('ur.user_id', $userId)
                                        ->where('ur.is_active', 1)
                                        ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                                        ->whereIn('r.name', $roleNames)
                                        ->get()
                                        ->getResultArray();

        $foundRoles = array_column($userRoles, 'name');
        return empty(array_diff($roleNames, $foundRoles));
    }

    /**
     * Get all role names assigned to a user
     *
     * @param int $userId The ID of the user.
     * @return array An array of role names.
     */
    public function getUserRoleNames(int $userId): array
    {
        $userRoles = $this->userModel->db->table('user_roles ur')
                                        ->select('r.name')
                                        ->join('roles r', 'r.id = ur.role_id')
                                        ->where('ur.user_id', $userId)
                                        ->where('ur.is_active', 1)
                                        ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                                        ->get()
                                        ->getResultArray();

        return array_column($userRoles, 'name');
    }

    /**
     * Get detailed role information for a user
     *
     * @param int $userId The ID of the user.
     * @return array An array of role details with assignment information.
     */
    public function getUserRoleDetails(int $userId): array
    {
        return $this->userModel->db->table('user_roles ur')
                                  ->select('r.id, r.name, r.description, ur.assigned_by, ur.assigned_at, ur.expires_at, ur.is_active')
                                  ->join('roles r', 'r.id = ur.role_id')
                                  ->where('ur.user_id', $userId)
                                  ->where('ur.is_active', 1)
                                  ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                                  ->get()
                                  ->getResultArray();
    }

    /**
     * Check if a user is a super admin (has System Administrator role)
     *
     * @param int $userId The ID of the user.
     * @return bool True if user is a super admin, false otherwise.
     */
    public function isSuperAdmin(int $userId): bool
    {
        return $this->userHasRole($userId, 'System Administrator');
    }

    /**
     * Get permissions that a user has through a specific role
     *
     * @param int $userId The ID of the user.
     * @param string $roleName The role name.
     * @return array An array of permission names for the specific role.
     */
    public function getUserPermissionsByRole(int $userId, string $roleName): array
    {
        // First check if user has the role
        if (!$this->userHasRole($userId, $roleName)) {
            return [];
        }

        // Get role ID
        $role = $this->roleModel->where('name', $roleName)->first();
        if (!$role) {
            return [];
        }

        return $this->getPermissionNamesByRoleId($role['id']);
    }












}