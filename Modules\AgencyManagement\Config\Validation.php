<?php

namespace Modules\AgencyManagement\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public $create_agency = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[agencies.name,id,{id}]',
        'code' => 'required|is_unique[agencies.code,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        'address' => 'permit_empty|max_length[255]',
        'contact_person' => 'permit_empty|max_length[100]',
        'contact_number' => 'permit_empty|max_length[20]',
        'email' => 'permit_empty|valid_email|max_length[255]|is_unique[agencies.email,id,{id}]',
        'is_active' => 'permit_empty|in_list[0,1]',
    ];

    public $create_agency_errors = [
        'name' => [
            'required' => 'Agency name is required.',
            'min_length' => 'Agency name must be at least 3 characters long.',
            'max_length' => 'Agency name cannot exceed 255 characters.',
            'is_unique' => 'This agency name already exists.',
        ],
        'code' => [
            'required' => 'Agency code is required.',
            'is_unique' => 'This agency code already exists.',
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.',
            'is_unique' => 'This email address is already registered.',
        ],
    ];

    public $update_agency = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[agencies.name,id,{id}]',
        'code' => 'required|is_unique[agencies.code,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        'address' => 'permit_empty|max_length[255]',
        'contact_person' => 'permit_empty|max_length[100]',
        'contact_number' => 'permit_empty|max_length[20]',
        'email' => 'permit_empty|valid_email|max_length[255]|is_unique[agencies.email,id,{id}]',
        'is_active' => 'permit_empty|in_list[0,1]',
    ];

    public $update_agency_errors = [
        'name' => [
            'required' => 'Agency name is required.',
            'min_length' => 'Agency name must be at least 3 characters long.',
            'max_length' => 'Agency name cannot exceed 255 characters.',
            'is_unique' => 'This agency name already exists.',
        ],
        'code' => [
            'required' => 'Agency code is required.',
            'is_unique' => 'This agency code already exists.',
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.',
            'is_unique' => 'This email address is already registered.',
        ],
    ];
    

}
