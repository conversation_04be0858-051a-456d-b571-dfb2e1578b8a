<?php

namespace Modules\UserManagement\Config;

// $routes->group('users', ['namespace' => 'Modules\UserManagement\Controllers', 'filter' => 'login'], function 
// ($routes) {


$routes->group('admin', function($routes) {
    $routes->group('users', [
        'namespace' => 'Modules\UserManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Main CRUD routes
        $routes->GET('/', 'UserController::index', ['as' => 'users.index']);
        $routes->GET('create', 'UserController::create', ['as' => 'users.create']);
        $routes->POST('store', 'UserController::store', ['as' => 'users.store']);
        $routes->GET('edit/(:segment)', 'UserController::edit/$1', ['as' => 'users.edit']);
        $routes->POST('update/(:segment)', 'UserController::update/$1', ['as' => 'users.update']);
        $routes->GET('delete/(:segment)', 'UserController::delete/$1', ['as' => 'users.delete']);

        // AJAX endpoints for DataTables
        $routes->POST('datatable', 'UserController::datatable', ['as' => 'users.datatable']);
        $routes->POST('ajax-delete', 'UserController::ajaxDelete', ['as' => 'users.ajax_delete']);
        $routes->GET('show/(:segment)', 'UserController::show/$1', ['as' => 'users.show']);

        // Bulk operations
        $routes->POST('bulk-delete', 'UserController::bulkDelete', ['as' => 'users.bulk_delete']);
        $routes->POST('bulk-activate', 'UserController::bulkActivate', ['as' => 'users.bulk_activate']);
        $routes->POST('bulk-deactivate', 'UserController::bulkDeactivate', ['as' => 'users.bulk_deactivate']);
    });
});
