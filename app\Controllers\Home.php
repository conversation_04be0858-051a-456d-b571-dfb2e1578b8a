<?php

namespace App\Controllers;

class Home extends BaseController
{
    public function index()
    {
        if(!session()->get('isLoggedIn')){
            return redirect()->to('cinauth/');
        }
        else{
            return redirect()->to('exhibitor/events');
        }
        // return view('welcome_message');
    }

    public function admin()
    {
        if(!session()->get('isLoggedIn')){
            return redirect()->to('admin/auth/login');
        }
        else{
            return redirect()->to('admin/dashboard');
        }
    }
}
