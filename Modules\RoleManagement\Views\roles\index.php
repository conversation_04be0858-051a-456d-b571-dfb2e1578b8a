<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Role Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?= $statistics['total'] ?? 0 ?></h3>
                <p>Total Roles</p>
            </div>
            <div class="icon">
                <i class="fas fa-users-cog"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?= $statistics['with_permissions'] ?? 0 ?></h3>
                <p>With Permissions</p>
            </div>
            <div class="icon">
                <i class="fas fa-key"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?= $statistics['without_permissions'] ?? 0 ?></h3>
                <p>Without Permissions</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
    </div>
</div>

<!-- Display flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<!-- Display validation errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <strong>Validation Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">All Roles</h3>
        <div class="card-tools">
            <a href="<?= route_to('roles.create') ?>" class="btn btn-primary btn-sm rounded-md shadow-sm">
                <i class="fas fa-plus"></i> Add New Role
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Bulk Actions -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                        <i class="fas fa-check"></i> Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                        <i class="fas fa-times"></i> Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <span class="ml-2 text-muted" id="selected-count">0 selected</span>
            </div>
        </div>

        <div class="table-responsive">
            <table id="rolesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <?php if (hasPermission('role.manage')) : ?>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <?php endif; ?>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Role View Modal -->
<div class="modal fade" id="roleViewModal" tabindex="-1" role="dialog" aria-labelledby="roleViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roleViewModalLabel">Role Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="roleViewContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
 <!-- DataTables  & Plugins -->
<script src="<?= base_url("assets/plugins/datatables/jquery.dataTables.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/jszip/jszip.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/pdfmake.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/vfs_fonts.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js") ?>"></script>

<script>
$(document).ready(function() {
    // CSRF token
    var csrfName = '<?= csrf_token() ?>';
    var csrfHash = '<?= csrf_hash() ?>';

    // Initialize DataTable with Hermawan DataTables
    var roleTable = $('#rolesTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/roles/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[2, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('role.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'name' }
            ,{ data: 'description' }
            ,{ data: 'status_badge', orderable: true, searchable: true }
            ,{ data: 'created_at' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // Select all checkbox functionality
    $('#select-all').on('click', function() {
        var isChecked = $(this).prop('checked');
        $('.select-item').prop('checked', isChecked);
        updateBulkButtons();
    });

    // Individual checkbox functionality
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select-all checkbox
        var totalCheckboxes = $('.select-item').length;
        var checkedCheckboxes = $('.select-item:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedCount = $('.select-item:checked').length;
        $('#selected-count').text(selectedCount + ' selected');

        if (selectedCount > 0) {
            $('#bulk-delete, #bulk-activate, #bulk-deactivate').prop('disabled', false);
        } else {
            $('#bulk-delete, #bulk-activate, #bulk-deactivate').prop('disabled', true);
        }
    }

    // Bulk delete
    $('#bulk-delete').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select roles to delete.');
            return;
        }

        if (confirm('Are you sure you want to delete the selected roles?')) {
            $.ajax({
                url: '<?= site_url('admin/roles/bulk-delete') ?>',
                type: 'POST',
                data: {
                    ids: selectedIds,
                    [csrfName]: csrfHash
                },
                success: function(response) {
                    if (response.success) {
                        roleTable.ajax.reload();
                        $('#select-all').prop('checked', false);
                        updateBulkButtons();
                        alert(response.message);
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while deleting roles.');
                }
            });
        }
    });

    // Bulk activate
    $('#bulk-activate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select roles to activate.');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/roles/bulk-activate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    roleTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    alert(response.message);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('An error occurred while activating roles.');
            }
        });
    });

    // Bulk deactivate
    $('#bulk-deactivate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select roles to deactivate.');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/roles/bulk-deactivate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    roleTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    alert(response.message);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('An error occurred while deactivating roles.');
            }
        });
    });

    // Individual delete
    $(document).on('click', '#deleteRole', function() {
        var roleId = $(this).data('id');

        if (confirm('Are you sure you want to delete this role?')) {
            $.ajax({
                url: '<?= site_url('admin/roles/ajax-delete') ?>',
                type: 'POST',
                data: {
                    id: roleId,
                    [csrfName]: csrfHash
                },
                success: function(response) {
                    if (response.success) {
                        roleTable.ajax.reload();
                        alert(response.message);
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while deleting the role.');
                }
            });
        }
    });

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});

// View role function
function viewRole(roleId) {
    $.ajax({
        url: '<?= site_url('admin/roles/show') ?>/' + roleId,
        type: 'GET',
        success: function(response) {
            $('#roleViewContent').html(response);
            $('#roleViewModal').modal('show');
        },
        error: function() {
            alert('An error occurred while loading role details.');
        }
    });
}
</script>
<?= $this->endSection() ?>