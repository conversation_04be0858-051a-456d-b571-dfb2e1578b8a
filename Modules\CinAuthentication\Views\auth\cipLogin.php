<?= $this->extend('Modules\CinAuthentication\Views\layouts\auth'); ?>

<?= $this->section('title'); ?><?= $title??'no title set';?><?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<div class="auth-logo">
    <!-- System Logo -->
    <img src="<?= base_url('assets/dist/img/citem_logo_2023.png') ?>" alt="System Logo" class="img-fluid">
    <h1><?= $system_name??'System Dashboard';?></h1>
</div>

<div class="card">
    <div class="card-body">
        <h2 class="h3 text-center mb-3">Sign in to your account</h2>

        <!-- Flash Messages -->
        <?php if(session()->has('warning')): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <?php echo session()->get('warning'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session()->getFlashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo session()->getFlashdata('success') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php elseif(session()->getFlashdata('failed')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo session()->getFlashdata('failed') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('error') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?= form_open(route_to('cinauth.login'), ['class' => 'needs-validation', 'novalidate' => '', 'id' => 'login-form']);?>
            <?= csrf_field() ?>

            <div class="mb-3">
                <label class="form-label"><?= $loginLabel ?></label>
                <div class="input-group input-group-flat">
                    <input type="text"
                           class="form-control <?php echo isInvalid($loginField) ? 'is-invalid' : '' ?>"
                           name="<?= $loginField ?>"
                           placeholder="Enter your <?= strtolower($loginLabel) ?>"
                           value="<?php echo old($loginField) ?>"
                           autocomplete="<?= $loginField === 'email' ? 'email' : 'username' ?>"
                           required/>
                    <span class="input-group-text">
                        <?php if($loginField === 'email'): ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <rect x="3" y="5" width="18" height="14" rx="2"/>
                                <polyline points="3,7 12,13 21,7"/>
                            </svg>
                        <?php else: ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <circle cx="12" cy="7" r="4"/>
                                <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                            </svg>
                        <?php endif; ?>
                    </span>
                </div>
                <?php if(session('errors.' . $loginField)): ?>
                    <div class="invalid-feedback d-block">
                        <?php echo show_validation_error($loginField,session("errors"));?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label class="form-label">Password</label>
                <div class="input-group input-group-flat">
                    <input id="password"
                           type="password"
                           class="form-control <?php echo isInvalid('password') ? 'is-invalid' : '' ?>"
                           name="password"
                           placeholder="Enter your password"
                           autocomplete="current-password"
                           required/>
                    <span class="input-group-text password-toggle" id="password-toggle">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="2"/>
                            <path d="M22 12c-2.667 4.667 -6 7 -10 7s-7.333 -2.333 -10 -7c2.667 -4.667 6 -7 10 -7s7.333 2.333 10 7"/>
                        </svg>
                    </span>
                </div>
                <?php if(session('errors.password')): ?>
                    <div class="invalid-feedback d-block">
                        <?php echo show_validation_error('password',session("errors"));?>
                    </div>
                <?php endif; ?>
            </div>


            <div class="form-footer">
                <button type="submit" name="submit" value="1" class="btn btn-primary w-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"/>
                        <path d="M20 12h-13l3 -3m0 6l-3 -3"/>
                    </svg>
                    Sign in
                </button>
            </div>
        </form>

        <!-- Optional Links -->
        <!--
        <div class="text-center text-muted mt-3">
            <a href="<?= site_url('auth/forgot_password') ?>" tabindex="-1">Forgot password?</a>
        </div>
        -->
    </div>
</div>
<?= $this->endSection(); ?>


<?= $this->section('script'); ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const passwordInput = document.querySelector("#password");
    const passwordToggle = document.querySelector("#password-toggle");

    if (passwordToggle && passwordInput) {
        passwordToggle.addEventListener("click", function(){
            const type = passwordInput.getAttribute("type") === "password" ? "text" : "password";
            passwordInput.setAttribute("type", type);

            // Toggle icon
            const icon = this.querySelector('svg');
            if (type === "text") {
                // Eye slash icon
                icon.innerHTML = `
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <line x1="3" y1="3" x2="21" y2="21"/>
                    <path d="M10.584 10.587a2 2 0 0 0 2.828 2.83"/>
                    <path d="M9.363 5.365a9.466 9.466 0 0 1 2.637 -.365c4 0 7.333 2.333 10 7c-.778 1.361 -1.612 2.524 -2.503 3.488m-2.14 1.861c-1.631 1.1 -3.415 1.651 -5.357 1.651c-4 0 -7.333 -2.333 -10 -7c1.369 -2.395 2.913 -4.175 4.632 -5.341"/>
                `;
            } else {
                // Eye icon
                icon.innerHTML = `
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <circle cx="12" cy="12" r="2"/>
                    <path d="M22 12c-2.667 4.667 -6 7 -10 7s-7.333 -2.333 -10 -7c2.667 -4.667 6 -7 10 -7s7.333 2.333 10 7"/>
                `;
            }
        });
    }

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>

<?= $this->endSection(); ?>