<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
Expired Roles - <?= esc($user['username']) ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
Expired Roles for <?= esc($user['username']) ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= site_url('admin/users') ?>">Users</a></li>
<li class="breadcrumb-item"><a href="<?= site_url('admin/users/assign-roles/' . $user['id']) ?>">Assign Roles</a></li>
<li class="breadcrumb-item active">Expired Roles</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-warning">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-history"></i>
            Expired Roles for <?= esc($user['username']) ?>
        </h3>
        <div class="card-tools">
            <a href="<?= site_url('admin/users/assign-roles/' . $user['id']) ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-user-tag"></i> Manage Active Roles
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?= session()->getFlashdata('success') ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?= session()->getFlashdata('error') ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>

        <!-- User Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-info">
                        <i class="fas fa-user"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">User Information</span>
                        <span class="info-box-number"><?= esc($user['username']) ?></span>
                        <span class="info-box-more"><?= esc($user['email']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-warning">
                        <i class="fas fa-clock"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Expired Roles</span>
                        <span class="info-box-number"><?= count($expiredRoles) ?></span>
                        <span class="info-box-more">Total expired role assignments</span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($expiredRoles)): ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th>Role Name</th>
                            <th>Description</th>
                            <th>Assigned Date</th>
                            <th>Expired Date</th>
                            <th>Days Expired</th>
                            <th>Assigned By</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($expiredRoles as $expiredRole): ?>
                            <?php
                                $expiredDate = strtotime($expiredRole['expires_at']);
                                $currentDate = time();
                                $daysExpired = floor(($currentDate - $expiredDate) / (60 * 60 * 24));
                            ?>
                            <tr>
                                <td>
                                    <strong><?= esc($expiredRole['role_name']) ?></strong>
                                </td>
                                <td><?= esc($expiredRole['role_description']) ?></td>
                                <td>
                                    <span class="badge badge-info">
                                        <?= date('M j, Y', strtotime($expiredRole['assigned_at'])) ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        <?= date('g:i A', strtotime($expiredRole['assigned_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge badge-warning">
                                        <?= date('M j, Y', $expiredDate) ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        <?= date('g:i A', $expiredDate) ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge badge-danger">
                                        <?= $daysExpired ?> day<?= $daysExpired != 1 ? 's' : '' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($expiredRole['assigned_by']): ?>
                                        <span class="badge badge-secondary">
                                            User ID: <?= $expiredRole['assigned_by'] ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($expiredRole['is_active']): ?>
                                        <span class="badge badge-warning">
                                            <i class="fas fa-clock"></i> Expired but Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-pause"></i> Deactivated
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('admin/users/extend-role-expiration/' . $user['id'] . '/' . $expiredRole['role_id']) ?>" 
                                           class="btn btn-sm btn-success" 
                                           title="Extend Expiration">
                                            <i class="fas fa-calendar-plus"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-info" 
                                                data-toggle="modal" 
                                                data-target="#roleHistoryModal"
                                                data-role-name="<?= esc($expiredRole['role_name']) ?>"
                                                data-role-id="<?= $expiredRole['role_id'] ?>"
                                                title="View History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Summary Statistics -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card card-outline card-warning">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-bar"></i> Expiration Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php
                                $recentlyExpired = 0; // Within 30 days
                                $longExpired = 0; // More than 30 days
                                $stillActive = 0; // Expired but still marked as active
                                
                                foreach ($expiredRoles as $role) {
                                    $expiredDate = strtotime($role['expires_at']);
                                    $daysExpired = floor((time() - $expiredDate) / (60 * 60 * 24));
                                    
                                    if ($daysExpired <= 30) {
                                        $recentlyExpired++;
                                    } else {
                                        $longExpired++;
                                    }
                                    
                                    if ($role['is_active']) {
                                        $stillActive++;
                                    }
                                }
                            ?>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="description-block">
                                        <h5 class="description-header text-warning"><?= $recentlyExpired ?></h5>
                                        <span class="description-text">Recently Expired (≤30 days)</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="description-block">
                                        <h5 class="description-header text-danger"><?= $longExpired ?></h5>
                                        <span class="description-text">Long Expired (>30 days)</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="description-block">
                                        <h5 class="description-header text-info"><?= $stillActive ?></h5>
                                        <span class="description-text">Still Active (Need Cleanup)</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="description-block">
                                        <h5 class="description-header text-success"><?= count($expiredRoles) - $stillActive ?></h5>
                                        <span class="description-text">Properly Deactivated</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <div class="alert alert-success">
                <h5><i class="icon fas fa-check"></i> Great!</h5>
                This user has no expired roles. All role assignments are current and valid.
            </div>
        <?php endif; ?>
    </div>

    <div class="card-footer">
        <a href="<?= site_url('admin/users/assign-roles/' . $user['id']) ?>" class="btn btn-primary">
            <i class="fas fa-user-tag"></i> Manage Active Roles
        </a>
        <a href="<?= site_url('admin/users') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
        <?php if (!empty($expiredRoles) && $stillActive > 0): ?>
            <button type="button" class="btn btn-warning" onclick="cleanupExpiredRoles()">
                <i class="fas fa-broom"></i> Cleanup Expired Roles (<?= $stillActive ?>)
            </button>
        <?php endif; ?>
    </div>
</div>

<!-- Role History Modal -->
<div class="modal fade" id="roleHistoryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-history"></i> Role Assignment History
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Role assignment history for <strong id="modalRoleName"></strong></p>
                <!-- History content would be loaded here via AJAX -->
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Loading history...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function cleanupExpiredRoles() {
    if (confirm('This will deactivate all expired roles that are still marked as active. Continue?')) {
        // Implementation for cleanup would go here
        // This could be an AJAX call to a cleanup endpoint
        alert('Cleanup functionality would be implemented here.');
    }
}

// Handle role history modal
$('#roleHistoryModal').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget);
    var roleName = button.data('role-name');
    var roleId = button.data('role-id');
    
    var modal = $(this);
    modal.find('#modalRoleName').text(roleName);
    
    // Here you would load the role history via AJAX
    // For now, just show a placeholder
    setTimeout(function() {
        modal.find('.modal-body').html(
            '<p>Role assignment history for <strong>' + roleName + '</strong></p>' +
            '<div class="alert alert-info">History functionality would be implemented here.</div>'
        );
    }, 1000);
});
</script>
<?= $this->endSection() ?>
