<!DOCTYPE html>
<html>
<head>
  <title>jVectorMap demo</title>
  <link rel="stylesheet" media="all" href="../jquery-jvectormap.css"/>
  <script src="assets/jquery-1.8.2.js"></script>
  <script src="../jquery-jvectormap.js"></script>
  <script src="../jquery-mousewheel.js"></script>

  <script src="../lib/jvectormap.js"></script>

  <script src="../lib/abstract-element.js"></script>
  <script src="../lib/abstract-canvas-element.js"></script>
  <script src="../lib/abstract-shape-element.js"></script>

  <script src="../lib/svg-element.js"></script>
  <script src="../lib/svg-group-element.js"></script>
  <script src="../lib/svg-canvas-element.js"></script>
  <script src="../lib/svg-shape-element.js"></script>
  <script src="../lib/svg-path-element.js"></script>
  <script src="../lib/svg-circle-element.js"></script>

  <script src="../lib/vml-element.js"></script>
  <script src="../lib/vml-group-element.js"></script>
  <script src="../lib/vml-canvas-element.js"></script>
  <script src="../lib/vml-shape-element.js"></script>
  <script src="../lib/vml-path-element.js"></script>
  <script src="../lib/vml-circle-element.js"></script>

  <script src="../lib/vector-canvas.js"></script>
  <script src="../lib/simple-scale.js"></script>
  <script src="../lib/numeric-scale.js"></script>
  <script src="../lib/ordinal-scale.js"></script>
  <script src="../lib/color-scale.js"></script>
  <script src="../lib/data-series.js"></script>
  <script src="../lib/proj.js"></script>
  <script src="../lib/world-map.js"></script>

  <script src="assets/jquery-jvectormap-map.js"></script>
  <script>
    $(function(){
      var map = $('#map1').vectorMap({
        map: 'map',
        markers: [
            {coords: [100, 100], name: 'Marker 1', style: {fill: 'yellow'}},
            {coords: [200, 200], name: 'Marker 2', style: {fill: 'yellow'}},
        ]
      });
    })
  </script>
</head>
<body>
  <div id="map1" style="width: 600px; height: 600px"></div>
</body>
</html>