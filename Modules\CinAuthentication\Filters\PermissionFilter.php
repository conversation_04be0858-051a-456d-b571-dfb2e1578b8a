<?php

namespace Modules\CinAuthentication\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;
use Modules\Authentication\Models\RoleModel;

class PermissionFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $requiredPermission = $arguments[0]; // e.g., "manage_users"
        $userRoleId = session()->get('role_id');

        if (!$userRoleId) {
            return redirect()->to('/auth/login')->with('error', 'Access denied.');
        }

        // Fetch permissions for the user's role
        $roleModel = new RoleModel();
        $role = $roleModel->find($userRoleId);
        $permissions = json_decode($role['permissions'], true);

        if (!in_array($requiredPermission, $permissions)) {
            return redirect()->to('/')->with('error', 'You do not have permission to access this page.');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Optional post-processing
    }
}
