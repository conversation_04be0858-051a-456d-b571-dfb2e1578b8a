<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combined Navigation Test - Sales Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Tabler CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" rel="preload" as="image">
    
    <!-- Custom CSS for combined navigation -->
    <style>
        .navbar-vertical {
            width: 15rem;
            position: fixed;
            top: 3.5rem; /* Height of top navbar */
            left: 0;
            bottom: 0;
            z-index: 1000;
            background: var(--tblr-bg-surface);
            border-right: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);
            overflow-y: auto;
        }
        
        .navbar-vertical .navbar-nav {
            flex-direction: column;
            padding: 1rem 0;
        }
        
        .navbar-vertical .nav-item {
            width: 100%;
        }
        
        .navbar-vertical .nav-link {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: var(--tblr-body-color);
            text-decoration: none;
            border-radius: 0;
            margin: 0 0.5rem;
        }
        
        .navbar-vertical .nav-link:hover {
            background-color: var(--tblr-bg-surface-secondary);
            color: var(--tblr-primary);
        }
        
        .navbar-vertical .nav-link.active {
            background-color: var(--tblr-primary);
            color: var(--tblr-primary-fg);
        }
        
        .navbar-vertical .nav-link .nav-link-icon {
            margin-right: 0.5rem;
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }
        
        .navbar-vertical .nav-link .nav-link-title {
            flex: 1;
        }
        
        .navbar-vertical .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: transparent;
            border: 0;
            box-shadow: none;
            border-radius: 0;
        }
        
        .navbar-vertical .dropdown-item {
            padding: 0.375rem 2.5rem;
            color: var(--tblr-body-color);
        }
        
        .navbar-vertical .dropdown-item:hover {
            background-color: var(--tblr-bg-surface-secondary);
            color: var(--tblr-primary);
        }
        
        .page-wrapper {
            margin-left: 15rem; /* Width of sidebar */
            margin-top: 3.5rem; /* Height of top navbar */
        }
        
        .navbar-brand {
            padding: 0.5rem 1rem;
        }
        
        .navbar-brand-image {
            height: 2rem;
            width: auto;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .navbar-vertical {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .navbar-vertical.show {
                transform: translateX(0);
            }
            
            .page-wrapper {
                margin-left: 0;
            }
            
            .navbar-toggler {
                display: block !important;
            }
        }
        
        .sidebar-toggle {
            display: none;
        }
        
        @media (max-width: 768px) {
            .sidebar-toggle {
                display: inline-block;
            }
        }
        
        /* Dropdown indicators */
        .navbar-vertical .dropdown-toggle::after {
            margin-left: auto;
            border: none;
            content: "›";
            font-size: 1.2em;
            transition: transform 0.2s ease;
        }
        
        .navbar-vertical .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(90deg);
        }
        
        /* Badge styles */
        .nav-link .badge {
            margin-left: auto;
        }
    </style>
    
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</head>
<body>
    <div class="page">
        <!-- Top Navigation Bar -->
        <header class="navbar navbar-expand-md navbar-light d-print-none" style="position: fixed; top: 0; left: 0; right: 0; z-index: 1030;">
            <div class="container-xl">
                <!-- Mobile sidebar toggle -->
                <button class="navbar-toggler sidebar-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- Brand -->
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href="<?= base_url('sales') ?>">
                        <img src="https://tabler.io/static/logo.svg" width="110" height="32" alt="Tabler" class="navbar-brand-image">
                        <span class="ms-2">Sales Dashboard</span>
                    </a>
                </h1>
                
                <!-- Top Navigation Menu -->
                <div class="navbar-nav flex-row order-md-last">
                    <!-- Top menu items -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                            <span class="avatar avatar-sm" style="background-image: url(https://ui-avatars.com/api/?name=Test+User&background=206bc4&color=fff)"></span>
                            <div class="d-none d-xl-block ps-2">
                                <div>Test User</div>
                                <div class="mt-1 small text-muted">Sales Manager</div>
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <a href="#" class="dropdown-item">Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item">Logout</a>
                        </div>
                    </div>
                </div>
                
                <!-- Top navigation menu items -->
                <div class="collapse navbar-collapse" id="navbar-menu">
                    <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('sales/dashboard') ?>">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="5 12 3 12 12 3 21 12 19 12"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>
                                    </span>
                                    <span class="nav-link-title">Dashboard</span>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" role="button">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
                                    </span>
                                    <span class="nav-link-title">Customers</span>
                                </a>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="<?= base_url('sales/customers') ?>">All Customers</a>
                                    <a class="dropdown-item" href="<?= base_url('sales/customers/add') ?>">Add Customer</a>
                                    <a class="dropdown-item" href="<?= base_url('sales/customers/reports') ?>">Customer Reports</a>
                                </div>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('sales/reports') ?>">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="4" y1="19" x2="20" y2="19"/><polyline points="4,15 8,9 12,11 16,6 20,10"/></svg>
                                    </span>
                                    <span class="nav-link-title">Reports</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Sidebar Navigation -->
        <aside class="navbar navbar-vertical navbar-expand-lg" id="sidebar-menu">
            <div class="container-fluid">
                <div class="navbar-nav">
                    <!-- Dashboard Section -->
                    <div class="nav-item">
                        <a class="nav-link active" href="<?= base_url('sales/dashboard') ?>">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="5 12 3 12 12 3 21 12 19 12"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>
                            </span>
                            <span class="nav-link-title">Dashboard</span>
                        </a>
                    </div>
                    
                    <!-- Sales Section -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#sales-menu" data-bs-toggle="dropdown" role="button">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="6" cy="19" r="2"/><circle cx="17" cy="19" r="2"/><path d="M17 17h-11v-14h-2"/><path d="M6 5l14 1l-1 7h-13"/></svg>
                            </span>
                            <span class="nav-link-title">Sales</span>
                            <span class="badge badge-sm bg-green ms-auto">12</span>
                        </a>
                        <div class="dropdown-menu" id="sales-menu">
                            <a class="dropdown-item" href="<?= base_url('sales/orders') ?>">Orders</a>
                            <a class="dropdown-item" href="<?= base_url('sales/quotes') ?>">Quotes</a>
                            <a class="dropdown-item" href="<?= base_url('sales/invoices') ?>">Invoices</a>
                            <a class="dropdown-item" href="<?= base_url('sales/products') ?>">Products</a>
                        </div>
                    </div>
                    
                    <!-- Customers Section -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#customers-menu" data-bs-toggle="dropdown" role="button">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
                            </span>
                            <span class="nav-link-title">Customers</span>
                        </a>
                        <div class="dropdown-menu" id="customers-menu">
                            <a class="dropdown-item" href="<?= base_url('sales/customers') ?>">All Customers</a>
                            <a class="dropdown-item" href="<?= base_url('sales/customers/add') ?>">Add Customer</a>
                            <a class="dropdown-item" href="<?= base_url('sales/customers/import') ?>">Import Customers</a>
                            <a class="dropdown-item" href="<?= base_url('sales/customers/segments') ?>">Customer Segments</a>
                        </div>
                    </div>
                    
                    <!-- Analytics Section -->
                    <div class="nav-item">
                        <a class="nav-link" href="<?= base_url('sales/analytics') ?>">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="4" y1="19" x2="20" y2="19"/><polyline points="4,15 8,9 12,11 16,6 20,10"/></svg>
                            </span>
                            <span class="nav-link-title">Analytics</span>
                        </a>
                    </div>
                    
                    <!-- Reports Section -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#reports-menu" data-bs-toggle="dropdown" role="button">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/><line x1="9" y1="9" x2="10" y2="9"/><line x1="9" y1="13" x2="15" y2="13"/><line x1="9" y1="17" x2="15" y2="17"/></svg>
                            </span>
                            <span class="nav-link-title">Reports</span>
                        </a>
                        <div class="dropdown-menu" id="reports-menu">
                            <a class="dropdown-item" href="<?= base_url('sales/reports/sales') ?>">Sales Reports</a>
                            <a class="dropdown-item" href="<?= base_url('sales/reports/performance') ?>">Performance</a>
                            <a class="dropdown-item" href="<?= base_url('sales/reports/forecasting') ?>">Forecasting</a>
                            <a class="dropdown-item" href="<?= base_url('sales/reports/custom') ?>">Custom Reports</a>
                        </div>
                    </div>
                    
                    <!-- Settings Section -->
                    <div class="nav-item">
                        <a class="nav-link" href="<?= base_url('sales/settings') ?>">
                            <span class="nav-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/><circle cx="12" cy="12" r="3"/></svg>
                            </span>
                            <span class="nav-link-title">Settings</span>
                        </a>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Page wrapper -->
        <div class="page-wrapper">
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <div class="page-pretitle">Navigation Test</div>
                            <h2 class="page-title">Combined Navigation Test</h2>
                            <div class="text-muted mt-1">Testing combined top navigation and sidebar menu system</div>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <div class="btn-list">
                                <a href="#" class="btn btn-primary d-none d-sm-inline-block">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="12" y1="5" x2="12" y2="19"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
                                    Create new
                                </a>
                                <a href="#" class="btn btn-primary d-sm-none btn-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="12" y1="5" x2="12" y2="19"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    <div class="row row-deck row-cards">
                        <!-- Navigation Status Card -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Combined Navigation System Status</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h4>Top Navigation Features</h4>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Fixed Top Header
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Brand Logo & Title
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    User Profile Dropdown
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Top Menu Items
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Mobile Toggle Button
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h4>Sidebar Navigation Features</h4>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Fixed Sidebar
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Collapsible Dropdowns
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Icon Support
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Badge Notifications
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    Mobile Responsive
                                                    <span class="badge bg-green">Active</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Demo Content Cards -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="subheader">Sales</div>
                                        <div class="ms-auto lh-1">
                                            <div class="dropdown">
                                                <a class="dropdown-toggle text-muted" href="#" data-bs-toggle="dropdown">Last 7 days</a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <a class="dropdown-item active" href="#">Last 7 days</a>
                                                    <a class="dropdown-item" href="#">Last 30 days</a>
                                                    <a class="dropdown-item" href="#">Last 3 months</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="h1 mb-3">75%</div>
                                    <div class="d-flex mb-2">
                                        <div>Conversion rate</div>
                                        <div class="ms-auto">
                                            <span class="text-green d-inline-flex align-items-center lh-1">
                                                6%
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar bg-primary" style="width: 75%" role="progressbar"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="subheader">Revenue</div>
                                        <div class="ms-auto lh-1">
                                            <div class="dropdown">
                                                <a class="dropdown-toggle text-muted" href="#" data-bs-toggle="dropdown">Last 7 days</a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <a class="dropdown-item active" href="#">Last 7 days</a>
                                                    <a class="dropdown-item" href="#">Last 30 days</a>
                                                    <a class="dropdown-item" href="#">Last 3 months</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-baseline">
                                        <div class="h1 mb-0 me-2">$4,300</div>
                                        <div class="me-auto">
                                            <span class="text-green d-inline-flex align-items-center lh-1">
                                                8%
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div id="chart-revenue-bg" class="chart-sm"></div>
                            </div>
                        </div>

                        <div class="col-md-12 col-lg-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="subheader">New customers</div>
                                    </div>
                                    <div class="d-flex align-items-baseline">
                                        <div class="h1 mb-3 me-2">6,782</div>
                                        <div class="me-auto">
                                            <span class="text-yellow d-inline-flex align-items-center lh-1">
                                                0%
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div id="chart-new-clients" class="chart-sm"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Test Actions -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Navigation Test Actions</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h4>Test Sidebar Navigation</h4>
                                            <p class="text-muted">Click on the sidebar menu items to test navigation functionality.</p>
                                            <div class="btn-list">
                                                <button class="btn btn-outline-primary" onclick="testSidebarDropdown('sales-menu')">Test Sales Dropdown</button>
                                                <button class="btn btn-outline-primary" onclick="testSidebarDropdown('customers-menu')">Test Customers Dropdown</button>
                                                <button class="btn btn-outline-primary" onclick="testSidebarDropdown('reports-menu')">Test Reports Dropdown</button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h4>Test Top Navigation</h4>
                                            <p class="text-muted">Test the top navigation bar functionality and responsiveness.</p>
                                            <div class="btn-list">
                                                <button class="btn btn-outline-success" onclick="toggleMobileSidebar()">Toggle Mobile Sidebar</button>
                                                <button class="btn btn-outline-success" onclick="testUserDropdown()">Test User Dropdown</button>
                                                <button class="btn btn-outline-success" onclick="testTopNavDropdown()">Test Top Nav Dropdown</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for navigation functionality -->
    <script>
        // Initialize dropdowns and navigation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Mobile sidebar toggle functionality
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.navbar-vertical');

            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth <= 768) {
                    const sidebar = document.querySelector('.navbar-vertical');
                    const sidebarToggle = document.querySelector('.sidebar-toggle');

                    if (sidebar && !sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle sidebar dropdown toggles
            const sidebarDropdowns = document.querySelectorAll('.navbar-vertical .dropdown-toggle');
            sidebarDropdowns.forEach(function(dropdown) {
                dropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    const dropdownMenu = this.nextElementSibling;
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';

                    // Close all other dropdowns
                    sidebarDropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.setAttribute('aria-expanded', 'false');
                            const otherMenu = otherDropdown.nextElementSibling;
                            if (otherMenu) {
                                otherMenu.style.display = 'none';
                            }
                        }
                    });

                    // Toggle current dropdown
                    this.setAttribute('aria-expanded', !isExpanded);
                    if (dropdownMenu) {
                        dropdownMenu.style.display = isExpanded ? 'none' : 'block';
                    }
                });
            });
        });

        // Test functions for navigation
        function testSidebarDropdown(menuId) {
            const dropdown = document.querySelector(`[href="#${menuId}"]`);
            if (dropdown) {
                dropdown.click();
                showAlert('Sidebar dropdown toggled: ' + menuId, 'success');
            }
        }

        function toggleMobileSidebar() {
            const sidebar = document.querySelector('.navbar-vertical');
            if (sidebar) {
                sidebar.classList.toggle('show');
                showAlert('Mobile sidebar toggled', 'info');
            }
        }

        function testUserDropdown() {
            const userDropdown = document.querySelector('[data-bs-toggle="dropdown"]');
            if (userDropdown) {
                userDropdown.click();
                showAlert('User dropdown toggled', 'success');
            }
        }

        function testTopNavDropdown() {
            const topNavDropdown = document.querySelector('.navbar-nav .dropdown-toggle');
            if (topNavDropdown) {
                topNavDropdown.click();
                showAlert('Top navigation dropdown toggled', 'success');
            }
        }

        function showAlert(message, type) {
            // Create alert element
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // Handle window resize for responsive behavior
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.navbar-vertical');
            if (window.innerWidth > 768 && sidebar) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>
