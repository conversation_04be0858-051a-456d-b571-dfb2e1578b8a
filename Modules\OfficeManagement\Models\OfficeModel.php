<?php

namespace Modules\OfficeManagement\Models;

use CodeIgniter\Model;
use Modules\AuditTrail\Libraries\AuditableTrait;

class OfficeModel extends Model
{
    use AuditableTrait;

    protected $DBGroup = 'default';
    protected $table = 'offices';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 
        'code',  
        'description',
        'email',
        'is_active',
        'created_at', 
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    public function __construct()
    {
        parent::__construct();
        $this->auditableEvents = ['create', 'update', 'delete'];
        $this->auditableFields = [];
        $this->auditableExcludeFields = ['created_at', 'updated_at'];

        if (method_exists($this, 'initializeAuditLogging')) {
            $this->initializeAuditLogging();
        }
    }

    public function getOfficesData(){
        return $this->db->table($this->table.' a')
            ->select('*')
            ->orderBy('a.created_at', 'DESC');
    }

    /**
     * Get agencies for DataTables with search and pagination
     */
    public function getDatatableData($request)
    {
        $builder = $this->builder();
        
        // Handle search
        if (!empty($request['search']['value'])) {
            $searchValue = $request['search']['value'];
            $builder->groupStart()
                    ->like('name', $searchValue)
                    ->orLike('description', $searchValue)
                    ->orLike('email', $searchValue)
                    ->orLike('address', $searchValue)
                    ->groupEnd();
        }

        // Handle ordering
        if (!empty($request['order'])) {
            $columnIndex = $request['order'][0]['column'];
            $columnName = $request['columns'][$columnIndex]['data'];
            $columnSortOrder = $request['order'][0]['dir'];
            
            // Map column names to actual database columns
            $columnMap = [
                'name' => 'name',
                'email' => 'email',
                'is_active' => 'is_active',
                'created_at' => 'created_at'
            ];
            
            if (isset($columnMap[$columnName])) {
                $builder->orderBy($columnMap[$columnName], $columnSortOrder);
            }
        } else {
            $builder->orderBy('created_at', 'DESC');
        }

        // Get total records count (before filtering)
        $totalRecords = $this->countAll();

        // Get filtered records count
        $filteredRecords = $builder->countAllResults(false);

        // Handle pagination
        if (isset($request['start']) && isset($request['length'])) {
            if ($request['length'] != -1) {
                $builder->limit($request['length'], $request['start']);
            }
        }

        $data = $builder->get()->getResultArray();

        return [
            'draw' => intval($request['draw']),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ];
    }

    /**
     * Get active offices for dropdowns
     */
    public function getActiveOffices()
    {
        return $this->where('is_active', 1)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get office with user count
     */
    public function getOfficeWithUserCount($id)
    {
        $office = $this->find($id);
        if ($office) {
            // Count users assigned to this agency
            $userCount = $this->db->table('users')
                                  ->where('office_id', $id)
                                  ->countAllResults();
            $office['user_count'] = $userCount;
        }
        return $office;
    }

    /**
     * Bulk update status
     */
    public function bulkUpdateStatus($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        return $this->whereIn('id', $ids)
                    ->set(['is_active' => $status])
                    ->update();
    }

    /**
     * Bulk delete agencies
     */
    public function bulkDelete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        // Check if any agency has users assigned
        $usersCount = $this->db->table('users')
                               ->whereIn('office_id', $ids)
                               ->countAllResults();

        if ($usersCount > 0) {
            return ['error' => 'Cannot delete offices that have users assigned to them.'];
        }

        $deleted = $this->whereIn('id', $ids)->delete();
        return ['success' => $deleted];
    }

    /**
     * Check if office can be deleted
     */
    public function canDelete($id)
    {
        $userCount = $this->db->table('users')
                              ->where('office_id', $id)
                              ->countAllResults();
        
        return $userCount === 0;
    }

    /**
     * Get agency statistics
     */
    public function getStatistics()
    {
        return [
            'total' => $this->countAll(),
            'active' => $this->where('is_active', 1)->countAllResults(false),
            'inactive' => $this->where('is_active', 0)->countAllResults(false),
        ];
    }
}
