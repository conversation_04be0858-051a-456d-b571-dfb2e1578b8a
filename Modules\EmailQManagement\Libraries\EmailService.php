<?php

namespace Modules\EmailQManagement\Libraries;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    protected $mail;

    public function __construct()
    {
        $this->mail = new PHPMailer(true);

        // SMTP Setup
        $this->mail->isSMTP();
        $this->mail->Host       = getenv('email.SMTPHost');
        $this->mail->SMTPAuth   = getenv('email.SMTPAuth') === 'true';
        $this->mail->SMTPAutoTLS = getenv('email.SMTPAutoTLS') === 'true';
        $this->mail->Username   = getenv('email.SMTPUser');
        $this->mail->Password   = getenv('email.SMTPPass');
        $this->mail->Port       = getenv('email.SMTPPort') ?: 587;

        // TLS/SSL
        $crypto = strtolower(getenv('email.SMTPCrypto'));
        if ($crypto === 'ssl') {
            $this->mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($crypto === 'tls') {
            $this->mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        }

        // Sender info
        $fromEmail = getenv('email.fromEmail');
        $fromName  = getenv('email.fromName') ?: 'App Mailer';

        // $this->mail->setFrom($fromEmail, $fromName);
        $this->mail->setFrom($fromEmail, 'Queue Mailer');
        $this->mail->isHTML(true);
    }

    public function sendTemplatedEmail(array $data): bool|string
    {
        try{
            $template      = $data['template'] ?? 'default';
            $templateData  = json_decode($data['template_data'] ?? '', true) ?? [];

            //$template = 'test'; // without .php
            $templatePath = ROOTPATH . "Modules/EmailQManagement/Views/templates/{$template}.html";

            if (!file_exists($templatePath)) {

                return "Template '{$template}.html' not found.";
            }
            
            $templateHtml = file_get_contents($templatePath);
            $html = $this->parseTemplatePlaceholders($templateHtml, $templateData);

            $this->mail->clearAllRecipients();
            $this->mail->addAddress($data['to_email']);
            $this->mail->Subject = $data['subject'] ?? '(No Subject)';
            $this->mail->Body    = $html;
            $this->mail->AltBody = strip_tags($html);

            $this->mail->send();
            return true;

        }catch(\Exception $e){
            return 'Mailer Error: ' . $e->getMessage();
        }
    }

    /**
     * Replace {{placeholders}} in HTML template safely
     * Escape to prevent XSS
     */
    public function parseTemplatePlaceholders(string $html, array $data): string
    {
        $allowHtml = ['message']; // whitelist keys allowed to have HTML

        foreach ($data as $key => $value) {
            $replaceValue = in_array($key, $allowHtml)
                ? $this->cleanHtml($value) // allow basic HTML
                : htmlspecialchars($value, ENT_QUOTES, 'UTF-8');

            $html = str_replace('{{' . $key . '}}', $replaceValue, $html);
        }

        return $html;
    }

    protected function cleanHtml($value): string
    {
        return strip_tags($value, '<b><i><strong><br><p><ul><li><a>');
    }
    
}
