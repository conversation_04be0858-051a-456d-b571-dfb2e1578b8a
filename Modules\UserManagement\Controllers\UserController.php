<?php

namespace Modules\UserManagement\Controllers;

use App\Controllers\AdminController;
use Modules\UserManagement\Models\UserModel;
use Modules\RoleManagement\Models\RoleModel;
use Modules\RoleManagement\Models\UserRoleModel;
use Modules\OfficeManagement\Models\OfficeModel;
use Modules\UserManagement\Config\Validation;
use \Hermawan\DataTables\DataTable;

class UserController extends AdminController
{
    protected $userModel;
    protected $roleModel;
    protected $officeModel;
    protected $userRoleModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->officeModel = new OfficeModel();
        $this->userRoleModel = new UserRoleModel();
    }

    public function index()
    {
        if (!hasPermission('user.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage users.');
        }

        $data['title'] = 'User Management';
        $data['page_title'] = 'User Management';
        $data['statistics'] = $this->userModel->getStatistics();
        return view('Modules\UserManagement\Views\users\index', $data);
    }

    public function create()
    {
        if (!hasPermission('user.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage users.');
        }
        $data['title'] = 'Manage Users';
        $data['page_title'] = 'User Management';
        $roleModel = new RoleModel();
        $officeModel = new OfficeModel();
        $data['roles'] = $roleModel->findAll();
        $data['offices'] = $officeModel->where('is_active', 1)->findAll();
        return view('Modules\UserManagement\Views\users\create', $data);
    }

    public function store()
    {
        $validation = new Validation();

        // Validate the input
        if(!$this->validate($validation->user, $validation->user_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Validate role selection
        $roleIds = $this->request->getPost('role_ids');
        if (empty($roleIds) || !is_array($roleIds)) {
            return redirect()->back()->withInput()->with('errors', ['role_ids' => 'Please select at least one role']);
        }

        $userModel = new UserModel();
        $data = $this->request->getPost();

        // Remove role_ids from user data as it's handled separately
        unset($data['role_ids']);

        // Insert user
        $userId = $userModel->insert($data);

        if ($userId) {
            // Assign roles to user
            $userModel->setUserRoles($userId, $roleIds, session()->get('id'));
            return redirect()->route('users.index')->with('success', 'User created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create user.');
        }
    }

    public function edit($id = null)
    {
        if (!hasPermission('user.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage users.');
        }
        $data['title'] = 'Manage Users';
        $data['page_title'] = 'User Management';
        $userModel = new UserModel();
        $roleModel = new RoleModel();
        $officeModel = new OfficeModel();

        // Get user with roles
        $user = $userModel->find($id);
        if (!$user) {
            return redirect()->route('users.index')->with('error', 'User not found.');
        }

        $user['roles'] = $this->userRoleModel->getUserRolesList($id);
        $data['user'] = $user;
        $data['roles'] = $roleModel->findAll();
        $data['offices'] = $officeModel->findAll();
        return view('Modules\UserManagement\Views\users\edit', $data);
    }

    public function update($id)
    {
        $validation = new Validation();

        // Validate the input
        if(!$this->validate($validation->user, $validation->user_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Validate role selection
        $roleIds = $this->request->getPost('role_ids');
        if (empty($roleIds) || !is_array($roleIds)) {
            return redirect()->back()->withInput()->with('errors', ['role_ids' => 'Please select at least one role']);
        }

        $userModel = new UserModel();
        $data = $this->request->getPost();

        // Remove role_ids from user data as it's handled separately
        unset($data['role_ids']);

        if (!empty($data['password'])) {
            // $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;

        if(!$userModel->update($id, $data)){
            return redirect()->back()->withInput()->with('errors', ['Error updating user']);
        }

        // Update user roles
        $this->userRoleModel->setUserRoles($id, $roleIds, session()->get('user_id'));

        return redirect()->route('users.index')->with('success', 'User updated successfully.');
    }

    public function delete($id)
    {
        // if (!hasPermission('user.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage users.');
        // }

        if ($this->userModel->delete($id)) {
            return redirect()->to(base_url('admin/users'))->with('success', 'User deleted successfully.');
        } else {
            return redirect()->to(base_url('admin/users'))->with('error', 'Could not delete user.');
        }
    }

    /**
     * Show user details via AJAX
     */
    public function show($id)
    {
        // if (!hasPermission('user.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $user = $this->userModel->getUserWithRoleAndOffice($id);

        if (!$user) {
            return $this->response->setJSON(['error' => 'User not found']);
        }

        return $this->response->setJSON(['success' => true, 'user' => $user]);
    }

    /**
     * AJAX delete user
     */
    public function ajaxDelete()
    {
        // if (!hasPermission('user.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $id = $this->request->getPost('id');

        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid user ID']);
        }

        // Check if user exists
        $user = $this->userModel->find($id);
        if (!$user) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not found']);
        }

        // Prevent deletion of current user
        if ($id == session()->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete your own account']);
        }

        if ($this->userModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'User deleted successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete user']);
    }

    /**
     * DataTables AJAX endpoint - Hermawan DataTables
     */
    public function datatable()
    {
        // if (!hasPermission('user.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $builder = $this->userModel->getUsersWithRoleAndOfficeBuilder();
        //print_r($builder);
        //print_r($builder->get()->getResultArray());
        // $builder = $this->userModel->table('users');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex') // Add row numbering
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->edit('role_names', function($row) {
                $badges = '';
                $roles = explode(', ', $row->role_names);
                foreach ($roles as $role) {
                    $badges .= '<span class="badge badge-info">' . esc($role) . '</span> ';
                }
                return $badges;
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                //must have a user.manage permission to see actions
                if (hasPermission('user.manage')) {
                    $actions .= '<div class="btn-group" role="group">';

                    $actions .= '<button type="button" class="btn btn-info btn-sm" onclick="viewUser(' . $row->id . ')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>';

                    $actions .= '<a href="' . site_url('admin/users/edit/' . $row->id) . '" class="btn btn-warning btn-sm" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

                    if(hasPermission('hide')){
                    $actions .= '<a href="' . site_url('admin/users/assign-roles/' . $row->id) . '" class="btn btn-success btn-sm" title="Assign Roles">
                        <i class="fas fa-user-tag"></i>
                    </a>';
                    }

                    $actions .= '<button id="deleteUser" type="button" class="btn btn-danger btn-sm" title="Delete" data-id="'. $row->id.'">
                        <i class="fas fa-trash"></i>
                    </button>';

                    $actions .= '</div>';
                }
                return $actions;
            })
            ->toJson(true); // Allow HTML in columns
    }

    /**
     * Bulk delete users
     */
    public function bulkDelete()
    {
        // if (!hasPermission('user.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No users selected']);
        }

        // Prevent deletion of current user
        $currentUserId = session()->get('user_id');
        if (in_array($currentUserId, $ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete your own account']);
        }

        $result = $this->userModel->bulkDelete($ids);

        if (isset($result['error'])) {
            return $this->response->setJSON(['success' => false, 'message' => $result['error']]);
        }

        return $this->response->setJSON(['success' => true, 'message' => 'Selected users deleted successfully']);
    }

    /**
     * Bulk activate users
     */
    public function bulkActivate()
    {
        // if (!hasPermission('user.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No users selected']);
        }

        if ($this->userModel->bulkUpdateStatus($ids, 1)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected users activated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate users']);
    }

    /**
     * Bulk deactivate users
     */
    public function bulkDeactivate()
    {
        // if (!hasPermission('user.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No users selected']);
        }

        if ($this->userModel->bulkUpdateStatus($ids, 0)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected users deactivated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate users']);
    }
}
