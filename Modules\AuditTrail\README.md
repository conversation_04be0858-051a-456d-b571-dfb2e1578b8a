# Audit Trail Module

This module provides comprehensive audit trail functionality for tracking user actions and system events with a modern web interface.

## Features

- **Complete Activity Tracking**: Track user actions (create, update, delete, login, logout, view, bulk operations)
- **Detailed Context**: Store IP address, user agent, URL, HTTP method, and request data
- **Data Change Tracking**: JSON storage for old and new values with detailed comparison
- **Automatic Logging**: Use AuditableTrait for automatic model event logging
- **Modern Web Interface**: DataTables-powered interface with statistics, charts, and export functionality
- **Advanced Filtering**: Search and filter audit logs by user, event type, entity, date range
- **Export Capabilities**: Export audit logs to CSV with date filtering
- **Cleanup Tools**: Automated cleanup of old audit logs with configurable retention
- **Statistics Dashboard**: Visual charts showing event distribution and entity activity
- **Permission-Based Access**: Integrated with your permission system

## Installation

1. Run the migration to create the audit_logs table:
   ```bash
   php spark migrate -n Modules\\AuditTrail
   ```

2. Load the helper in your controllers or autoload it:
   ```php
   helper('audit_logger');
   ```

3. Access the audit trail interface at: `/admin/audit`

## Usage

### Manual Logging

```php
// Basic event logging
audit_log('user_login', 'User', $userId);

// Log with data changes
audit_update('User', $userId, $oldData, $newData);

// Log bulk operations
audit_bulk_operation('delete', 'User', [1, 2, 3], ['reason' => 'Cleanup']);

// Log file operations
audit_file_operation('upload', 'document.pdf', ['size' => 1024, 'type' => 'pdf']);

// Log system events
audit_system_event('backup_completed', ['duration' => '5 minutes']);

// Log failed login attempts
audit_failed_login('john_doe', 'Invalid password');

// Log permission/role changes
audit_permission_change('granted', $userId, ['user.create', 'user.edit']);
audit_role_change('assigned', $userId, ['admin', 'moderator']);
```

### Automatic Logging with AuditableTrait

Add the AuditableTrait to your models for automatic logging:

```php
use Modules\AuditTrail\Libraries\AuditableTrait;

class UserModel extends Model
{
    use AuditableTrait;

    // Optional: Customize auditable events
    protected $auditableEvents = ['create', 'update', 'delete'];

    // Optional: Specify fields to audit (empty = all fields)
    protected $auditableFields = ['username', 'email', 'status'];

    // Optional: Exclude fields from auditing
    protected $auditableExcludeFields = ['password', 'remember_token'];

    // Your model code...
}
```

### Advanced Trait Configuration

```php
// Enable/disable specific events
$userModel->setAuditableEvents(['create', 'update']);

// Set specific fields to audit
$userModel->setAuditableFields(['username', 'email', 'status']);

// Set fields to exclude
$userModel->setAuditableExcludeFields(['password', 'created_at', 'updated_at']);

// Get audit logs for a specific entity
$logs = $userModel->getAuditLogs($userId, 100);
```

### Retrieving Audit Data

```php
// Get logs for a specific entity
$entityLogs = get_audit_entity_logs('User', $userId, 50);

// Get logs for a specific user
$userLogs = get_audit_user_logs($userId, 100);

// Get recent logs
$recentLogs = get_audit_recent_logs(200);

// Get statistics
$stats = get_audit_statistics();
```

## Web Interface Features

### Dashboard
- **Statistics Cards**: Total logs, today's activity, monthly activity, event types
- **Visual Charts**: Event distribution (doughnut chart) and entity activity (bar chart)
- **Real-time Updates**: Statistics update automatically after operations

### Data Table
- **Server-side Processing**: Efficient handling of large datasets
- **Advanced Search**: Search across all fields including user names and event types
- **Responsive Design**: Works on desktop and mobile devices
- **Sortable Columns**: Click column headers to sort data
- **Pagination**: Navigate through large datasets easily

### Audit Details Modal
- **Complete Information**: View all audit trail details in a popup modal
- **Data Comparison**: Side-by-side view of old vs new values
- **Formatted Display**: JSON data is formatted for easy reading
- **User Context**: Shows user information and session details

### Export & Management
- **CSV Export**: Export filtered data with date range selection
- **Bulk Cleanup**: Remove old audit logs with configurable retention period
- **Safety Warnings**: Confirmation dialogs for destructive operations

## Database Schema

The `audit_logs` table structure:

| Column | Type | Description |
|--------|------|-------------|
| id | INT PRIMARY KEY | Unique identifier |
| user_id | INT | ID of user who performed action |
| event | VARCHAR(50) | Type of event (create, update, delete, etc.) |
| auditable_type | VARCHAR(100) | Entity type affected |
| auditable_id | INT | ID of affected entity |
| old_values | JSON | Previous values (for updates/deletes) |
| new_values | JSON | New values (for creates/updates) |
| ip_address | VARCHAR(45) | User's IP address |
| user_agent | TEXT | Browser user agent |
| url | TEXT | Request URL |
| method | VARCHAR(10) | HTTP method (GET, POST, etc.) |
| created_at | TIMESTAMP | When action occurred |

## Permissions

The module integrates with your permission system:

- `audit.view` - View audit trails
- `audit.export` - Export audit data
- `audit.manage` - Cleanup old logs and advanced management

## Configuration

### Retention Policy
Configure automatic cleanup in your scheduled tasks:

```php
// Clean logs older than 1 year
$auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
$deleted = $auditLogger->cleanOldLogs(365);
```

### Custom Event Types
You can log any custom event types:

```php
audit_log('custom_event', 'CustomEntity', $entityId, $oldData, $newData);
```

## Best Practices

1. **Use Descriptive Event Names**: Use clear, consistent event naming
2. **Include Relevant Context**: Add meaningful data to help with debugging
3. **Regular Cleanup**: Implement automated cleanup to manage database size
4. **Monitor Performance**: Large audit tables may need indexing optimization
5. **Security Considerations**: Ensure sensitive data is not logged in plain text

## Troubleshooting

### Common Issues

1. **Trait Not Working**: Ensure the trait is properly imported and callbacks are enabled
2. **Permission Errors**: Check that users have appropriate audit permissions
3. **Large Dataset Performance**: Consider adding database indexes for frequently queried fields
4. **Memory Issues**: Use pagination and cleanup old logs regularly

### Debug Mode
Enable debug logging to troubleshoot issues:

```php
log_message('debug', 'Audit trail debug info');
```