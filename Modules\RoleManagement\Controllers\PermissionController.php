<?php

namespace Modules\RoleManagement\Controllers;

use App\Controllers\BaseController;
use Modules\RoleManagement\Models\RoleModel;
use Modules\RoleManagement\Models\PermissionModel;
use Modules\RoleManagement\Models\RolePermissionModel;
use Modules\Authentication\Services\PermissionService;
use Modules\RoleManagement\Config\Validation;
use \Hermawan\DataTables\DataTable;

class PermissionController extends BaseController
{
    protected $roleModel;
    protected $permissionModel;
    protected $rolePermissionModel;
    protected $permissionService;

    public function __construct()
    {
        $this->roleModel = new RoleModel();
        $this->permissionModel = new PermissionModel();
        $this->rolePermissionModel = new RolePermissionModel();
        $this->permissionService = new PermissionService();
    }

    //use
    public function index()
    {
        if (!hasPermission('permission.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage permissions.');
        }
        $data['title'] = 'Manage Permissions';
        $data['page_title'] = 'Permission Management';
        $data['permissions'] = $this->permissionModel->findAll();
        if (hasPermission('permission.dashboard')) {
            $data['stats'] = $this->permissionModel->getStatistics();
        }
        return view('Modules\RoleManagement\Views\permissions\index', $data);
    }

    //use
    public function create()
    {
        if (!hasPermission('permission.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage permissions.');
        }
        $data['title'] = 'Manage Permissions';
        $data['page_title'] = 'Permission Management';
        // $data['roles'] = $this->roleModel->findAll();
        $data['categories'] = $this->permissionModel->getDistinctCategories();

        return view('Modules\RoleManagement\Views\permissions\create', $data);
    }

    //use
    public function store()
    {
        
        $validation = new Validation();
        if(!$this->validate($validation->create_permission, $validation->create_permission_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'category' => $this->request->getPost('category'),
        ];

        if(!$this->permissionModel->insert($data)){
            return redirect()->back()->withInput()->with('error', 'Failed to create permission.');
        }
        return redirect()->route('permission.index')->with('success', 'Permission created successfully.');
    }

    //use
    public function edit($id)
    {
        // if (!hasPermission('permission.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage permissions.');
        // }
        // echo "edit";exit();

        $permission = $this->permissionModel->find($id);

        //get all categories to load in the view where ill loof using foreach
        $categories = $this->permissionModel->getDistinctCategories();
        if (empty($permission)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Permission not found: ' . $id);
        }

        $data = [
            'permission' => $permission,
            'categories' => $categories,
            'title' => 'Edit Permission',
            'page_title' => 'Edit Permission',
        ];

        $roleModel = new RoleModel();
        $data['role'] = $roleModel->find($id);
        return view('Modules\RoleManagement\Views\permissions\edit', $data);
    }

    public function update($id)
    {
        // if (!hasPermission('permission.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage permissions.');
        // }

        $validation = new Validation();
        $rules = $validation->update_permission;
        $rules['name'] = str_replace('{id}', $id, $rules['name']);
        if(!$this->validate($rules, $validation->update_permission_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'category' => $this->request->getPost('category'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
        ];

        $permissionModel = new PermissionModel();
        if(!$permissionModel->update($id, $data)){
            return redirect()->back()->withInput()->with('error', 'Failed to update permission.');
        }
        return redirect()->route('permission.index')->with('success', 'Permission updated successfully.');
    }

    public function delete($id)
    {
        // if (!hasPermission('permission.manage')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage permissions.');
        // }

        // Check if the permission can be deleted
        if (!$this->permissionModel->canDelete($id)) {
            return redirect()->to(site_url('admin/permissions'))->with('error', 'Cannot delete permission: It is currently assigned to roles.');
        }

        // Start a transaction for atomicity
        $this->permissionModel->db->transBegin();

        try {
            // Delete associated role_permissions
            $this->rolePermissionModel->where('permission_id', $id)->delete();
            
            // Finally, delete the permission itself
            $this->permissionModel->delete($id);

            $this->permissionModel->db->transCommit();
            return redirect()->to(site_url('admin/permissions'))->with('success', 'Permission deleted successfully.');
        } catch (\Exception $e) {
            $this->permissionModel->db->transRollback();
            return redirect()->to(site_url('admin/permissions'))->with('error', 'Failed to delete permission: ' . $e->getMessage());
        }
    }

    /**
     * DataTables AJAX endpoint
     */
    public function datatable()
    {
        // if (!hasPermission('permission.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $builder = $this->permissionModel->db->table('permissions');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex')
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->edit('name', function($row) {
                return '<strong>' . esc($row->name) . '</strong>';
            })
            ->edit('description', function($row) {
                return !empty($row->description) ? esc($row->description) : '<span class="text-muted">No description</span>';
            })
            ->add('category', function($row) {
                return !empty($row->category)
                    ? '<span class="badge badge-info">' . esc($row->category) . '</span>'
                    : '<span class="text-muted">No category</span>';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                // Temporarily disable permission check for debugging
                // if (hasPermission('permission.manage')) {
                    $actions .= '<button type="button" class="btn btn-info btn-sm me-1" onclick="viewPermission(' . $row->id . ')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>';
                    $actions .= '<a href="' . site_url('admin/permissions/edit/' . $row->id) . '" class="btn btn-warning btn-sm me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    $actions .= '<button id="deletePermission" type="button" class="btn btn-danger btn-sm" title="Delete" data-id="'. $row->id.'">
                        <i class="fas fa-trash"></i>
                    </button>';
                // }
                return $actions;
            })
            ->toJson(true);
    }

    /**
     * Get statistics for AJAX
     */
    public function stats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->permissionModel->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Bulk delete permissions
     */
    public function bulkDelete()
    {
        // if (!hasPermission('permission.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No permissions selected']);
        }

        // Check if any permissions are being used by roles
        $roleCount = $this->permissionModel->db->table('role_permissions')
                                             ->whereIn('permission_id', $ids)
                                             ->countAllResults();

        if ($roleCount > 0) {
            return $this->response->setJSON(['success' => false, 'message' => "Cannot delete permissions. They are assigned to $roleCount role(s)."]);
        }

        $result = $this->permissionModel->bulkDelete($ids);

        if (isset($result['error'])) {
            return $this->response->setJSON(['success' => false, 'message' => $result['error']]);
        }

        return $this->response->setJSON(['success' => true, 'message' => 'Selected permissions deleted successfully']);
    }

    /**
     * Bulk activate permissions
     */
    public function bulkActivate()
    {
        // if (!hasPermission('permission.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No permissions selected']);
        }

        if ($this->permissionModel->bulkUpdateStatus($ids, 1)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected permissions activated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate permissions']);
    }

    /**
     * Bulk deactivate permissions
     */
    public function bulkDeactivate()
    {
        // if (!hasPermission('permission.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $ids = $this->request->getPost('ids');

        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No permissions selected']);
        }

        if ($this->permissionModel->bulkUpdateStatus($ids, 0)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected permissions deactivated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate permissions']);
    }

    /**
     * AJAX delete permission
     */
    public function ajaxDelete()
    {
        // if (!hasPermission('permission.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $id = $this->request->getPost('id');

        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid permission ID']);
        }

        // Check if permission is being used by roles
        $roleCount = $this->permissionModel->db->table('role_permissions')
                                             ->where('permission_id', $id)
                                             ->countAllResults();

        if ($roleCount > 0) {
            return $this->response->setJSON(['success' => false, 'message' => "Cannot delete permission. It is assigned to $roleCount role(s)."]);
        }

        if ($this->permissionModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Permission deleted successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete permission']);
    }

    /**
     * Show permission details for modal view
     */
    public function show($id)
    {
        // if (!hasPermission('permission.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $permission = $this->permissionModel->find($id);
        if (empty($permission)) {
            return $this->response->setJSON(['error' => 'Permission not found']);
        }

        // Get roles that have this permission
        $roles = $this->permissionModel->db->table('role_permissions rp')
                                          ->select('r.id, r.name, r.description')
                                          ->join('roles r', 'r.id = rp.role_id')
                                          ->where('rp.permission_id', $id)
                                          ->get()
                                          ->getResultArray();

        $data = [
            'permission' => $permission,
            'roles' => $roles
        ];

        return view('Modules\RoleManagement\Views\permissions\show', $data);
    }
}
