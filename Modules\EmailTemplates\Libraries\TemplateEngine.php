<?php

namespace Modules\EmailTemplateManager\Libraries;

use Modules\EmailTemplates\Config\EmailTemplates as EmailTemplatesConfig;
use Modules\EmailTemplates\Models\EmailTemplateModel;

class TemplateEngine
{
    protected EmailTemplateModel $templateModel;
    protected EmailTemplatesConfig $config;

    public function __construct()
    {
        $this->templateModel = new EmailTemplateModel();
        $this->config = new EmailTemplatesConfig();
    }

    public function processTemplate(string $templateId, array $data=[]):
    {
        $template = $this->templateModel->find($templateId);

        if (!$template) {
            throw new \Exception('Template not found');
        }

        $html = $template['html_content'];
        $subject = $template['subject'];

        // foreach ($data as $key => $value) {
        //     $html = str_replace('{{' . $key . '}}', $value, $html);
        //     $subject = str_replace('{{' . $key . '}}', $value, $subject);
        // }

        // Process subject line
        $processedSubject = $this->replacePlaceholders($template['subject'], $allPlaceholders);
        
        // Process HTML content
        $processedHtml = $this->replacePlaceholders($htmlContent, $allPlaceholders);

        return [
            'template_id' => $template['id'],
            'subject' => $subject,
            'html' => $html,
            'placeholders_used' => $allPlaceholders
        ];
    }

    /**
     * Send email using template
     */
    public function sendEmail(string $templateSlug, string $toEmail, array $placeholders = [], ?string $toName = null): bool
    {
        try {
            // Process template
            $processedTemplate = $this->processTemplate($templateSlug, $placeholders);
            
            // Get email service
            $email = \Config\Services::email();
            
            // Configure email
            $fromEmail = $this->config->emailConfig['from_email'] ?? env('email.fromEmail');
            $fromName = $this->config->emailConfig['from_name'] ?? env('email.fromName');
            
            $email->setFrom($fromEmail, $fromName);
            $email->setTo($toEmail, $toName);
            $email->setSubject($processedTemplate['subject']);
            $email->setMessage($processedTemplate['html']);
            
            if ($this->config->emailConfig['reply_to']) {
                $email->setReplyTo($this->config->emailConfig['reply_to']);
            }
            
            // Send email
            $result = $email->send();
            
            // Log usage
            // $this->logUsage(
            //     $processedTemplate['template_id'],
            //     $toEmail,
            //     $toName,
            //     $processedTemplate['subject'],
            //     $processedTemplate['placeholders_used'],
            //     $result ? 'sent' : 'failed',
            //     $result ? null : $email->printDebugger()
            // );
            
            return $result;
            
        } catch (\Exception $e) {
            // Log error
            if (isset($processedTemplate['template_id'])) {
                $this->logUsage(
                    $processedTemplate['template_id'],
                    $toEmail,
                    $toName,
                    $processedTemplate['subject'] ?? 'Error processing template',
                    $placeholders,
                    'failed',
                    $e->getMessage()
                );
            }
            
            throw $e;
        }
    }
    



    private function getTemplatePlaceholders(string $templateSlug): array
    {
        return $this->config->templatePlaceholders[$templateSlug] ?? [];
    }

    public function previewTemplate(string $templateSlug, array $placeholders = []): array
    {
        return $this->processTemplate($templateSlug, $placeholders);
    }

    public function validatePlaceholders(string $templateSlug, array $placeholders): array
    {
        $template = $this->templateModel->getBySlug($templateSlug);
        if (!$template) {
            throw new \RuntimeException('Template not found: ' . $templateSlug);
        }
        
        $requiredPlaceholders = $template['placeholders'] ?? [];
        $errors = [];
        
        foreach ($requiredPlaceholders as $placeholder) {
            if ($placeholder['required'] && !isset($placeholders[$placeholder['key']])) {
                $errors[] = "Required placeholder '{$placeholder['key']}' is missing";
            }
        }
        
        return $errors;
    }

    public function getAvailablePlaceholders(string $templateSlug): array
    {
        $defaultPlaceholders = $this->config->defaultPlaceholders;
        $templatePlaceholders = $this->config->templatePlaceholders[$templateSlug] ?? [];
        
        return array_merge($defaultPlaceholders, $templatePlaceholders);
    }

    protected function replacePlaceholders(string $content, array $placeholders): string
    {
        foreach ($placeholders as $key => $value) {
            // Support both {{key}} and {key} formats
            $content = str_replace(['{{'.$key.'}}', '{'.$key.'}'], $value, $content);
        }
        
        // Remove any remaining unreplaced placeholders
        $content = preg_replace('/\{\{[^}]+\}\}/', '', $content);
        $content = preg_replace('/\{[^}]+\}/', '', $content);
        
        return $content;
    }




}
