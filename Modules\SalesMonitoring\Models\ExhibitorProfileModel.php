<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;

use Config\TableNames;
use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class ExhibitorProfileModel extends Model
{
    protected $DBGroup = 'default';
    protected $table;
    protected $primaryKey       = 'ff_code';
    protected $useAutoIncrement = true;
    protected $returnType       = \Modules\SalesMonitoring\Entities\ExhibitorProfile::class;
    protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = ["ff_code","acct_no","co_name","brand_name","description","registration_no","y_estab","y_export","cont_per_ln","cont_per_fn","mi","title","salutation","gender","age_group","date_apply","date_input","country","continent","states","region","area","province","add_st","add_city","zipcode","tel_off_country","tel_off_area","tel_off","fax","co_mobile","owner_mobile","owner_email","co_email","webpage","remarks","remarks_date","deleted","remarks1","edited","fair_code","stand_name","tin","facebook","twitter","instagram","youtube","linkedin","tiktok","behance","username","userdesc","logo","sector","membership_assoc","password","rights","forgotten_password_code","forgotten_password_time","last_login","active","slug","image","youtube_id","details","level","foldername","messaging_app","ffCode_Old","coEmail_orig","quickEmail_status","fameplus_id","SiteMembership","marketing_status","email_status","email_date_status","social_others","portfolio","ff_code","acct_no","co_name","brand_name","description","registration_no","y_estab"];

    // protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    // protected $useTimestamps = false;
    // protected $dateFormat    = 'datetime';
    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];


    // protected $contactFields    = [
    //     'rep_code as id','co_name as company_name','cont_per_fn as first_name','cont_per_ln as last_name','mi as middle_initial','email','position','gender','country','continent','region','province','add_st','add_city','tel_off','webpage','mobile','facebook','twitter','instagram','pinterest','weibo','linkedin',
    // ];

    protected $contactAttendanceFields    = [
        'a.ff_code','a.co_name','cont_per_fn as first_name','cont_per_ln as last_name','co_email','add_st','add_city','country','province','zipcode','region','b.fair_code','b.fair_stat_app','b.fair_stat_part','b.foreignlocal','CONCAT(add_st," ",add_city," ",province) as address','webpage'
    ];

    protected $logger;
    protected TableNames $tables;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
        $this->tables = new TableNames();
        $this->table = $this->tables->exhibitor_profile;
    }


    public function findById($id){
        return $this->find($id);
    }

    public function getContactByEmail($email){
        return $this->where('co_email', $email)->first();
    }


    public function getContactAttendance1($ff_code,$fair_code){
        try{
            $builder = $this->db->table($this->table.' a')
            ->select($this->contactAttendanceFields)
            ->join('e_attendance b','a.ff_code=b.ff_code')
            ->where(['b.fair_code'=>$fair_code,'a.ff_code'=>$ff_code]);
            
            $query = $builder->get();
            $results['data'] = $query->getRow();
            $sql = $this->db->getLastQuery()->getQuery();
            $results['sql'] = str_replace("\n", " ", $sql);
            // echo $results['sql'];exit();
            return $results['data'];
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }

    public function getContactAttendance($ff_code,$fair_code){
        try{
            $builder = $this->db->table($this->table.' a')
            ->select($this->contactAttendanceFields)
            ->join($this->tables->exh_attendance.' b','a.ff_code=b.ff_code','inner')
            ->where(['b.fair_code'=>$fair_code,'a.ff_code'=>$ff_code]);
            
            $query = $builder->get();
            $results['data'] = $query->getFirstRow();
            $sql = $this->db->getLastQuery()->getQuery();
            $results['sql'] = str_replace("\n", " ", $sql);
            // echo $results['sql'];exit();
            return $results['data'];
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }




    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }













}
