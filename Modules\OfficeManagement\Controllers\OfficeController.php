<?php

namespace Modules\OfficeManagement\Controllers;

use App\Controllers\AdminController;
use Modules\OfficeManagement\Models\OfficeModel;
use Modules\OfficeManagement\Config\Validation;
use \Hermawan\DataTables\DataTable;

class OfficeController extends AdminController
{
    protected $officeModel;

    public function __construct()
    {
        $this->officeModel = new OfficeModel();
    }

    public function index()
    {
        // if (!hasPermission('office.manage')) {
        //     return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        // }


        $data['title'] = 'Office Management';
        $data['page_title'] = 'Office Management';
        $data['statistics'] = $this->officeModel->getStatistics();
        return view('Modules\OfficeManagement\Views\index', $data);
    }

    public function create()
    {
        if (!hasPermission('office.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        }

        $data['title'] = 'Create Office';
        $data['page_title'] = 'Create New Office';
        
        return view('Modules\OfficeManagement\Views\create', $data);
    }

    public function store()
    {
        if (!hasPermission('office.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        }

        $data = $this->request->getPost();
        
        // Validate the input
        $validation = new Validation();
        if(!$this->validate($validation->create_office, $validation->create_office_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        

        // Set default values
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;

        if ($this->officeModel->insert($data)) {
            return redirect()->route('offices.index')->with('success', 'Office created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create office.');
        }
    }

    public function edit($id = null)
    {
        if (!hasPermission('office.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        }

        $office = $this->officeModel->find($id);
        
        if (!$office) {
            return redirect()->route('offices.index')->with('error', 'Office not found.');
        }

        $data['office'] = $office;
        $data['title'] = 'Edit Office';
        $data['page_title'] = 'Edit Office: ' . $office['name'];
        
        return view('Modules\OfficeManagement\Views\edit', $data);
    }

    public function update($id = null)
    {
        if (!hasPermission('office.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        }

        $office = $this->officeModel->find($id);
        
        if (!$office) {
            return redirect()->route('agencies.index')->with('error', 'Agency not found.');
        }

        $data = $this->request->getPost();
        $data['id'] = $id;

        // Load validation rules and add the ID for unique validation
        $validation = new Validation();
        $rules = $validation->update_office;

        // Replace {id} placeholder in the unique rule
        $rules['name'] = str_replace('{id}', $id, $rules['name']);
        $rules['email'] = str_replace('{id}', $id, $rules['email']);
        $rules['code'] = str_replace('{id}', $id, $rules['code']);
        if(!$this->validate($rules, $validation->update_office_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Set default values
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;

        if ($this->officeModel->update($id, $data)) {
            return redirect()->route('offices.index')->with('success', 'Office updated successfully.');
        } else {
            // return redirect()->back()->withInput()->with('error', 'Failed to update agency.');
            return view('Modules\OfficeManagement\Views\edit', ['office' => $office,
            'title' => 'Edit Office',
            'page_title' => 'Edit Office: ' . $office['name'],
            'error' => $this->officeModel->errors(),
            'oldInput' => $data]);
        }
    }

    public function delete($id = null)
    {
        if (!hasPermission('office.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage offices.');
        }

        $office = $this->officeModel->find($id);
        
        if (!$office) {
            return redirect()->route('agencies.index')->with('error', 'Office not found.');
        }

        // Check if agency can be deleted
        if (!$this->officeModel->canDelete($id)) {
            return redirect()->route('offices.index')->with('error', 'Cannot delete office. There are users assigned to this office.');
        }

        if ($this->officeModel->delete($id)) {
            return redirect()->route('offices.index')->with('success', 'Office deleted successfully.');
        } else {
            return redirect()->route('offices.index')->with('error', 'Failed to delete office.');
        }
    }

    public function show($id = null)
    {
        // if (!hasPermission('office.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $office = $this->officeModel->getOfficeWithUserCount($id);
        $html = view('Modules\OfficeManagement\Views\show', ['office' => $office]);
        
        if (!$office) {
            return $this->response->setJSON(['error' => 'Office not found']);
        }

        return $this->response->setJSON(['success' => true, 'data' => $office, 'html' => $html]);
    }

    /**
     * DataTables AJAX endpoint using Hermawan DataTables
     */
    public function datatable()
    {
        if (!hasPermission('office.view')) {
            return $this->response->setJSON(['error' => 'Permission denied']);
        }

        $builder = $this->officeModel->db->table('offices');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex') // Add row numbering
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                if (hasPermission('office.manage')) {
                    $actions .= '<button type="button" class="btn btn-info btn-sm me-1" onclick="viewOffice(' . $row->id . ')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>';
                    $actions .= '<a href="' . site_url('admin/offices/edit/' . $row->id) . '" class="btn btn-warning btn-sm me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    $actions .= '<button id="deleteOffice" type="button" class="btn btn-danger btn-sm" title="Delete" data-id="'. $row->id.'">
                        <i class="fas fa-trash"></i>
                    </button>';
                }
                return $actions;
            })
            ->toJson(true); // Allow HTML in columns
    }

    /**
     * AJAX delete endpoint
     */
    public function ajaxDelete()
    {
        if (!hasPermission('office.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $id = $this->request->getPost('id');
        $office = $this->officeModel->find($id);
        
        if (!$office) {
            return $this->response->setJSON(['success' => false, 'message' => 'Office not found']);
        }

        // Check if office can be deleted
        if (!$this->officeModel->canDelete($id)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete office. There are users assigned to this office.']);
        }

        if ($this->officeModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Office deleted successfully']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete office']);
        }
    }

    /**
     * Bulk delete agencies
     */
    public function bulkDelete()
    {
        if (!hasPermission('office.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No offices selected']);
        }

        $result = $this->officeModel->bulkDelete($ids);
        
        if (isset($result['error'])) {
            return $this->response->setJSON(['success' => false, 'message' => $result['error']]);
        }

        return $this->response->setJSON(['success' => true, 'message' => 'Selected offices deleted successfully']);
    }

    /**
     * Bulk activate agencies
     */
    public function bulkActivate()
    {
        if (!hasPermission('office.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No offices selected']);
        }

        if ($this->officeModel->bulkUpdateStatus($ids, 1)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected offices activated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate offices']);
    }

    /**
     * Bulk deactivate agencies
     */
    public function bulkDeactivate()
    {
        if (!hasPermission('office.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No offices selected']);
        }

        if ($this->officeModel->bulkUpdateStatus($ids, 0)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected offices deactivated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate offices']);
    }
}
