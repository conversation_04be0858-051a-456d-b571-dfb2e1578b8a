# Agency Management Module

A comprehensive agency management system for CodeIgniter 4 with Hermawan DataTables integration, AJAX functionality, and bulk operations.

## Features

- **CRUD Operations**: Full create, read, update, delete functionality
- **Hermawan DataTables**: Server-side processing with search, pagination, and sorting
- **AJAX Integration**: Asynchronous operations for better user experience
- **Bulk Operations**: Bulk activate, deactivate, and delete agencies
- **Permission-Based Access**: Integration with existing role-permission system
- **Responsive Design**: AdminLTE integration with mobile-friendly interface
- **Real-time Statistics**: Dashboard statistics for agency counts
- **Form Validation**: Client-side and server-side validation
- **Modal Views**: Quick view agency details in modal popup

## Installation

1. **Run Migrations**:
   ```bash
   php spark migrate -n "Modules\AgencyManagement"
   ```

2. **Seed Sample Data** (Optional):
   ```bash
   php spark db:seed "Modules\AgencyManagement\Database\Seeds\AgencySeeder"
   ```

## Database Schema

The module uses an `agencies` table with the following structure:

```sql
CREATE TABLE agencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    address VARCHAR(255),
    contact_person VARCHAR(100),
    contact_number VARCHAR(20),
    email VARCHAR(255) UNIQUE,
    is_active TINYINT(1) DEFAULT 1,
    created_at DATETIME,
    updated_at DATETIME
);
```

## Usage

### Accessing Agency Management

Navigate to `/admin/agencies` to access the agency management interface.

**Required Permissions**:
- `agency.view`: View agency information
- `agency.manage`: Full CRUD operations

### Features Overview

#### 1. **DataTables Integration**
- Server-side processing for large datasets
- Real-time search across all fields
- Sortable columns
- Responsive pagination
- Export functionality (CSV, Excel, PDF, Print)

#### 2. **AJAX Operations**
- View agency details in modal
- Delete agencies without page refresh
- Bulk operations with real-time feedback
- Form validation with instant feedback

#### 3. **Bulk Operations**
- Select multiple agencies with checkboxes
- Bulk activate/deactivate agencies
- Bulk delete with confirmation
- Real-time button state management

## API Endpoints

### Main Routes
- `GET /admin/agencies` - List all agencies
- `GET /admin/agencies/create` - Create agency form
- `POST /admin/agencies/store` - Store new agency
- `GET /admin/agencies/edit/{id}` - Edit agency form
- `POST /admin/agencies/update/{id}` - Update agency
- `GET /admin/agencies/delete/{id}` - Delete agency

### AJAX Endpoints
- `POST /admin/agencies/datatable` - DataTables data source
- `GET /admin/agencies/show/{id}` - Get agency details
- `POST /admin/agencies/ajax-delete` - AJAX delete
- `POST /admin/agencies/bulk-delete` - Bulk delete
- `POST /admin/agencies/bulk-activate` - Bulk activate
- `POST /admin/agencies/bulk-deactivate` - Bulk deactivate

## Model Methods

### AgencyModel

#### Key Methods

- `getDatatableData($request)`: Get paginated data for DataTables
- `getActiveAgencies()`: Get all active agencies for dropdowns
- `getAgencyWithUserCount($id)`: Get agency with user count
- `bulkUpdateStatus($ids, $status)`: Bulk update agency status
- `bulkDelete($ids)`: Bulk delete agencies with validation
- `canDelete($id)`: Check if agency can be deleted
- `getStatistics()`: Get agency statistics

## Controller Methods

### AgencyController

#### CRUD Methods
- `index()`: Display agency list with DataTables
- `create()`: Show create form
- `store()`: Store new agency
- `edit($id)`: Show edit form
- `update($id)`: Update agency
- `delete($id)`: Delete agency

#### AJAX Methods
- `datatable()`: DataTables AJAX endpoint
- `show($id)`: Get agency details for modal
- `ajaxDelete()`: AJAX delete endpoint
- `bulkDelete()`: Bulk delete agencies
- `bulkActivate()`: Bulk activate agencies
- `bulkDeactivate()`: Bulk deactivate agencies

## Frontend Features

### DataTables Configuration
```javascript
$('#agenciesTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
        url: 'agencies/datatable',
        type: 'POST'
    },
    columns: [
        { data: 'name' },
        { data: 'contact_person' },
        { data: 'email' },
        { data: 'status_badge' },
        { data: 'actions' }
    ]
});
```

### Bulk Operations
- Checkbox selection with "Select All" functionality
- Dynamic button state management
- Confirmation dialogs for destructive operations
- Real-time feedback with success/error messages

### Modal Integration
- Quick view agency details
- AJAX-loaded content
- Responsive design
- User count display

## Permissions

The module adds the following permissions:

- `agency.manage`: Full agency management access
- `agency.view`: View agency information

## Validation Rules

### Agency Model Validation
- `name`: Required, 3-255 characters, unique
- `description`: Optional, max 500 characters
- `address`: Optional, max 255 characters
- `contact_person`: Optional, max 100 characters
- `contact_number`: Optional, max 20 characters
- `email`: Optional, valid email, unique
- `is_active`: Optional, boolean (0 or 1)

## JavaScript Features

### Form Validation
- Real-time email validation
- Character counters for text areas
- Contact number formatting
- Required field validation

### User Experience
- Loading indicators during AJAX operations
- Success/error message display
- Confirmation dialogs for destructive actions
- Responsive button states

## Customization

### Styling
The module uses AdminLTE classes. Customize by overriding CSS:

```css
.agencies-table .status-badge {
    /* Custom status badge styles */
}
```

### DataTables Options
Modify DataTables configuration in the view:

```javascript
// Add custom buttons
buttons: ["copy", "csv", "excel", "pdf", "print", "colvis"]

// Change page length options
lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
```

## Integration with User Management

The agency system is designed to integrate with user management:

```php
// In UserModel, add agency relationship
public function getUsersWithAgency() {
    return $this->select('users.*, agencies.name as agency_name')
                ->join('agencies', 'agencies.id = users.agency_id', 'left')
                ->findAll();
}
```

## Troubleshooting

### DataTables Not Loading
1. Check AJAX endpoint permissions
2. Verify CSRF token configuration
3. Check browser console for JavaScript errors

### Bulk Operations Not Working
1. Verify checkbox selection
2. Check AJAX endpoint responses
3. Ensure proper permissions

### Form Validation Issues
1. Check validation rules in model
2. Verify form field names match model
3. Check for JavaScript validation conflicts

## Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Test AJAX functionality thoroughly

## License

This module follows the same license as the main application.
