<?= $this->extend('layout/main') ?> <!-- Assuming you have a main layout file -->

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Edit Profile</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile') ?>">My Profile</a></li>
                        <li class="breadcrumb-item active">Edit Profile</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title">Personal Information</h3>
                        </div>
                        <?= form_open('profile/update') ?>
                        <div class="card-body">

                            <?php if (session()->has('errors')): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <ul>
                                        <?php foreach (session('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            <?php endif; ?>

                            <?php if (session()->getFlashdata('message')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('message') ?>
                                </div>
                            <?php endif; ?>
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success"><?= session()->getFlashdata('success') ?></div>
                            <?php endif; ?>
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger"><?= session()->getFlashdata('error') ?></div>
                            <?php endif; ?>
                            <?php if (isset($validation)): ?>
                                <div class="alert alert-danger">
                                    <?= $validation->listErrors() ?>
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" class="form-control <?= isInvalid('username') ?>" id="username" name="username" value="<?= old('username', esc($user['username'])) ?>">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('username', session("errors")); ?>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="first_name">First Name</label>
                                <input type="text" class="form-control <?= isInvalid('first_name') ?>" id="first_name" name="first_name" value="<?= old('first_name', esc($user['first_name'])) ?>">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('first_name', session("errors")); ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="last_name">Last Name</label>
                                <input type="text" class="form-control <?= isInvalid('last_name') ?>" id="last_name" name="last_name" value="<?= old('last_name', esc($user['last_name'])) ?>">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('last_name', session("errors")); ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email address</label>
                                <input type="text" class="form-control <?= isInvalid('email') ?>" id="email" name="email" value="<?= old('email', esc($user['email'])) ?>">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('email', session("errors")); ?>
                                </div>
                            </div>
                            <hr>
                            <h4>Change Password (Optional)</h4>
                            <p class="text-muted">Leave blank if you don't want to change your password.</p>
                            <div class="form-group">
                                <label for="password">New Password</label>
                                <input type="password" class="form-control <?= isInvalid('password') ?>" id="password" name="password">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('password', session("errors")); ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">Confirm New Password</label>
                                <input type="password" class="form-control <?= isInvalid('confirm_password') ?>" id="confirm_password" name="confirm_password">
                                <div class="invalid-feedback">
                                    <?php echo show_validation_error('confirm_password', session("errors")); ?>
                                </div>
                            </div>

                        </div>
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                            <a href="<?= base_url('profile') ?>" class="btn btn-default float-right">Cancel</a>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>