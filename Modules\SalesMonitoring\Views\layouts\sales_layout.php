<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title', 'Sales Dashboard | Trade Expo') ?></title>
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">
    
    <!-- Tabler CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
    <!-- <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" rel="preload" as="image"> -->

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">

    <!-- SweetAlert2 for confirmations -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS for Sales Module -->
    <style>
        :root {
            --sales-primary: #206bc4;
            --sales-success: #2fb344;
            --sales-warning: #f59f00;
            --sales-danger: #d63384;
            --sales-info: #17a2b8;
        }
        
        .sales-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .sales-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .metric-icon {
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 1.5rem;
        }
        
        .navbar-brand-image {
            max-height: 2rem;
            width: auto;
        }

        .navbar {
            position: relative; /* Ensure a stacking context is created */
            z-index: 1060;
        }
                
        /* Drawer Styles */
        .sales-drawer {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            max-width: 600px;
            height: 100vh;
            background: white;
            z-index: 1050;
            transition: right 0.3s ease;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .sales-drawer.show {
            right: 0;
        }
        
        .sales-drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 1040;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .sales-drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .sales-drawer-header {
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        
        .sales-drawer-body {
            padding: 1.5rem;
        }
        
        .sales-drawer-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        
        /* Form Styles */
        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            background: #f8f9fa;
        }
        
        .form-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--sales-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .form-section-title .icon {
            margin-right: 0.5rem;
        }
        
        .currency-input {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-weight: 500;
            z-index: 2;
        }
        
        .currency-input input {
            padding-left: 2.5rem;
        }
        
        /* Status Badge Styles */
        .status-booked { background-color: var(--sales-success) !important; }
        .status-negotiation { background-color: var(--sales-warning) !important; }
        .status-considered { background-color: var(--sales-info) !important; }
        
        /* Sale Type Cards */
        .sale-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .sale-type-card:hover {
            border-color: var(--sales-primary);
            transform: translateY(-2px);
        }
        
        .sale-type-card.active {
            border-color: var(--sales-primary);
            background-color: rgba(32, 107, 196, 0.1);
        }
        
        .sale-type-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        /* Fade-in animation for form sections */
        .fade-in {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* DataTables responsive styles */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: 1rem;
        }

        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 1rem;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .sales-drawer {
                width: 100%;
                max-width: 100%;
            }

            .metric-icon {
                width: 2.5rem;
                height: 2.5rem;
                font-size: 1.25rem;
            }

            .sales-drawer-body {
                padding: 1rem;
            }

            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                text-align: center;
                margin-bottom: 1rem;
            }

            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_paginate {
                text-align: center;
                margin-top: 1rem;
            }

            .btn-list {
                flex-direction: column;
                gap: 0.25rem;
            }

            .btn-list .btn {
                width: 100%;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }

            .table-responsive {
                border: none;
            }
        }
        
        /* Loading States */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }
        
        .btn-loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Custom Alert Styles */
        .alert-floating {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            animation: slideInRight 0.3s ease-out;
        }
        
        /* Table Enhancements */
        .table-sales {
            font-size: 0.875rem;
        }
        
        .table-sales th {
            font-weight: 600;
            color: #495057;
            border-top: none;
        }
        
        .table-sales td {
            vertical-align: middle;
        }
        
        .table-sales .btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .navbar .dropdown-toggle::after {
            display: inline-block;
        }
    </style>
    
    <?= $this->renderSection('styles') ?>
</head>
<body class="<?= $this->renderSection('body_class', '') ?>">
    <div class="page">
        <!-- Header -->
        <header class="navbar navbar-expand-md navbar-light d-print-none" data-aos="fade-down">
            <div class="container-xl">

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href="<?= base_url('sales') ?>">
                        <img src="<?= base_url('assets/img/citem_logo_2023.png') ?>" alt="CITEM" class="navbar-brand-image">
                        <span class="ms-2">Sales Monitoring System</span>
                    </a>
                </h1>
                
                <div class="navbar-nav flex-row order-md-last">
                    <!-- User Profile -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" data-bs-auto-close="outside">
                            <span class="avatar avatar-sm" style="background-image: url(<?= session()->get('user_avatar') ?? 'https://ui-avatars.com/api/?name=' . urlencode(session()->get('company_name') ?? 'Exhibitor') . '&background=206bc4&color=fff' ?>)"></span>
                            <div class="d-none d-xl-block ps-2">
                                <div><?= session()->get('company_name') ?? 'Exhibitor' ?></div>
                                <div class="mt-1 small text-muted"><?= session()->get('user_role') ?? 'Exhibitor' ?></div>
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <!-- <a href="#" class="dropdown-item">Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <div class="dropdown-divider"></div> -->
                            <a href="<?= route_to('cinauth.logout') ?>" class="dropdown-item">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <?= $this->renderSection('navigation') ?>

        <!-- Page wrapper -->
        <div class="page-wrapper  d-print-none">




            <!-- Page header -->
            <div class="page-header d-print-none" data-aos="fade-up">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <div class="page-pretitle">
                                <?= $this->renderSection('page_pretitle', 'Sales Management') ?>
                            </div>
                            <h2 class="page-title">
                                <?= $this->renderSection('page_title', 'Dashboard') ?>
                            </h2>
                            <div class="text-muted mt-1">
                                <?= $this->renderSection('page_subtitle', 'Manage your trade expo sales and performance') ?>
                            </div>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <div class="btn-list">
                                <?= $this->renderSection('page_actions') ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    <?= $this->renderSection('content') ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Initialize AOS -->
    <script>
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>


