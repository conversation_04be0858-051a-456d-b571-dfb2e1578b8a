<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\Utils;
use GuzzleHttp\Psr7\Request;

class Guzzle<PERSON>allModel extends Model{


    public function callExternalApi1(array $data, string $url = 'products/1'){
        $client = new Client([
            'verify' => false,
            'base_uri' => 'https://api.local/',
            'headers' => [
                'Content-Type' => 'application/json',
                // Replace with your actual credentials
                'Authorization' => 'Bearer YOUR_OPEN_AI_KEY',
                'OpenAI-Organization' => 'YOUR_ORGANIZATION_ID',
            ],
        ]);

        $startTime = microtime(true);
        $promise = $client->postAsync($url, ['json' => $data]);

        $result = Utils::unwrap([$promise]);
        $endTime = microtime(true);
        $elapsedTime = number_format($endTime - $startTime, 2);

        try {
            $responseData = json_decode($result[0]->getBody()->getContents(), true);
            return $responseData; // Return the response data
        } catch (RequestException $e) {
            return ['error' => $e->getMessage()]; // Return an error message
        }
    }


    public function importData(string $url) {
        $data = $this->callExternalApi([], $url); // Retrieve data from API

        if (isset($data['error'])) {
            return ['error' => $data['error']]; // Return error message
        }

        $itemsToInsert = [];
        $itemsToUpdate = [];

        // Process data for insert/update based on your logic
        foreach ($data as $item) {
            // Example logic (replace with your actual logic)
            $existingItem = $this->where('id', $item['id'])->first();
            if (empty($existingItem)) {
                $itemsToInsert[] = $item;
            } else {
                // Update logic (modify existing data if needed)
                $item['updated_at'] = date('Y-m-d H:i:s');
                $itemsToUpdate[] = $item;
            }
        }

        // Insert new data
        if (!empty($itemsToInsert)) {
            $this->db->table('your_table_name')->insertBatch($itemsToInsert);
        }

        // Update existing data
        if (!empty($itemsToUpdate)) {
            $batchQuery = new Query($this->db);
            foreach ($itemsToUpdate as $item) {
                $batchQuery->update('your_table_name', $item, ['id' => $item['id']]);
            }
            $batchQuery->executeBatch();
        }

        return ['success' => true]; // Return success message
    }

    //for pushing batch
    // public function callExternalApi(array $data, string $url = 'products/1') {
    public function callExternalApi(array $data, string $url){
        $bearerKey = getenv('import_authorization');
        $client = new Client([
            'verify' => false,
            'base_uri' => 'https://api.local/',
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => $bearerKey,
            ],
        ]);

        $promises = [];
        foreach ($data as $item) {
            $promises[] = $client->postAsync($url, ['json' => $item]);
        }

        $results = Utils::unwrap($promises);

        $responses = [];
        $totalRecords = count($data);
        $successCount = 0;
        $errorCount = 0;

        foreach ($results as $result) {
            try {
                $responseData = json_decode($result->getBody()->getContents(), true);
                $responses[] = $responseData;
                $successCount++;
            } catch (RequestException $e) {
                $responses[] = ['error' => $e->getMessage()];
                $errorCount++;
            }
        }

        //return $responses; // Return an array of individual responses
        return [
            'responses' => $responses,
            'totalRecords' => $totalRecords,
            'successCount' => $successCount,
            'errorCount' => $errorCount,
        ];
    }

    // ... other model methods (importData can be removed if not needed)
    

    public function api_guzzle($data){
        // diagnostics($data);exit();
        $client = new GuzzleHttp\Client(['verify' => false ]);
        // echo $data['uri'];exit();
        $url = $data['base_url'].$data['uri'];
        echo $url;exit();
        // $url = 'http://localhost/api_ifex/v1/exhibitors';

        $headers = array(
            'headers'=>['User-Agent' => 'testing/1.0',
                        'x-api-key'=>$data['api_key'],
                        ],
                );
        $prep_payload['form_params'] = $data['payload'];

        $payload = array_merge($headers,$prep_payload);

        if($data['method']=='post'){
            try{
                $response = $client->request('POST',$url,$payload);
                $status = $response->getStatusCode(); // 200
                // $body = json_decode($response->getBody(),true);
                $body = json_decode($response->getBody()->getContents(),true);
                if($body['status']=='success'){
                    // $this->session->set_flashdata(array('msg'=>$body['message'], 'status_code'=>$body['status']));
                    return $body;
                }
            }catch(GuzzleHttp\Exception\ClientException $e){
                $response = $e->getResponse();
                $body = json_decode($response->getBody()->getContents(),true);
                return $body;
            }
        }elseif($data['method']=='patch'){
            try{
                $response = $client->patch($url,$payload);
                $status = $response->getStatusCode(); // 200
                $body = json_decode($response->getBody()->getContents(),true);
                if($body['status']=='success'){
                    return $body;
                    // $this->session->set_flashdata(array('msg'=>$body['message'], 'status_code'=>$body['status']));
                }
            }catch(GuzzleHttp\Exception\BadResponseException $e){
                $response = $e->getResponse();
                $body = json_decode($response->getBody()->getContents(),true);
                return $body;
            }
        }


        // echo '<pre>';
        // print_r($payload);
        // echo '</pre>';
        exit();

        try{
            $response =  $client->request(strtoupper($data['method']),$url,$payload);
            // echo $response->getStatusCode(); // 200
            echo $response->getReasonPhrase(); // OK
            echo '<pre>';
            $test = json_decode($response->getBody(),true);
            print_r($test);
            echo '</pre>';


        }
        catch(GuzzleHttp\Exception\BadResponseException $e){
            $response = $e->getResponse();
            $responseBodyAsString = $response->getBody()->getContents();
            print_r($responseBodyAsString);

        }


    }
    





}
