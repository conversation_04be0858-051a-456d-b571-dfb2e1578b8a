<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/email-templates') ?>">Email Templates</a></li>
<li class="breadcrumb-item active">Create Template</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<style>
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99999 !important;
        border-radius: 8px;
        flex-direction: column;
    }
    
    .loading-overlay.hidden {
        display: none !important;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    #email-editor {
        height: 600px;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    .placeholder-tag {
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .placeholder-tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Create New Email Template</h3>
    </div>
    <form id="templateForm">
        <?= csrf_field() ?>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateName">Template Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="templateName" name="name" required>
                    </div>
                </div>
                <!-- <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateSlug">Slug <span class="text-danger">*</span><small class="text-muted">(Unique identifier for this template)</small></label>
                        <input type="text" class="form-control" id="templateSlug" name="slug" readonly ><small class="form-text text-muted">Use only lowercase letters, numbers, and underscores. No spaces allowed.</small>
                    </div>
                </div> -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateSlug">Slug <span class="text-danger">*</span><small class="text-muted">(Unique identifier for this template)</small></label>
                        <input type="text" class="form-control" id="templateSlug" name="slug"><small class="form-text text-muted">Use only lowercase letters, numbers, and underscores. No spaces allowed.</small>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateCategory">Category <span class="text-danger">*</span></label>
                        <select class="form-control" id="templateCategory" name="category_name">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= $category ?>"><?= esc($category) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateStatus">Status</label>
                        <select class="form-control" id="templateStatus" name="is_active">
                            <option value="1" selected>Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="templateDescription">Description</label>
                <textarea class="form-control" id="templateDescription" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label for="templateSubject">Email Subject <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="templateSubject" name="subject" required>
            </div>
        </div>
    </form>
</div>

<!-- Email Editor Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Email Design</h3>
        <div class="card-tools">
            <!-- <button type="button" class="btn btn-tool" id="undoBtn" title="Undo">
                <i class="fas fa-undo"></i>
            </button>
            <button type="button" class="btn btn-tool" id="redoBtn" title="Redo">
                <i class="fas fa-redo"></i>
            </button> -->
        </div>
    </div>
    <div class="card-body p-0">
        <div class="position-relative">
            <div id="email-editor"></div>
            <div id="editor-loading" class="loading-overlay" style="display: none;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 15px; color: #666; font-size: 14px;">Loading Email Editor...</p>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <button type="submit" class="btn btn-primary" id="saveTemplateBtn">
            <i class="fas fa-save mr-2"></i>Save Template
        </button>
        <button type="submit" class="btn btn-info" id="sendTestEmailBtn">
            <i class="fas fa-paper-plane mr-2"></i>Send Test Email
        </button>
        <a href="<?= base_url('admin/email-templates') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to List
        </a>
        <button type="button" class="btn btn-info float-right" id="previewBtn">
            <i class="fas fa-eye mr-2"></i>Preview
        </button>
    </div>
</div>

<!-- Placeholders Card -->
<!-- <div class="card">
    <div class="card-header">
        <h3 class="card-title">Available Placeholders</h3>
    </div>
    <div class="card-body">
        <p class="text-muted small mb-3">Click on any placeholder to copy it to clipboard.</p>
        
        <h6>Default Placeholders:</h6>
        <div class="mb-3">
            <?php //foreach ($config->defaultPlaceholders as $key => $placeholder): ?>
            <span class="badge badge-secondary mr-1 mb-1 placeholder-tag" 
                  data-placeholder="{{<?//= $key ?>}}" 
                  title="<?//= $placeholder['description'] ?>" 
                  style="cursor: pointer;">
                {{<?//= $key ?>}}
            </span>
            <?php //endforeach; ?>
        </div>
    </div>
</div> -->

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script src="https://editor.unlayer.com/embed.js"></script>

<script>
$(document).ready(function() {
    let unlayerEditor;
    let isEditorReady = false;

    // Generate slug from name
    //$('#templateName').on('input', function() {
    //     const name = $(this).val();
    //     const slug = name.toLowerCase()
    //                     .replace(/[^a-z0-9\s-]/g, '')
    //                     .replace(/\s+/g, '-')
    //                     .replace(/-+/g, '-')
    //                     .trim('-');
    //     $('#templateSlug').val(slug);
    // });

    // Show notification function
    function showNotification(type, title, message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: type,
                title: title,
                text: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        } else {
            alert(`${title}: ${message}`);
        }
    }

    // Make sure the Unlayer library is loaded
    if (typeof unlayer === 'undefined') {
        showNotification('error', 'Editor Error', 'Email editor failed to load. Please refresh the page.');
        return;
    }
    
    // Initialize Unlayer editor
    if (document.getElementById('email-editor')) {
        $('#editor-loading').show().removeClass('hidden');

        // Add timeout to hide loading if Unlayer doesn't load
        const loadingTimeout = setTimeout(function() {
            $('#editor-loading').hide().addClass('hidden');
            showNotification('warning', 'Editor Timeout', 'Editor took too long to load, but may still be working');
        }, 20000);
        
        unlayerEditor = unlayer.createEditor({
            id: 'email-editor',
            displayMode: 'email',
            designMode: 'edit',
            projectId: 1,
            tools: {},
            appearance: {
                theme: 'modern_light',
                panels: {
                    tools: { dock: 'right' }
                }
            },
            // callbacks: {
            //     onReady: function() {
            //         clearTimeout(loadingTimeout);
            //         isEditorReady = true;
            //         $('#editor-loading').hide().addClass('hidden');
            //         showNotification('success', 'Editor Ready', 'Email editor loaded successfully');
            //     },
            //     onDesignLoad: function(data) {
            //         $('#editor-loading').hide().addClass('hidden');
            //     },
            //     onDesignUpdated: function(data) {
            //         window.formDirty = true;
            //     }
            // }
        });
        clearTimeout(loadingTimeout);
        isEditorReady = true;
        
        // Additional fallback - hide loading after 3 seconds regardless
        setTimeout(function() {
            if ($('#editor-loading').is(':visible')) {
                $('#editor-loading').hide().addClass('hidden');
            }
        }, 3000);
    }

    // Handle form submission
    $('#saveTemplateBtn').on('click', function(e) {
        e.preventDefault();
        
        // if (!isEditorReady) {
        //     showNotification('error', 'Editor Not Ready', 'Please wait for the editor to load');
        //     return;
        // }

        const $saveBtn = $('#saveTemplateBtn');
        const originalHtml = $saveBtn.html();
        $saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...');

        unlayerEditor.exportHtml(function(data) {
            const formData = {
                name: $('#templateName').val(),
                slug: $('#templateSlug').val(),
                description: $('#templateDescription').val(),
                category_name: $('#templateCategory').val(),
                subject: $('#templateSubject').val(),
                is_active: $('#templateStatus').val(),
                html_content: data.html,
                design_json: JSON.stringify(data.design),
                <?= csrf_token() ?>: $('input[name="<?= csrf_token() ?>"]').val()
            };

            $.ajax({
                url: '<?= base_url('admin/email-templates/store') ?>',
                type: 'POST',
                data: formData,
                success: function(response) {
                    $saveBtn.prop('disabled', false).html(originalHtml);
                    
                    if (response.success) {
                        showNotification('success', 'Success', 'Template created successfully');
                        setTimeout(function() {
                            window.location.href = '<?= base_url('admin/email-templates') ?>';
                        }, 1500);
                    } else {
                        showNotification('error', 'Error', response.message || 'Failed to create template');
                    }
                },
                error: function(xhr) {
                    $saveBtn.prop('disabled', false).html(originalHtml);
                    showNotification('error', 'Error', 'Failed to create template');
                }
            });
        });
    });

    // Handle send test email button click
    $('#sendTestEmailBtn').on('click', function() {
        Swal.fire({
            title: 'Send Test Email',
            text: 'Enter the email address to send the test email to:',
            input: 'email',
            inputPlaceholder: 'Email Address',
            showCancelButton: true,
            confirmButtonText: 'Send',
            preConfirm: (email) => {
                if (!email) {
                    Swal.showValidationMessage('Please enter an email address');
                }
                return email;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                sendTestEmail(result.value);
            }
        });
    });

    function sendTestEmail(email) {
        unlayerEditor.exportHtml(function(data) {
            $.ajax({
                url: '<?= base_url('admin/email-templates/test-email') ?>',
                type: 'POST',
                data: {
                    email: email,
                    html_content: data.html,
                    subject: $('#templateSubject').val(),
                    <?= csrf_token() ?>: $('input[name="<?= csrf_token() ?>"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('success', 'Success', 'Test email sent successfully');
                    } else {
                        showNotification('error', 'Error', response.message || 'Failed to send test email');
                    }
                },
                error: function(xhr) {
                    showNotification('error', 'Error', 'Failed to send test email');
                }
            });
        });
    }

    // Placeholder click to copy
    // $('.placeholder-tag').on('click', function() {
    //     const placeholder = $(this).data('placeholder');
    //     if (navigator.clipboard) {
    //         navigator.clipboard.writeText(placeholder).then(function() {
    //             showNotification('success', 'Copied', `Placeholder "${placeholder}" copied to clipboard`);
    //         });
    //     }
    // });


});
</script>
<?= $this->endSection() ?>
