<?php

namespace Modules\AuditTrail\Config;

$routes->group('admin', function($routes) {
    $routes->group('audit', [
        'namespace' => 'Modules\AuditTrail\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Main routes
        $routes->get('/', 'AuditTrailController::index', ['as' => 'audit.index']);
        $routes->get('show/(:segment)', 'AuditTrailController::show/$1', ['as' => 'audit.show']);
        $routes->get('logs', 'AuditTrailController::index', ['as' => 'audit.logs']);

        // AJAX endpoints
        $routes->post('datatable', 'AuditTrailController::datatable', ['as' => 'audit.datatable']);
        $routes->get('show/(:segment)', 'AuditTrailController::show/$1', ['as' => 'audit.show']);
        $routes->post('stats', 'AuditTrailController::stats', ['as' => 'audit.stats']);

        // Management operations
        $routes->post('clean-old-logs', 'AuditTrailController::cleanOldLogs', ['as' => 'audit.clean_old_logs']);
        $routes->get('export', 'AuditTrailController::export', ['as' => 'audit.export']);
    });
});
