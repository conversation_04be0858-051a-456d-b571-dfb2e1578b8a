<?php

namespace Modules\EmailQManagement\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Modules\EmailQManagement\Models\EmailQueueModel;
use App\libraries\EmailService;

class ProcessQueue extends BaseCommand
{
    protected $group       = 'EmailQueue';
    protected $name        = 'email:process';
    protected $description = 'Process and send queued emails.';

    // CLI version
    public function run(array $params)
    {
        $emailQueueModel = new EmailQueueModel();
        $service = new EmailService();

        $emails = $model->where('status', 'pending')
                        ->where('attempts <', 3)
                        ->orderBy('created_at', 'ASC')
                        ->findAll(10);

        if (empty($emails)) {
            CLI::write('No queued emails to process.', 'yellow');
            return;
        }

        foreach ($emails as $email) {
            $result = $service->sendTemplatedEmail($email);

            if ($result === true) {
                $emailQueueModel->update($email['id'], [
                    'status'   => $result === true ? 'sent' : 'failed',
                    'error'    => $result === true ? null : $result,
                    'attempts' => $email['attempts'] + 1,
                    'sent_at'  => $result === true ? date('Y-m-d H:i:s') : null,
                ]);
                CLI::write(($result === true
                ? "✔ Sent to {$email['to_email']}"
                : "✖ Failed to send to {$email['to_email']}: $result"), $result === true ? 'green' : 'red');
            }
            else {
                CLI::error("Failed to send to {$email['to_email']}: $result");
            }
        }
    }

    // Can be called from controllers (dashboard trigger)
    public static function runFromWeb()
    {
        (new static())->processQueue(false); // silent mode
    }

    private function processQueue(bool $verbose = false)
    {
        $model = new EmailQueueModel();

        // Fetch up to 10 pending emails with < 3 attempts
        $emails = $model->where('status', 'pending')
                        ->where('attempts <', 3)
                        ->orderBy('created_at', 'ASC')
                        ->findAll(10);

        if (empty($emails)) {
            if ($verbose) CLI::write('No emails to process.', 'yellow');
            return;
        }

        foreach ($emails as $email) {
            $result = $this->sendEmail($email);

            // Update record based on result
            $model->update($email['id'], [
                'status'   => $result['status'],
                'attempts' => $email['attempts'] + 1,
                'error'    => $result['error'],
                'sent_at'  => $result['status'] === 'sent' ? date('Y-m-d H:i:s') : null
            ]);

            // CLI output
            if ($verbose) {
                if ($result['status'] === 'sent') {
                    CLI::write("✔ Email sent to: {$email['to_email']}", 'green');
                } else {
                    CLI::error("✖ Failed to send to {$email['to_email']}: " . $result['error']);
                }
            }
        }
    }

    private function sendEmail(array $email): array
    {
        try {
            $mail = new PHPMailer(true);

            // SMTP config from .env
            $mail->isSMTP();
            $mail->Host       = env('email.SMTPHost');
            $mail->SMTPAuth   = env('email.SMTPAuth');
            $mail->SMTPAutoTLS= env('email.SMTPAutoTLS');
            $mail->Username   = env('email.SMTPUser');
            $mail->Password   = env('email.SMTPPass');
            $mail->Port       = env('email.SMTPPort');
            $mail->SMTPSecure = env('email.SMTPCrypto');

            $mail->setFrom(env('email.fromEmail'), 'Queue Mailer');
            $mail->addAddress($email['to_email']);
            $mail->Subject = $email['subject'] ?? '(No Subject)';
            $mail->isHTML(true);

            // Load template
            $template = $email['template'] ?? 'default';
            $templateData = json_decode($email['template_data'] ?? '', true) ?? [];

            if (!view()->exists("Modules\\EmailQManagement\\Views\\templates\\$template")) {
                throw new \Exception("Template '$template' not found.");
            }

            $mail->Body = view("Modules\\EmailQManagement\\Views\\templates\\$template", $templateData);

            $mail->send();

            return [
                'status' => 'sent',
                'error'  => null,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error'  => $e->getMessage(),
            ];
        }
    }
}
