<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;

class CipModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'e_cin';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = \App\Entities\Cip::class;
    // protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = [
        "used","sector","ff_code","co_name","co_email","cin","cip","fair_code","marked","survey"
    ];

    // protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];



}
