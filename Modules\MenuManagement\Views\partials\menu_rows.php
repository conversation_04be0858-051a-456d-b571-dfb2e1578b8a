<?php
// Initialize variables if not set
if (!isset($counter)) {
    $counter = 1;
}
if (!isset($level)) {
    $level = 0;
}

// Function to render menu rows recursively
function renderMenuRows($menus, $level = 0, &$counter = 1) {
    $html = '';
    foreach ($menus as $menu) {
        $html .= '<tr>';
        $html .= '<td>' . $counter++ . '.</td>';
        $html .= '<td>';
        $html .= str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
        if ($level > 0) {
            $html .= '<i class="fas fa-level-up-alt fa-rotate-90 text-muted"></i> ';
        }
        $html .= esc($menu['label']);
        $html .= '</td>';

        // URL column
        $html .= '<td>';
        if (!empty($menu['url'])) {
            $html .= '<code>' . esc($menu['url']) . '</code>';
        } else {
            $html .= '<span class="text-muted">-</span>';
        }
        $html .= '</td>';

        // Icon column
        $html .= '<td>';
        if (!empty($menu['icon'])) {
            $html .= '<i class="' . esc($menu['icon']) . '"></i> <small class="text-muted">' . esc($menu['icon']) . '</small>';
        } else {
            $html .= '<span class="text-muted">-</span>';
        }
        $html .= '</td>';

        // Permission column
        $html .= '<td>';
        if (!empty($menu['permission_name'])) {
            $html .= '<span class="badge badge-info">' . esc($menu['permission_name']) . '</span>';
        } else {
            $html .= '<span class="text-muted">Public</span>';
        }
        $html .= '</td>';

        // Parent column
        $html .= '<td>';
        if ($level > 0) {
            $html .= '<span class="text-muted">Child Menu</span>';
        } else {
            $html .= '<span class="text-muted">Root Menu</span>';
        }
        $html .= '</td>';

        // Sort order column
        $html .= '<td><span class="badge badge-secondary">' . $menu['sort_order'] . '</span></td>';

        // Status column
        $html .= '<td>';
        if ($menu['active']) {
            $html .= '<span class="badge badge-success">Active</span>';
        } else {
            $html .= '<span class="badge badge-danger">Inactive</span>';
        }
        $html .= '</td>';

        // Actions column
        $html .= '<td>';
        if (hasPermission('menu.manage')) {
            $html .= '<a href="' . route_to('menu.edit', $menu['id']) . '" class="btn btn-info btn-sm" title="Edit">';
            $html .= '<i class="fas fa-edit"></i></a> ';
            $html .= '<a href="' . route_to('menu.delete', $menu['id']) . '" class="btn btn-danger btn-sm btn-delete" title="Delete">';
            $html .= '<i class="fas fa-trash"></i></a>';
        } else {
            $html .= '<span class="text-muted">Not available</span>';
        }
        $html .= '</td>';
        $html .= '</tr>';

        // Render children
        if (isset($menu['children']) && !empty($menu['children'])) {
            $html .= renderMenuRows($menu['children'], $level + 1, $counter);
        }
    }
    return $html;
}

echo renderMenuRows($menus, $level, $counter);
?>
