<?php

namespace Modules\OfficeManagement\Migrations;

use CodeIgniter\Database\Migration;

class AddOfficePermissions extends Migration
{
    public function up()
    {
        // Insert office management permissions
        $permissions = [
            [
                'name' => 'office.manage',
                'description' => 'Manage offices (create, edit, delete)',
                'category' => 'Office Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'office.view',
                'description' => 'View office information',
                'category' => 'Office Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('permissions')->insertBatch($permissions);
    }

    public function down()
    {
        // Remove office management permissions
        $this->db->table('permissions')
                 ->whereIn('name', ['office.manage', 'office.view'])
                 ->delete();
    }
}
