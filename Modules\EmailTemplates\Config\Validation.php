<?php

namespace Modules\EmailTemplates\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public array $save_template = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[email_templates.name]',
        'slug' => 'required|alpha_dash|is_unique[email_templates.slug]',
        'description' => 'permit_empty|max_length[500]',
        'category_name' => 'required|min_length[1]|max_length[100]',
        'subject' => 'required|min_length[1]|max_length[255]',
        'is_active' => 'permit_empty|in_list[0,1]',
        'html_content' => 'required',
        'design_json' => 'required',
    ];

    public array $save_template_errors = [
        'name' => [
            'required' => 'Template name is required.',
            'min_length' => 'Template name must be at least 3 characters long.',
            'max_length' => 'Template name cannot exceed 255 characters.',
            'is_unique' => 'This template name already exists.',
        ],
        'slug' => [
            'required' => 'Slug is required.',
            'alpha_dash' => 'Slug can only contain letters, numbers, underscores, and dashes.',
            'is_unique' => 'This slug already exists.',
        ],
        'category_name' => [
            'required' => 'Category is required.',
        ],
        'subject' => [
            'required' => 'Subject is required.',
        ],
        'html_content' => [
            'required' => 'HTML content is required.',
        ],
        'design_json' => [
            'required' => 'Design JSON is required.',
        ],
    ];
}
