<?php

namespace Modules\AgencyManagement\Migrations;

use CodeIgniter\Database\Migration;

class AddAgencyPermissions extends Migration
{
    public function up()
    {
        // Insert agency management permissions
        $permissions = [
            [
                'name' => 'agency.manage',
                'description' => 'Manage agencies (create, edit, delete)',
                'category' => 'Agency Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'agency.view',
                'description' => 'View agency information',
                'category' => 'Agency Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('permissions')->insertBatch($permissions);
    }

    public function down()
    {
        // Remove agency management permissions
        $this->db->table('permissions')
                 ->whereIn('name', ['agency.manage', 'agency.view'])
                 ->delete();
    }
}
