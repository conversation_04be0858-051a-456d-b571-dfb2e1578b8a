<?php

namespace Modules\MenuManagement\Models;

use CodeIgniter\Model;

class MenuRoleModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'menu_roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'menu_id',
        'role_id',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get all roles assigned to a specific menu
     * @param int $menuId
     * @return array
     */
    public function getMenuRoles(int $menuId): array
    {
        return $this->select('menu_roles.*, roles.name as role_name, roles.description as role_description')
                    ->join('roles', 'roles.id = menu_roles.role_id')
                    ->where('menu_roles.menu_id', $menuId)
                    ->where('roles.is_active', 1)
                    ->findAll();
    }

    /**
     * Get all role IDs assigned to a specific menu
     * @param int $menuId
     * @return array
     */
    public function getMenuRoleIds(int $menuId): array
    {
        $roles = $this->select('role_id')
                     ->where('menu_id', $menuId)
                     ->findAll();
        
        return array_column($roles, 'role_id');
    }

    /**
     * Get all menus assigned to a specific role
     * @param int $roleId
     * @return array
     */
    public function getRoleMenus(int $roleId): array
    {
        return $this->select('menu_roles.*, menus.label, menus.url, menus.icon')
                    ->join('menus', 'menus.id = menu_roles.menu_id')
                    ->where('menu_roles.role_id', $roleId)
                    ->where('menus.active', 1)
                    ->findAll();
    }

    /**
     * Assign multiple roles to a menu
     * @param int $menuId
     * @param array $roleIds
     * @return bool
     */
    public function assignRolesToMenu(int $menuId, array $roleIds): bool
    {
        // First, remove existing role assignments for this menu
        $this->where('menu_id', $menuId)->delete();

        // Then add new assignments
        if (!empty($roleIds)) {
            $data = [];
            foreach ($roleIds as $roleId) {
                $data[] = [
                    'menu_id' => $menuId,
                    'role_id' => $roleId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
            }
            
            return $this->insertBatch($data) !== false;
        }

        return true; // Successfully removed all roles (empty assignment)
    }

    /**
     * Remove a specific role from a menu
     * @param int $menuId
     * @param int $roleId
     * @return bool
     */
    public function removeRoleFromMenu(int $menuId, int $roleId): bool
    {
        return $this->where('menu_id', $menuId)
                    ->where('role_id', $roleId)
                    ->delete();
    }

    /**
     * Check if a menu is assigned to a specific role
     * @param int $menuId
     * @param int $roleId
     * @return bool
     */
    public function isMenuAssignedToRole(int $menuId, int $roleId): bool
    {
        $count = $this->where('menu_id', $menuId)
                     ->where('role_id', $roleId)
                     ->countAllResults();
        
        return $count > 0;
    }

    /**
     * Get menu count by role
     * @param int $roleId
     * @return int
     */
    public function getMenuCountByRole(int $roleId): int
    {
        return $this->where('role_id', $roleId)->countAllResults();
    }

    /**
     * Bulk delete menu roles by menu IDs
     * @param array $menuIds
     * @return bool
     */
    public function bulkDeleteByMenuIds(array $menuIds): bool
    {
        if (empty($menuIds)) {
            return true;
        }

        return $this->whereIn('menu_id', $menuIds)->delete();
    }

    /**
     * Bulk delete menu roles by role IDs
     * @param array $roleIds
     * @return bool
     */
    public function bulkDeleteByRoleIds(array $roleIds): bool
    {
        if (empty($roleIds)) {
            return true;
        }

        return $this->whereIn('role_id', $roleIds)->delete();
    }
}
