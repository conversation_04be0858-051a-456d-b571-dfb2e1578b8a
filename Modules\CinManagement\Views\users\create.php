<?= $this->extend('layouts/basic'); ?>

<?= $this->section('title'); ?>Receivables - CARMS<?= $this->endSection(); ?>

<?= $this->section('header'); ?>

<?= $this->endSection(); ?>


<?= $this->section("content") ?>


  <!-- Content Header (Page header) -->
  <section class="content-header">
    <div class="container-fluid">
      <div class="row">
        <div class="col-sm-6">
          <h1><?= $pageTitle??'' ?></h1>
        </div>
        <div class="col-sm-6">
          <ol class="float-sm-right">
            <div class="fixed-table-toolbar">
              <div class="bs-bars pull-left">
                <div id="userBulkEditToolbar">
                  <!-- PLACEHOLDER:right side -->
                </div>
              </div>
            </div>
          </ol>
        </div><!-- /.col -->
      </div>
    </div><!-- /.container-fluid -->
  </section>


    <!-- Datatable -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-12 col-md-8">
                

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">User Form</h3>
                    </div>

                    <div class="card-body">

                        <?php if (session()->has('errors')): ?>
                            <div class="alert alert-danger">
                                <ul>
                                    <?php foreach (session('errors') as $error): ?>
                                        <li><?= esc($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form action="<?= route_to('user.store') ?>" method="post">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" name="username" class="form-control" value="<?= old('username',isset($username) ? esc($username) : '') ?>">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" name="email" class="form-control" value="<?= old('email',isset($email) ? esc($email) : '') ?>">
                            </div>
                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" name="password" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="role_id">Role</label>
                                <select name="role_id" class="form-control" required>
                                    <?php foreach ($roles as $role): ?>
                                    <option value="<?= $role['id'] ?>"><?= $role['name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Save</button>
                        </form>
                    </div>
                </div>


            </div>
        </div>
    </div>
</section>


<?= $this->endSection(); ?>
