<?php

namespace Modules\MenuManagement\Controllers;

use App\Controllers\AdminController;
use Modules\MenuManagement\Models\MenuModel;
use Modules\MenuManagement\Models\MenuRoleModel;
use Modules\MenuManagement\Services\MenuService;
use Modules\RoleManagement\Models\PermissionModel;
use Modules\RoleManagement\Models\RoleModel;
use Hermawan\DataTables\DataTable;

class MenuController extends AdminController
{
    protected $menuModel;
    protected $menuRoleModel;
    protected $menuService;
    protected $permissionModel;
    protected $roleModel;

    public function __construct()
    {
        $this->menuModel = new MenuModel();
        $this->menuRoleModel = new MenuRoleModel();
        $this->menuService = new MenuService();
        $this->permissionModel = new PermissionModel();
        $this->roleModel = new RoleModel();
    }

    public function index()
    {
        
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $data['title'] = 'Menu Management';
        $data['page_title'] = 'Menu Management';

        // Get statistics for dashboard cards
        $data['stats'] = [
            'total' => $this->menuModel->countAllResults(),
            'active' => $this->menuModel->where('active', 1)->countAllResults(),
            'inactive' => $this->menuModel->where('active', 0)->countAllResults(),
            'root_menus' => $this->menuModel->where('parent_id IS NULL')->countAllResults(),
        ];
        return view('Modules\MenuManagement\Views\index', $data);
    }

    /**
     * DataTables AJAX endpoint
     */
    public function datatable()
    {
        // if (!hasPermission('menu.manage')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $builder = $this->menuModel->getMenusWithRolesBuilder();

        // print_r($builder->get()->getResultArray());exit();



        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex')
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->edit('label', function ($row) {
                $indent = '';
                if ($row->parent_id) {
                    // Add indentation for child menus
                    $level = $this->getMenuLevel($row->id);
                    $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
                }

                $icon = $row->icon ? '<i class="' . $row->icon . '"></i> ' : '';
                return $indent . $icon . esc($row->label);
            })
            ->edit('url', function ($row) {
                return $row->url ? '<code>' . esc($row->url) . '</code>' : '<span class="text-muted">No URL</span>';
            })
            ->add('roles', function ($row) {
                if (!empty($row->role_names)) {
                    $roles = explode(',', $row->role_names);
                    $badges = '';
                    foreach ($roles as $role) {
                        $badges .= '<span class="badge bg-blue text-blue-fg me-1">' . esc(trim($role)) . '</span>';
                    }
                    return $badges;
                }
                return '<span class="badge bg-secondary">Public</span>';
            })
            ->edit('permission_name', function ($row) {
                return $row->permission_name ?
                    '<span class="badge bg-yellow">' . esc($row->permission_name) . '</span>' :
                    '<span class="text-muted">None</span>';
            })
            ->edit('parent_label', function ($row) {
                return $row->parent_label ? esc($row->parent_label) : '<span class="text-muted">Root Menu</span>';
            })
            ->edit('active', function ($row) {
                $badgeClass = $row->active ? 'bg-green' : 'bg-red';
                $status = $row->active ? 'Active' : 'Inactive';
                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->add('actions', function ($row) {
                $actions = '<div class="btn-list">';

                if (hasPermission('menu.manage')) {
                    // Get SVG icons using helper function
                    $editIcon = function_exists('getTablerIconSvg') ? getTablerIconSvg('ti ti-edit') : '<i class="ti ti-edit"></i>';
                    $deleteIcon = function_exists('getTablerIconSvg') ? getTablerIconSvg('ti ti-trash') : '<i class="ti ti-trash"></i>';

                    $actions .= '<a href="' . route_to('menu.edit', $row->id) . '" class="btn btn-sm btn-outline-primary" title="Edit">' .
                                    $editIcon .
                                '</a>';
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteMenu(' . $row->id . ')" title="Delete">' .
                                    $deleteIcon .
                                '</button>';
                }

                $actions .= '</div>';
                return $actions;
            })
            ->toJson(true);
    }

    /**
     * Get menu level for indentation
     */
    private function getMenuLevel($menuId, $level = 0)
    {
        $menu = $this->menuModel->find($menuId);
        if (!$menu || !$menu['parent_id']) {
            return $level;
        }
        return $this->getMenuLevel($menu['parent_id'], $level + 1);
    }

    /**
     * Bulk delete menus
     */
    public function bulkDelete()
    {
        if (!hasPermission('menu.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        // Check for child menus
        $hasChildren = false;
        foreach ($ids as $id) {
            $children = $this->menuModel->where('parent_id', $id)->findAll();
            if (!empty($children)) {
                $hasChildren = true;
                break;
            }
        }

        if ($hasChildren) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete menus with child items']);
        }

        try {
            // Delete menu role assignments first
            $this->menuRoleModel->bulkDeleteByMenuIds($ids);

            // Delete menus
            $this->menuModel->whereIn('id', $ids)->delete();

            return $this->response->setJSON(['success' => true, 'message' => 'Selected menus deleted successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete menus: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk activate menus
     */
    public function bulkActivate()
    {
        if (!hasPermission('menu.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        try {
            $this->menuModel->whereIn('id', $ids)->set(['active' => 1])->update();
            return $this->response->setJSON(['success' => true, 'message' => 'Selected menus activated successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate menus: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk deactivate menus
     */
    public function bulkDeactivate()
    {
        if (!hasPermission('menu.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        try {
            $this->menuModel->whereIn('id', $ids)->set(['active' => 0])->update();
            return $this->response->setJSON(['success' => true, 'message' => 'Selected menus deactivated successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate menus: ' . $e->getMessage()]);
        }
    }

    public function create()
    {
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $data['title'] = 'Create Menu';
        $data['page_title'] = 'Create New Menu';
        $data['permissions'] = $this->permissionModel->findAll(); // Keep for backward compatibility
        $data['roles'] = $this->roleModel->where('is_active', 1)->findAll();
        $data['parentMenus'] = $this->menuModel->getMenuOptions();

        return view('Modules\MenuManagement\Views\create', $data);
    }

    public function store()
    {
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $data = $this->request->getPost();

        // Validate the input
        if (!$this->menuModel->validate($data)) {
            return redirect()->back()->withInput()->with('errors', $this->menuModel->errors());
        }

        // Validate hierarchy
        if (isset($data['parent_id']) && $data['parent_id']) {
            if (!$this->menuService->validateMenuHierarchy(null, $data['parent_id'])) {
                return redirect()->back()->withInput()->with('error', 'Invalid menu hierarchy.');
            }
        }

        // Extract role assignments
        $roleIds = $data['role_ids'] ?? [];
        unset($data['role_ids']); // Remove from menu data

        // Clean data
        $data['permission_id'] = !empty($data['permission_id']) ? $data['permission_id'] : null;
        $data['parent_id'] = !empty($data['parent_id']) ? $data['parent_id'] : null;
        $data['sort_order'] = !empty($data['sort_order']) ? $data['sort_order'] : 0;
        $data['active'] = isset($data['active']) ? 1 : 0;
        $data['css_class'] = !empty($data['css_class']) ? trim($data['css_class']) : null;
        $data['target'] = !empty($data['target']) ? $data['target'] : '_self';
        $data['menu_position'] = !empty($data['menu_position']) ? $data['menu_position'] : 'sidebar';
        $data['display_style'] = !empty($data['display_style']) ? $data['display_style'] : 'default';

        $menuId = $this->menuModel->insert($data);
        if ($menuId) {
            // Assign roles to the menu
            if (!empty($roleIds)) {
                $this->menuRoleModel->assignRolesToMenu($menuId, $roleIds);
            }

            // Clear all menu caches since menu structure changed
            $this->menuService->clearAllMenuCache();

            return redirect()->route('menu.index')->with('success', 'Menu created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create menu.');
        }
    }

    public function edit($id = null)
    {
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $menu = $this->menuModel->find($id);

        if (!$menu) {
            return redirect()->route('menu.index')->with('error', 'Menu not found.');
        }

        $data['menu'] = $menu;
        $data['title'] = 'Edit Menu';
        $data['page_title'] = 'Edit Menu: ' . $menu['label'];
        $data['permissions'] = $this->permissionModel->findAll(); // Keep for backward compatibility
        $data['roles'] = $this->roleModel->where('is_active', 1)->findAll();
        $data['assignedRoleIds'] = $this->menuRoleModel->getMenuRoleIds($id);
        $data['parentMenus'] = $this->menuModel->getMenuOptions($id);

        // Debug: Log parent menu options and current menu details
        log_message('debug', 'Edit Menu ID: ' . $id . ', Current parent_id: ' . ($menu['parent_id'] ?? 'null'));
        log_message('debug', 'Current menu details: ' . json_encode($menu));
        log_message('debug', 'Available parent menus: ' . json_encode($data['parentMenus']));

        // Additional debug: Get all menus to see the full structure
        $allMenus = $this->menuModel->select('id, label, parent_id, active')->findAll();
        log_message('debug', 'All menus in database: ' . json_encode($allMenus));


        return view('Modules\MenuManagement\Views\edit', $data);
    }

    public function update($id = null)
    {
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $menu = $this->menuModel->find($id);

        if (!$menu) {
            return redirect()->route('menu.index')->with('error', 'Menu not found.');
        }

        $data = $this->request->getPost();

        // Debug: Log what data is being submitted
        log_message('debug', 'Menu Update - ID: ' . $id . ', Submitted data: ' . json_encode($data));

        // Validate the input
        if (!$this->menuModel->validate($data)) {
            return redirect()->back()->withInput()->with('errors', $this->menuModel->errors());
        }

        // Validate hierarchy
        if (isset($data['parent_id']) && $data['parent_id']) {
            if (!$this->menuService->validateMenuHierarchy($id, $data['parent_id'])) {
                return redirect()->back()->withInput()->with('error', 'Invalid menu hierarchy. A menu cannot be a parent of itself or its ancestors.');
            }
        }

        // Extract role assignments
        $roleIds = $data['role_ids'] ?? [];
        unset($data['role_ids']); // Remove from menu data

        // Clean data
        $data['permission_id'] = !empty($data['permission_id']) ? $data['permission_id'] : null;
        $data['parent_id'] = !empty($data['parent_id']) ? $data['parent_id'] : null;
        $data['sort_order'] = !empty($data['sort_order']) ? $data['sort_order'] : 0;
        $data['active'] = isset($data['active']) ? 1 : 0;
        $data['css_class'] = !empty($data['css_class']) ? trim($data['css_class']) : null;
        $data['target'] = !empty($data['target']) ? $data['target'] : '_self';
        $data['menu_position'] = !empty($data['menu_position']) ? $data['menu_position'] : 'sidebar';
        $data['display_style'] = !empty($data['display_style']) ? $data['display_style'] : 'default';

        if ($this->menuModel->update($id, $data)) {
            // Update role assignments
            $this->menuRoleModel->assignRolesToMenu($id, $roleIds);

            // Clear all menu caches since menu was updated
            $this->menuService->clearAllMenuCache();

            return redirect()->route('menu.index')->with('success', 'Menu updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update menu.');
        }
    }

    public function delete($id = null)
    {
        if (!hasPermission('menu.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage menus.');
        }

        $menu = $this->menuModel->find($id);

        if (!$menu) {
            return redirect()->route('menu.index')->with('error', 'Menu not found.');
        }

        // Check if menu has children
        $children = $this->menuModel->where('parent_id', $id)->findAll();
        if (!empty($children)) {
            return redirect()->route('menu.index')->with('error', 'Cannot delete menu with child items. Please delete or reassign child items first.');
        }

        if ($this->menuModel->delete($id)) {
            // Clear all menu caches since menu was deleted
            $this->menuService->clearAllMenuCache();

            return redirect()->route('menu.index')->with('success', 'Menu deleted successfully.');
        } else {
            return redirect()->route('menu.index')->with('error', 'Failed to delete menu.');
        }
    }

    public function reorder()
    {
        if (!hasPermission('menu.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $orders = $this->request->getPost('orders');

        if (!$orders || !is_array($orders)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid data']);
        }

        if ($this->menuService->reorderMenus($orders)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu order updated successfully']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update menu order']);
        }
    }

    /**
     * Preview icon for AJAX requests
     */
    public function previewIcon()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $input = $this->request->getJSON(true);
        $iconClass = $input['icon'] ?? '';

        if (empty($iconClass)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No icon provided']);
        }

        // Use the helper function to get SVG or fallback
        if (strpos($iconClass, 'ti ti-') === 0 && function_exists('getTablerIconSvg')) {
            $iconHtml = getTablerIconSvg($iconClass);
        } else {
            $iconHtml = '<i class="' . esc($iconClass) . '"></i>';
        }

        return $this->response->setJSON([
            'success' => true,
            'icon_html' => $iconHtml
        ]);
    }

    /**
     * Debug method to check parent menu options
     */
    public function debugParentOptions($id = null)
    {
        if (ENVIRONMENT !== 'development') {
            return $this->response->setStatusCode(404);
        }

        if (!$id) {
            return $this->response->setJSON(['error' => 'Menu ID required']);
        }

        $menu = $this->menuModel->find($id);
        if (!$menu) {
            return $this->response->setJSON(['error' => 'Menu not found']);
        }

        $parentOptions = $this->menuModel->getMenuOptions($id);
        $allMenus = $this->menuModel->select('id, label, parent_id, active')->findAll();

        return $this->response->setJSON([
            'current_menu' => $menu,
            'parent_options' => $parentOptions,
            'all_menus' => $allMenus,
            'excluded_children' => $this->menuModel->getChildrenIds($id)
        ]);
    }
}