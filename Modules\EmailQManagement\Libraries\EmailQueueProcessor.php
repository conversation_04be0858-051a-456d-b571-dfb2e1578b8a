<?php
namespace Modules\EmailQManagement\Libraries;

use Modules\EmailQManagement\Models\EmailQueueModel;
use Modules\EmailQManagement\Libraries\EmailService;

class EmailQueueProcessor
{
    public function run(): array
    {
        $emailQueueModel = new EmailQueueModel();
        $emailService = new EmailService();

        $emails = $emailQueueModel->where('status', 'pending')
                        ->where('attempts <', 3)
                        ->orderBy('created_at', 'asc')
                        ->findAll(10); // Limit batch size

        $results = [];

        foreach ($emails as $email) {
            $result = $emailService->sendTemplatedEmail($email);

            $emailQueueModel->update($email['id'], [
                'status'   => $result === true ? 'sent' : 'failed',
                'error'    => $result === true ? null : $result,
                'attempts' => $email['attempts'] + 1,
                'sent_at'  => $result === true ? date('Y-m-d H:i:s') : null,
            ]);

            $results[] = [
                'id' => $email['id'],
                'result' => $result === true ? 'Sent' : 'Failed: ' . $result
            ];
        }

        return $results;
    }
}
