<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/roles') ?>">Roles</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Display flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<!-- Display validation errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <strong>Validation Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-user-tag"></i> Create New Role</h3>
            </div>
            <form action="<?= site_url('admin/roles/store') ?>" method="post" id="roleForm">
                <?= csrf_field() ?>
                <div class="card-body">
                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag"></i> Role Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" class="form-control <?= session('errors.name') ? 'is-invalid' : '' ?>"
                               value="<?= old('name') ?>" required placeholder="Enter role name">
                        <?php if (session('errors.name')): ?>
                            <div class="invalid-feedback"><?= session('errors.name') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Enter a unique name for this role (e.g., "Administrator", "Editor")</small>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left"></i> Description</label>
                        <textarea name="description" id="description" class="form-control <?= session('errors.description') ? 'is-invalid' : '' ?>"
                                  rows="4" placeholder="Enter role description"><?= old('description') ?></textarea>
                        <?php if (session('errors.description')): ?>
                            <div class="invalid-feedback"><?= session('errors.description') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Provide a brief description of what this role can do</small>
                    </div>

                    <!-- <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" <?= old('is_active', '1') ? 'checked' : '' ?>>
                            <label class="custom-control-label" for="is_active">
                                <i class="fas fa-toggle-on"></i> Active Status
                            </label>
                        </div>
                        <small class="form-text text-muted">Inactive roles cannot be assigned to users</small>
                    </div> -->
                </div>

                <div class="card-footer bg-light">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Role
                    </button>
                    <a href="<?= site_url('admin/roles') ?>" class="btn btn-secondary ml-2">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-info-circle"></i> Role Information</h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-lightbulb"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Tips</span>
                        <span class="info-box-number">Role Creation</span>
                    </div>
                </div>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Use descriptive names</li>
                    <li><i class="fas fa-check text-success"></i> Keep descriptions clear</li>
                    <li><i class="fas fa-check text-success"></i> Set appropriate permissions later</li>
                    <li><i class="fas fa-check text-success"></i> Test role functionality</li>
                </ul>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> After creating the role, you can assign specific permissions to control what users with this role can do.
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('#roleForm').on('submit', function(e) {
        var name = $('#name').val().trim();

        if (name === '') {
            e.preventDefault();
            $('#name').addClass('is-invalid');
            if (!$('#name').next('.invalid-feedback').length) {
                $('#name').after('<div class="invalid-feedback">Role name is required.</div>');
            }
            return false;
        } else {
            $('#name').removeClass('is-invalid');
            $('#name').next('.invalid-feedback').remove();
        }
    });

    // Real-time validation
    $('#name').on('input', function() {
        var name = $(this).val().trim();
        if (name !== '') {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>
<?= $this->endSection() ?>