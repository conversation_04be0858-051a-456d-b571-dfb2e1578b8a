<?php

namespace Modules\AuditTrail\Migrations;

use CodeIgniter\Database\Migration;

class CreateAuditLogsTable extends Migration
{
    public function up()
    {
        // Define the fields for the 'audit_trails' table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true, // Allow null if action is by guest or system
            ],
            'event' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'comment'    => 'e.g., login, create_user, update_product',
            ],
            'auditable_type' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'null'       => true, // e.g., User, Product, Order (the model/entity being audited)
            ],
            'auditable_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true, // ID of the audited entity
            ],
            'old_values' => [
                'type' => 'TEXT',
                'null' => true, // JSON string of old values for updates/deletes
            ],
            'new_values' => [
                'type' => 'TEXT',
                'null' => true, // JSON string of new values for creates/updates
            ],
            'ip_address' => [
                'type'       => 'VARCHAR',
                'constraint' => '45', // IPv4 or IPv6
                'null'       => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'url' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'null'       => true,
            ],
            'method' => [
                'type'       => 'VARCHAR',
                'constraint' => '10', // e.g., GET, POST, PUT, DELETE
                'null'       => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        // Add primary key
        $this->forge->addPrimaryKey('id');

        // Add foreign key constraint (optional, depends on your user table)
        // If you have a 'users' table, you might want to add this:
        // $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'SET NULL');

        // Create the table
        $this->forge->createTable('audit_logs', true); // 'true' adds IF NOT EXISTS
    }

    public function down()
    {
        // Drop the table if it exists
        $this->forge->dropTable('audit_logs', true);
    }
}