<?php

namespace Modules\OfficeManagement\Config;

$routes->group('admin', function($routes) {
    $routes->group('offices', [
        'namespace' => 'Modules\OfficeManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Main CRUD routes
        $routes->GET('/', 'OfficeController::index', ['as' => 'offices.index']);
        $routes->GET('create', 'OfficeController::create', ['as' => 'offices.create']);
        $routes->POST('store', 'OfficeController::store', ['as' => 'offices.store']);
        $routes->GET('edit/(:segment)', 'OfficeController::edit/$1', ['as' => 'offices.edit']);
        $routes->POST('update/(:segment)', 'OfficeController::update/$1', ['as' => 'offices.update']);
        $routes->GET('delete/(:segment)', 'OfficeController::delete/$1', ['as' => 'offices.delete']);

        // AJAX endpoints for DataTables
        $routes->POST('datatable', 'OfficeController::datatable', ['as' => 'offices.datatable']);
        $routes->POST('ajax-delete', 'OfficeController::ajaxDelete', ['as' => 'offices.ajax_delete']);
        $routes->GET('show/(:segment)', 'OfficeController::show/$1', ['as' => 'offices.show']);

        // Bulk operations
        $routes->POST('bulk-delete', 'OfficeController::bulkDelete', ['as' => 'offices.bulk_delete']);
        $routes->POST('bulk-activate', 'OfficeController::bulkActivate', ['as' => 'offices.bulk_activate']);
        $routes->POST('bulk-deactivate', 'OfficeController::bulkDeactivate', ['as' => 'offices.bulk_deactivate']);
    });
});
