<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- Choices.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css">
<style>

    /* .choices__list--dropdown {
        background-color: #fff !important;
        opacity: 1 !important;
        backdrop-filter: none !important;
    }

    .choices__list--dropdown {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        border: 1px solid #ccc;
    } */


    /* .choices__inner {
        min-height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        background-color: #fff;
    }
    .choices[data-type*="select-multiple"] .choices__inner {
        cursor: text;
    }
    .choices__item--choice.is-highlighted {
        background-color: #007bff;
    }
    .choices__item--selectable.is-highlighted {
        background-color: #007bff;
    }
    .is-invalid .choices__inner {
        border-color: #dc3545;
    }
    .choices__item--choice {
        padding: 8px 12px;
    }
    .choices__item--choice:hover {
        background-color: #f8f9fa;
    }
    .border-warning .choices__inner {
        border-color: #ffc107 !important;
    } */

    /* .choices__item--selectable {
        background-color: #000000ff !important;
        color: #fff !important;
    } */
    
</style>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/users') ?>">Users</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Create New User</h3>
    </div>
    <form action="<?= route_to('users.store') ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('success')) : ?>
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="username">Username <span class="text-danger">*</span></label>
                        <input type="text" name="username" class="form-control <?= isInvalid('username') ?>" id="username" placeholder="Enter username" value="<?= old('username') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('username', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Unique username for login</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email">Email Address <span class="text-danger">*</span></label>
                        <input type="email" name="email" class="form-control <?= isInvalid('email') ?>" id="email" placeholder="Enter email address" value="<?= old('email') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('email', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Valid email address for notifications</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="first_name">First Name <span class="text-danger">*</span></label>
                        <input type="text" name="first_name" class="form-control <?= isInvalid('first_name') ?>" id="first_name" placeholder="Enter first name" value="<?= old('first_name') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('first_name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">User's first name</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="last_name">Last Name <span class="text-danger">*</span></label>
                        <input type="text" name="last_name" class="form-control <?= isInvalid('last_name') ?>" id="last_name" placeholder="Enter last name" value="<?= old('last_name') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('last_name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">User's last name</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password">Password <span class="text-danger">*</span></label>
                        <input type="password" name="password" class="form-control <?= isInvalid('password') ?>" id="password" placeholder="Enter password">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('password', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Minimum 8 characters with letters and numbers</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password_confirm">Confirm Password <span class="text-danger">*</span></label>
                        <input type="password" name="password_confirm" class="form-control <?= isInvalid('password_confirm') ?>" id="password_confirm" placeholder="Confirm password">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('password_confirm', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Re-enter the password</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="role_ids">Roles <span class="text-danger">*</span></label>
                        <select name="role_ids[]" id="role_ids" class="form-control <?= isInvalid('role_ids') ?>" multiple="multiple" data-placeholder="Select roles">
                            <?php
                            foreach ($roles as $role) : ?>
                                <option value="<?= $role['id'] ?>"><?= $role['name'] ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('role_ids', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Select one or more roles for the user. User will have combined permissions from all assigned roles.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="office_id">Office</label>
                        <select name="office_id" id="office_id" class="form-control <?= isInvalid('office_id') ?>">
                            <option value="">Select Agency (Optional)</option>
                            <?php foreach ($offices as $office) : ?>
                                <option value="<?= $office['id'] ?>" ><?= $office['name'] ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('office_id', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Optional office assignment</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input <?= isInvalid('is_active') ?>" id="is_active" name="is_active"
                                    value="1" <?= old('is_active', '1') ? 'checked' : '' ?>>
                            <label class="custom-control-label" for="is_active">Active</label>
                            <div class="invalid-feedback">
                                <?php echo show_validation_error('is_active', session("errors")); ?>
                            </div>
                            <small class="form-text text-muted">Inactive users cannot login to the system</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create User
            </button>
            <a href="<?= route_to('users.index') ?>" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- Choices.js JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Choices.js for role selection

    const roleSelect = new Choices('#role_ids', {
        removeItemButton: true,
        searchEnabled: true,
        searchChoices: true,
        searchPlaceholderValue: 'Search roles...',
        placeholder: true,
        placeholderValue: 'Select roles for the user',
        itemSelectText: 'Press to select',
        maxItemCount: -1,
        classNames: {
            containerOuter: 'choices',
            containerInner: 'choices__inner',
            input: 'choices__input',
            inputCloned: 'choices__input--cloned',
            list: 'choices__list',
            listItems: 'choices__list--multiple',
            listSingle: 'choices__list--single',
            listDropdown: 'choices__list--dropdown',
            item: 'choices__item',
            itemSelectable: 'choices__item--selectable',
            itemDisabled: 'choices__item--disabled',
            itemChoice: 'choices__item--choice',
            placeholder: 'choices__placeholder',
            group: 'choices__group',
            groupHeading: 'choices__heading',
            button: 'choices__button',
            activeState: 'is-active',
            focusState: 'is-focused',
            openState: 'is-open',
            disabledState: 'is-disabled',
            highlightedState: 'is-highlighted',
            selectedState: 'is-selected',
            flippedState: 'is-flipped',
            loadingState: 'is-loading',
            noResults: 'has-no-results',
            noChoices: 'has-no-choices'
        }
    });

    // Real-time email validation
    $('#email').on('blur', function() {
        var email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('Please enter a valid email address');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Password strength indicator
    $('#password').on('input', function() {
        var password = $(this).val();
        var strength = getPasswordStrength(password);
        var strengthText = '';
        var strengthClass = '';

        if (password.length === 0) {
            strengthText = '';
        } else if (strength < 2) {
            strengthText = 'Weak password';
            strengthClass = 'text-danger';
        } else if (strength < 3) {
            strengthText = 'Medium password';
            strengthClass = 'text-warning';
        } else {
            strengthText = 'Strong password';
            strengthClass = 'text-success';
        }

        if (!$(this).siblings('.password-strength').length) {
            $(this).siblings('.form-text').after('<small class="password-strength"></small>');
        }

        $(this).siblings('.password-strength').text(strengthText).attr('class', 'password-strength ' + strengthClass);
    });

    // Password confirmation validation
    $('#password_confirm').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();

        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('Passwords do not match');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Username validation (no spaces, special characters)
    $('#username').on('input', function() {
        var username = $(this).val();
        var validUsername = /^[a-zA-Z0-9_]+$/.test(username);

        if (username && !validUsername) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('Username can only contain letters, numbers, and underscores');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Form submission validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            }
        });

        // Check password match
        if ($('#password').val() !== $('#password_confirm').val()) {
            $('#password_confirm').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire('Error!', 'Please fill in all required fields correctly.', 'error');
        }
    });

});

function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function getPasswordStrength(password) {
    var strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}
</script>
<?= $this->endSection() ?>