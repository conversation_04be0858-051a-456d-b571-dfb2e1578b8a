<?php

namespace Modules\MenuManagement\Migrations;

use CodeIgniter\Database\Migration;

class AddCssClassAndTargetFields extends Migration
{
    public function up()
    {
        // Add css_class field
        $this->forge->addColumn('menus', [
            'css_class' => [
                'type'       => 'VARCHAR',
                'constraint' => '100',
                'null'       => true,
                'after'      => 'icon',
            ],
        ]);

        // Add target field
        $this->forge->addColumn('menus', [
            'target' => [
                'type'       => 'ENUM',
                'constraint' => ['_self', '_blank', '_parent', '_top'],
                'default'    => '_self',
                'after'      => 'css_class',
            ],
        ]);
    }

    public function down()
    {
        // Remove the added columns
        $this->forge->dropColumn('menus', ['css_class', 'target']);
    }
}
