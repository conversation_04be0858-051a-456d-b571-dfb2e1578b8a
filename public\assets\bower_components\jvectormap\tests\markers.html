<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>Maps</title>
  <link rel="stylesheet" media="all" href="../jquery-jvectormap.css"/>
  <style>
    ul {
      padding: 0;
      list-style: none;
    }
  </style>

  <script src="assets/jquery-1.8.2.js"></script>
  <script src="../jquery-jvectormap.js"></script>
  <script src="../jquery-mousewheel.js"></script>

  <script src="../lib/jvectormap.js"></script>

  <script src="../lib/abstract-element.js"></script>
  <script src="../lib/abstract-canvas-element.js"></script>
  <script src="../lib/abstract-shape-element.js"></script>

  <script src="../lib/svg-element.js"></script>
  <script src="../lib/svg-group-element.js"></script>
  <script src="../lib/svg-canvas-element.js"></script>
  <script src="../lib/svg-shape-element.js"></script>
  <script src="../lib/svg-path-element.js"></script>
  <script src="../lib/svg-circle-element.js"></script>

  <script src="../lib/vml-element.js"></script>
  <script src="../lib/vml-group-element.js"></script>
  <script src="../lib/vml-canvas-element.js"></script>
  <script src="../lib/vml-shape-element.js"></script>
  <script src="../lib/vml-path-element.js"></script>
  <script src="../lib/vml-circle-element.js"></script>

  <script src="../lib/vector-canvas.js"></script>
  <script src="../lib/simple-scale.js"></script>
  <script src="../lib/ordinal-scale.js"></script>
  <script src="../lib/numeric-scale.js"></script>
  <script src="../lib/color-scale.js"></script>
  <script src="../lib/data-series.js"></script>
  <script src="../lib/proj.js"></script>
  <script src="../lib/world-map.js"></script>

  <script src="assets/jquery-jvectormap-us-aea-en.js"></script>
  <script>
    $(function(){
      var markers = [
            [61.18, -149.53],
            [21.18, -157.49],
            {latLng: [40.66, -73.56], name: 'New York City', style: {r: 8, fill: 'yellow'}},
            {latLng: [41.52, -87.37], style: {fill: 'red', r: 10}}
          ],
          values1 = [1, 2, 3, 4],
          values2 = [1, 2, 3, 4];

      var map = new jvm.WorldMap({
        container: $('.map'),
        map: 'us_aea_en',
        markers: markers,
        series: {
          markers: [{
            attribute: 'fill',
            scale: ['#C8EEFF', '#0071A4'],
            normalizeFunction: 'polynomial',
            values: values1
          },{
            attribute: 'r',
            scale: [5, 20],
            normalizeFunction: 'polynomial',
            values: values2
          }],
          regions: [{
            scale: {
              red: '#ff0000',
              blue: '#00ff00'
            },
            attribute: 'fill',
            normalizeFunction: 'polynomial',
            values: {
              "US-KS": 'red',
              "US-MO": 'red',
              "US-IA": 'blue',
              "US-NE": 'blue'
            }
          },{
            values: {
              "US-NY": 'blue',
              "US-FL": 'blue'
            },
            attribute: 'fill'
          }]
        },
        regionsSelectable: true,
        markersSelectable: true,
        markersSelectableOne: true,
        selectedRegions: JSON.parse( window.localStorage.getItem('jvectormap-selected-regions') || '[]' ),
        selectedMarkers: JSON.parse( window.localStorage.getItem('jvectormap-selected-markers') || '[]' ),

        onMarkerLabelShow: function(event, label, index){
          label.html(label.html()+' (modified marker)');
        },
        onMarkerOver: function(event, index){
          console.log('marker-over', index);
        },
        onMarkerOut: function(event, index){
          console.log('marker-out', index);
        },
        onMarkerClick: function(event, index){
          console.log('marker-click', index);
        },
        onMarkerSelected: function(event, index, isSelected, selectedMarkers){
          console.log('marker-select', index, isSelected, selectedMarkers);
          if (window.localStorage) {
            window.localStorage.setItem(
              'jvectormap-selected-markers',
              JSON.stringify(selectedMarkers)
            );
          }
        },

        onRegionLabelShow: function(event, label, code){
          label.html(label.html()+' (modified)');
        },
        onRegionOver: function(event, code){
          console.log('region-over', code, map.getRegionName(code));
        },
        onRegionOut: function(event, code){
          console.log('region-out', code);
        },
        onRegionClick: function(event, code){
          console.log('region-click', code);
        },
        onRegionSelected: function(event, code, isSelected, selectedRegions){
          console.log('region-select', code, isSelected, selectedRegions);
          if (window.localStorage) {
            window.localStorage.setItem(
              'jvectormap-selected-regions',
              JSON.stringify(selectedRegions)
            );
          }
        },
        onViewportChange: function(e, scale, transX, transY){
            console.log('viewportChange', scale, transX, transY);
        }
      });

      $('.list-markers :checkbox').change(function(){
        var index = $(this).closest('li').attr('data-marker-index');

        if ($(this).prop('checked')) {
          map.addMarker( index, markers[index], [values1[index], values2[index]] );
        } else {
          map.removeMarkers( [index] );
        }
      });
      $('.button-add-all').click(function(){
        $('.list-markers :checkbox').prop('checked', true);
        map.addMarkers(markers, [values1, values2]);
      });
      $('.button-remove-all').click(function(){
        $('.list-markers :checkbox').prop('checked', false);
        map.removeAllMarkers();
      });
      $('.button-clear-selected-regions').click(function(){
        map.clearSelectedRegions();
      });
      $('.button-clear-selected-markers').click(function(){
        map.clearSelectedMarkers();
      });
      $('.button-remove-map').click(function(){
        map.remove();
      });
      $('.button-change-values').click(function(){
        map.series.regions[1].clear();
        map.series.regions[1].setValues({
          "US-TX": "black",
          "US-CA": "black"
        });
      });
      $('.button-reset-map').click(function(){
        map.reset();
      });
    });
  </script>
</head>
<body>
  <div class="map" style="width: 800px; height: 500px"></div>
  <ul class="list-markers">
    <li data-marker-index="0"><label><input checked type="checkbox"/> Marker 1</label></li>
    <li data-marker-index="1"><label><input checked type="checkbox"/> Marker 2</label></li>
    <li data-marker-index="2"><label><input checked type="checkbox"/> Marker 3</label></li>
    <li data-marker-index="3"><label><input checked type="checkbox"/> Marker 4</label></li>
  </ul>
  <input type="button" value="Add all" class="button-add-all"/>
  <input type="button" value="Remove all" class="button-remove-all"/>
  &nbsp;&nbsp;&nbsp;
  <input type="button" value="Clear selected regions" class="button-clear-selected-regions"/>
  <input type="button" value="Clear selected markers" class="button-clear-selected-markers"/>
  &nbsp;&nbsp;&nbsp;
  <input type="button" value="Remove map" class="button-remove-map"/>
  &nbsp;&nbsp;&nbsp;
  <input type="button" value="Change values" class="button-change-values"/>
  &nbsp;&nbsp;&nbsp;
  <input type="button" value="Reset map" class="button-reset-map"/>
</body>
</html>