<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?> : <?= $role['name'] ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/roles') ?>">Assign Permissions</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-warning">
    <div class="card-header">
        <h3 class="card-title">Select Permissions</h3>
    </div>
    <form action="<?= site_url('admin/roles/save-permissions/' . $role['id']) ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (!empty($categorizedPermissions)): ?>
                <?php foreach ($categorizedPermissions as $category => $permissions): ?>
                    <div class="mb-4">
                        <h4><?= esc($category) ?></h4>
                        <?php foreach ($permissions as $permission): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="permissions[]" value="<?= $permission['id'] ?>" id="permission_<?= $permission['id'] ?>"
                                    <?= in_array($permission['id'], $assignedPermissionIds) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="permission_<?= $permission['id'] ?>">
                                    <?= esc($permission['name']) ?> (<?= esc($permission['description']) ?>)
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p>No permissions available. Please create some permissions first.</p>
            <?php endif; ?>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-success rounded-md shadow-sm" <?= empty($categorizedPermissions) ? 'disabled' : '' ?>>Save Permissions</button>
            <a href="<?= site_url('admin/roles') ?>" class="btn btn-secondary rounded-md ml-2">Cancel</a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>