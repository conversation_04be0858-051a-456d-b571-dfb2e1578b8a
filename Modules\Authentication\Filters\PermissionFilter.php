<?php

namespace Modules\Authentication\Filters;

use <PERSON>Igniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;
use App\Services\PermissionService;

class PermissionFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $requiredPermission = $arguments[0]; // e.g., "manage_users"
        $userId = session()->get('user_id');

        if (!$userId) {
            return redirect()->to('/auth/login')->with('error', 'Access denied. Please login first.');
        }

        // Use the PermissionService to check user permissions across all roles
        $permissionService = new PermissionService();

        if (!$permissionService->userHasPermission($userId, $requiredPermission)) {
            return redirect()->to('/')->with('error', 'You do not have permission to access this page.');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Optional post-processing
    }
}
