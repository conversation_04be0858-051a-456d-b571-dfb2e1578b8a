<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= route_to('offices.index') ?>">Agency Management</a></li>
<li class="breadcrumb-item active">Edit</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Edit Office</h3>
    </div>
    <form action="<?= route_to('offices.update', $office['id']); ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Office Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control <?= isInvalid('name') ?>" id="name" placeholder="Enter office name" value="<?= old('name', $office['name']) ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">The official name of the office</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="code">Office Code <span class="text-danger">*</span></label>
                        <input type="text" name="code" class="form-control <?= isInvalid('code') ?>" id="code" placeholder="Enter office code" value="<?= old('code', $office['code']) ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('code', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Unique code identifier for the office</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" name="email" class="form-control <?= isInvalid('email') ?>" id="email" placeholder="Enter email address" value="<?= old('email', $office['email']) ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('email', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Primary email for this office</small>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" class="form-control <?= isInvalid('description') ?>" id="description" rows="4" placeholder="Enter office description"><?= old('description', $office['description']) ?></textarea>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('description', session("errors")); ?>
                </div>
                <small class="form-text text-muted">Brief description about the office and its services</small>
            </div>

            <div class="form-group">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                            value="1" <?= old('is_active', $office['is_active']) ? 'checked' : '' ?>>
                    <label class="custom-control-label" for="is_active">Active</label>
                    <small class="form-text text-muted">Inactive offices will not be available for user assignment</small>
                </div>
            </div>

            <!-- Agency Information -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title">Record Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Created:</strong><br>
                                    <?= date('F d, Y \a\t g:i A', strtotime($office['created_at'])) ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Last Updated:</strong><br>
                                    <?= date('F d, Y \a\t g:i A', strtotime($office['updated_at'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Agency
            </button>
            <a href="<?= route_to('offices.index') ?>" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
            <?php if (hasPermission('agency.manage')) : ?>
            <a href="<?= route_to('offices.delete', $office['id'])?>" class="ajxdelete btn btn-danger float-right" data-id="<?= $office['id'] ?>">
                <i class="fas fa-trash"></i> Delete Agency
            </a>
            <?php endif; ?>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Character counter for description
    $('#description').on('input', function() {
        var maxLength = 500;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }
        
        $(this).next('.char-counter').text(remaining + ' characters remaining');
        
        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // Trigger character counter on page load
    // $('#description').trigger('input');

    // Highlight changes
    // $('input, textarea, select').on('change', function() {
    //     $(this).addClass('border-warning');
    // });

    // Delete agency
    $(document).on('click', '.ajxdelete', function(e) {
        e.preventDefault();
        href = $(this).attr('href');
        //sweetalert
        swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = href;
            }
        });
        return false;
        
    });
});
// CSRF token variables
var csrfName = '<?= csrf_token() ?>';
var csrfHash = '<?= csrf_hash() ?>';
</script>
<?= $this->endSection() ?>
