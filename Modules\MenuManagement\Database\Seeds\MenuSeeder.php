<?php

namespace Modules\MenuManagement\Database\Seeds;

use CodeIgniter\Database\Seeder;

class MenuSeeder extends Seeder
{
    public function run()
    {
        // Get permission IDs for reference
        $permissions = $this->db->table('permissions')->get()->getResultArray();
        $permissionMap = [];
        foreach ($permissions as $permission) {
            $permissionMap[$permission['name']] = $permission['id'];
        }

        // Sample menu data
        $menus = [
            [
                'label' => 'Dashboard',
                'url' => 'admin/dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'permission_id' => null, // Public access
                'parent_id' => null,
                'sort_order' => 1,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'label' => 'User Management',
                'url' => null, // Parent menu
                'icon' => 'fas fa-users',
                'permission_id' => isset($permissionMap['manage_users']) ? $permissionMap['manage_users'] : null,
                'parent_id' => null,
                'sort_order' => 2,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'label' => 'Role Management',
                'url' => null, // Parent menu
                'icon' => 'fas fa-user-shield',
                'permission_id' => isset($permissionMap['role.manage']) ? $permissionMap['role.manage'] : null,
                'parent_id' => null,
                'sort_order' => 3,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'label' => 'System Settings',
                'url' => null, // Parent menu
                'icon' => 'fas fa-cogs',
                'permission_id' => isset($permissionMap['system.manage']) ? $permissionMap['system.manage'] : null,
                'parent_id' => null,
                'sort_order' => 4,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert parent menus first
        $this->db->table('menus')->insertBatch($menus);

        // Get the inserted parent menu IDs
        $insertedMenus = $this->db->table('menus')->get()->getResultArray();
        $menuMap = [];
        foreach ($insertedMenus as $menu) {
            $menuMap[$menu['label']] = $menu['id'];
        }

        // Child menus
        $childMenus = [
            // User Management children
            [
                'label' => 'Manage Users',
                'url' => 'admin/users',
                'icon' => 'far fa-circle',
                'permission_id' => isset($permissionMap['manage_users']) ? $permissionMap['manage_users'] : null,
                'parent_id' => isset($menuMap['User Management']) ? $menuMap['User Management'] : null,
                'sort_order' => 1,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'label' => 'Add User',
                'url' => 'admin/users/create',
                'icon' => 'far fa-circle',
                'permission_id' => isset($permissionMap['manage_users']) ? $permissionMap['manage_users'] : null,
                'parent_id' => isset($menuMap['User Management']) ? $menuMap['User Management'] : null,
                'sort_order' => 2,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            // Role Management children
            [
                'label' => 'Manage Roles',
                'url' => 'admin/roles',
                'icon' => 'far fa-circle',
                'permission_id' => isset($permissionMap['role.manage']) ? $permissionMap['role.manage'] : null,
                'parent_id' => isset($menuMap['Role Management']) ? $menuMap['Role Management'] : null,
                'sort_order' => 1,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'label' => 'Add Role',
                'url' => 'admin/roles/create',
                'icon' => 'far fa-circle',
                'permission_id' => isset($permissionMap['role.manage']) ? $permissionMap['role.manage'] : null,
                'parent_id' => isset($menuMap['Role Management']) ? $menuMap['Role Management'] : null,
                'sort_order' => 2,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            // System Settings children
            [
                'label' => 'Menu Management',
                'url' => 'admin/menu-management',
                'icon' => 'far fa-circle',
                'permission_id' => isset($permissionMap['menu.manage']) ? $permissionMap['menu.manage'] : null,
                'parent_id' => isset($menuMap['System Settings']) ? $menuMap['System Settings'] : null,
                'sort_order' => 1,
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert child menus
        $this->db->table('menus')->insertBatch($childMenus);
    }
}
