<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title) ?></title>
    <script src="[https://cdn.tailwindcss.com](https://cdn.tailwindcss.com)"></script>
    <link href="[https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap](https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap)" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f4f7f6;
            color: #333;
        }
        .container {
            max-width: 900px;
        }
        .card {
            background-color: #ffffff;
            border-radius: 0.75rem; /* rounded-xl */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .detail-row {
            display: flex;
            margin-bottom: 0.75rem;
        }
        .detail-label {
            font-weight: 600;
            color: #4a5568; /* text-gray-700 */
            width: 150px; /* Fixed width for labels */
            flex-shrink: 0;
        }
        .detail-value {
            color: #333;
            flex-grow: 1;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="p-6">
    <div class="container mx-auto mt-8 p-6 card">
        <h1 class="text-3xl font-bold text-gray-800 mb-6"><?= esc($title) ?></h1>

        <div class="mb-4">
            <a href="<?= site_url('audit-trail') ?>" class="inline-flex items-center px-4 py-2 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition duration-200">
                &larr; Back to Audit Logs
            </a>
        </div>

        <?php if (empty($auditTrail)): ?>
            <p class="text-red-500">Audit trail entry not found.</p>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mb-6">
                <div class="detail-row">
                    <div class="detail-label">ID:</div>
                    <div class="detail-value"><?= esc($auditTrail['id']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">User ID:</div>
                    <div class="detail-value"><?= esc($auditTrail['user_id'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Event:</div>
                    <div class="detail-value"><?= esc($auditTrail['event']) ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Auditable Type:</div>
                    <div class="detail-value"><?= esc($auditTrail['auditable_type'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Auditable ID:</div>
                    <div class="detail-value"><?= esc($auditTrail['auditable_id'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">IP Address:</div>
                    <div class="detail-value"><?= esc($auditTrail['ip_address'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">URL:</div>
                    <div class="detail-value break-all"><?= esc($auditTrail['url'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Method:</div>
                    <div class="detail-value"><?= esc($auditTrail['method'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">User Agent:</div>
                    <div class="detail-value text-sm break-all"><?= esc($auditTrail['user_agent'] ?? 'N/A') ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Timestamp:</div>
                    <div class="detail-value"><?= esc($auditTrail['created_at']) ?></div>
                </div>
            </div>

            <h2 class="text-xl font-semibold text-gray-800 mb-3">Old Values:</h2>
            <?php if (!empty($auditTrail['old_values'])): ?>
                <pre><?= json_encode($auditTrail['old_values'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) ?></pre>
            <?php else: ?>
                <p class="text-gray-600">No old values recorded.</p>
            <?php endif; ?>

            <h2 class="text-xl font-semibold text-gray-800 mt-6 mb-3">New Values:</h2>
            <?php if (!empty($auditTrail['new_values'])): ?>
                <pre><?= json_encode($auditTrail['new_values'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) ?></pre>
            <?php else: ?>
                <p class="text-gray-600">No new values recorded.</p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>