/* Include Font Awesome icons (replace with your icon library if needed) */
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css");



/* ... and so on for other button variants */



.button-row {
  display: flex;
  flex-wrap: wrap; /* Allow buttons to wrap to next line on smaller screens */
  justify-content: space-around;
}

.custom-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-size: 1rem;
  background-color: #f0f0f0;
  border: none;
  cursor: pointer;
  color: #262626;
  padding: 15px 20px;
  min-width: 250px;
  border-radius: 10px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.75);
  transition: all 0.2s ease-in-out;
  background-color: transparent;
  height: auto;
  min-height:60px;
  flex-basis: 100%; /* Make buttons full width on small screens */
  margin: 10px; /* Add spacing between buttons */
  transition: all 0.3s ease-in-out;
}


/* Media query for larger screens */
@media (min-width: 768px) {
  .custom-button {
    flex-basis: auto; /* Reset width for larger screens */
    margin: 0; /* Remove margin on larger screens */
  }
}


.custom-button:hover {
/*  background-color: #e0e0e0;*/
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px); /* Add subtle lift on hover */
}

.custom-button:active {
  background-color: #d0d0d0;
  transform: translateY(2px); /* Add slight press effect */
}