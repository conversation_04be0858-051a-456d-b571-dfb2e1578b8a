<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/permissions') ?>">Permissions</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Display flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<!-- Display validation errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <strong>Validation Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-key"></i> Create New Permission</h3>
            </div>
            <form action="<?= site_url('admin/permissions/store') ?>" method="post" id="permissionForm">
                <?= csrf_field() ?>
                <div class="card-body">
                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag"></i> Permission Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" class="form-control <?= session('errors.name') ? 'is-invalid' : '' ?>"
                               value="<?= old('name') ?>" required placeholder="Enter permission name">
                        <?php if (session('errors.name')): ?>
                            <div class="invalid-feedback"><?= session('errors.name') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Enter a unique name for this permission (e.g., "user.create", "user.manage")</small>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left"></i> Description</label>
                        <textarea name="description" id="description" class="form-control <?= session('errors.description') ? 'is-invalid' : '' ?>"
                                  rows="4" placeholder="Enter permission description"><?= old('description') ?></textarea>
                        <?php if (session('errors.description')): ?>
                            <div class="invalid-feedback"><?= session('errors.description') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Provide a brief description of what this permission allows</small>
                    </div>

                    <div class="form-group">
                        <label for="category"><i class="fas fa-folder"></i> Category</label>
                        
                        <input list="categoryOptions"
                            type="text"
                            name="category"
                            id="category"
                            class="form-control <?= session('errors.category') ? 'is-invalid' : '' ?>"
                            value="<?= old('category') ?>"
                            placeholder="Enter category name">
                        
                        <datalist id="categoryOptions">
                            <?php foreach ($categories as $category): ?>
                            <!-- Add more categories as needed -->
                                <option value="<?= $category['category'] ?>"><?= $category['category'] ?></option>
                            <?php endforeach; ?>
                        </datalist>

                        <?php if (session('errors.category')): ?>
                            <div class="invalid-feedback"><?= session('errors.category') ?></div>
                        <?php endif; ?>

                        <small class="form-text text-muted">
                            Group related permissions together (e.g., "User Management", "Reports")
                        </small>
                    </div>

                    <!-- <div class="form-group">
                        <label for="category"><i class="fas fa-folder"></i> Category</label>
                        <input type="text" name="category" id="category" class="form-control <?//= session('errors.category') ? 'is-invalid' : '' ?>"
                               value="<?//= old('category') ?>" placeholder="Enter category name">
                        <?php //if (session('errors.category')): ?>
                            <div class="invalid-feedback"><?//= session('errors.category') ?></div>
                        <?php //endif; ?>
                        <small class="form-text text-muted">Group related permissions together (e.g., "User Management", "Content Editing")</small>
                    </div>-->
                </div> 

                <div class="card-footer bg-light">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Permission
                    </button>
                    <a href="<?= site_url('admin/permissions') ?>" class="btn btn-secondary ml-2">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-info-circle"></i> Permission Guidelines</h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-lightbulb"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Best Practices</span>
                        <span class="info-box-number">Naming</span>
                    </div>
                </div>

                <h6><i class="fas fa-check-circle text-success"></i> Naming Convention:</h6>
                <ul class="list-unstyled ml-3">
                    <li><code>module.manage</code> format</li>
                    <li><code>user.create</code></li>
                    <li><code>purchase.view_own</code></li>
                    <li><code>report.export</code></li>
                </ul>

                <h6><i class="fas fa-tags text-info"></i> Common Categories:</h6>
                <ul class="list-unstyled ml-3">
                    <li>• User Management</li>
                    <li>• System Administration</li>
                    <li>• Reporting</li>
                </ul>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> Permissions define what actions users can perform. Be specific and descriptive.
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>

<script>
$(document).ready(function() {
    // Form validation
    $('#permissionForm').on('submit', function(e) {
        var name = $('#name').val().trim();

        if (name === '') {
            e.preventDefault();
            $('#name').addClass('is-invalid');
            if (!$('#name').next('.invalid-feedback').length) {
                $('#name').after('<div class="invalid-feedback">Permission name is required.</div>');
            }
            return false;
        } else {
            $('#name').removeClass('is-invalid');
            $('#name').next('.invalid-feedback').remove();
        }
    });

    // Real-time validation
    $('#name').on('input', function() {
        var name = $(this).val().trim();
        if (name !== '') {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Auto-suggest category based on permission name
    $('#name').on('input', function() {
        var name = $(this).val().toLowerCase();
        var category = '';

        if (name.includes('user')) category = 'User Management';
        else if (name.includes('role') || name.includes('permission')) category = 'Role Management';
        else if (name.includes('post') || name.includes('content')) category = 'Content Management';
        else if (name.includes('system') || name.includes('admin')) category = 'System Administration';
        else if (name.includes('report')) category = 'Reporting';

        if (category && !$('#category').val()) {
            $('#category').val(category);
        }
    });
});
</script>
<?= $this->endSection() ?>