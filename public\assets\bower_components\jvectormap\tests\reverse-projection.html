<!DOCTYPE html>
<html>
<head>
  <title>jVectorMap demo</title>
  <link rel="stylesheet" media="all" href="../jquery-jvectormap.css"/>
  <script src="assets/jquery-1.8.2.js"></script>
  <script src="../jquery-jvectormap.js"></script>
  <script src="../jquery-mousewheel.js"></script>

  <script src="../lib/jvectormap.js"></script>

  <script src="../lib/abstract-element.js"></script>
  <script src="../lib/abstract-canvas-element.js"></script>
  <script src="../lib/abstract-shape-element.js"></script>

  <script src="../lib/svg-element.js"></script>
  <script src="../lib/svg-group-element.js"></script>
  <script src="../lib/svg-canvas-element.js"></script>
  <script src="../lib/svg-shape-element.js"></script>
  <script src="../lib/svg-path-element.js"></script>
  <script src="../lib/svg-circle-element.js"></script>

  <script src="../lib/vml-element.js"></script>
  <script src="../lib/vml-group-element.js"></script>
  <script src="../lib/vml-canvas-element.js"></script>
  <script src="../lib/vml-shape-element.js"></script>
  <script src="../lib/vml-path-element.js"></script>
  <script src="../lib/vml-circle-element.js"></script>

  <script src="../lib/vector-canvas.js"></script>
  <script src="../lib/simple-scale.js"></script>
  <script src="../lib/numeric-scale.js"></script>
  <script src="../lib/ordinal-scale.js"></script>
  <script src="../lib/color-scale.js"></script>
  <script src="../lib/data-series.js"></script>
  <script src="../lib/proj.js"></script>
  <script src="../lib/world-map.js"></script>

  <script src="assets/jquery-jvectormap-us-lcc-en.js"></script>
  <script>
    $(function(){
      var map,
          markerIndex = 0,
          markersCoords = {};

      map = new jvm.WorldMap({
          map: 'us_lcc_en',
          markerStyle: {
            initial: {
              fill: 'red'
            }
          },
          container: $('#map'),
          onMarkerLabelShow: function(e, label, code){
            map.label.text(markersCoords[code].lat.toFixed(2)+' '+markersCoords[code].lng.toFixed(2));
          },
          onMarkerClick: function(e, code){
            map.removeMarkers([code]);
            map.label.hide();
          }
      });

      $('#map').click(function(e){
          var latLng = map.pointToLatLng(e.offsetX, e.offsetY),
              targetCls = $(e.target).attr('class');

          if (latLng && (!targetCls || (targetCls && $(e.target).attr('class').indexOf('jvectormap-marker') === -1))) {
            markersCoords[markerIndex] = latLng;
            map.addMarker(markerIndex, {latLng: [latLng.lat, latLng.lng]});
            markerIndex += 1;
          }
      });
      $('#map').bind('');
    });
  </script>
</head>
<body>
  <div id="map" style="width: 900px; height: 600px"></div>
</body>
</html>