<?php

namespace Modules\RoleManagement\Controllers;

use App\Controllers\BaseController;
use Modules\UserManagement\Models\UserModel; 
use Modules\RoleManagement\Models\RoleModel;
use Modules\RoleManagement\Models\UserRoleModel;

class UserRoleController extends BaseController
{
    protected $userModel;
    protected $roleModel;
    protected $userRoleModel; // New property for direct interaction


    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->userRoleModel = new UserRoleModel(); // Initialize
    }

    /**
     * Role Management Dashboard
     */
    public function dashboard()
    {
        // Get some users for the dropdown
        $users = $this->userModel->select('id, username, email')
                                 ->where('is_active', 1)
                                 ->orderBy('username', 'ASC')
                                 ->limit(20)
                                 ->findAll();

        $data = [
            'users' => $users
        ];

        return view('Modules\RoleManagement\Views\users\role_management_dashboard', $data);
    }

    public function assignRoles($userId)
    {
        $user = $this->userModel->find($userId);
        if (empty($user)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found: ' . $userId);
        }

        $allRoles = $this->roleModel->findAll();
        $assignedRoles = $this->userModel->getRoles($userId); // This now fetches pivot data

        // Reformat assigned roles for easier lookup in the view
        $assignedRoleData = [];
        foreach ($assignedRoles as $ar) {
            $assignedRoleData[$ar['id']] = $ar; // Key by role ID
        }

        $data = [
            'title' => 'Assign Roles',
            'page_title' => 'Assign Roles',
            'user' => $user,
            'allRoles' => $allRoles,
            'assignedRoleData' => $assignedRoleData, // Pass the detailed assigned role data
        ];

        return view('Modules\RoleManagement\Views\users\assign_roles', $data);
    }

    public function saveRoles($userId)
    {
        $user = $this->userModel->find($userId);
        if (empty($user)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found: ' . $userId);
        }

        // Get all submitted role assignments
        $submittedRoles = $this->request->getPost('roles') ?? [];
        $submittedExpiresAt = $this->request->getPost('expires_at') ?? [];
        $submittedIsActive = $this->request->getPost('is_active') ?? [];

        // Validate that at least one role is selected
        if (empty($submittedRoles)) {
            return redirect()->back()->withInput()->with('error', 'User must have at least one role assigned.');
        }

        $roleAssignments = [];
        $assignedByUserId = session('user_id') ?? null;
        $hasActiveRole = false;

        foreach ($submittedRoles as $roleId) {
            $isActive = isset($submittedIsActive[$roleId]) ? 1 : 0;
            $expiresAt = !empty($submittedExpiresAt[$roleId]) ? $submittedExpiresAt[$roleId] : null;

            // Check if this role will be active and not expired
            $isRoleValid = false;
            if ($isActive) {
                if (empty($expiresAt)) {
                    // No expiration date = permanent role = valid
                    $isRoleValid = true;
                } else {
                    // Check if expiration date is in the future
                    try {
                        $expiryDateTime = new \DateTime($expiresAt);
                        $currentDateTime = new \DateTime();
                        if ($expiryDateTime > $currentDateTime) {
                            $isRoleValid = true;
                        }
                    } catch (\Exception $e) {
                        // Invalid date format, will be caught by validation later
                        log_message('warning', 'Invalid expiration date format: ' . $expiresAt);
                    }
                }
            }

            if ($isRoleValid) {
                $hasActiveRole = true;
            }

            $roleAssignments[] = [
                'role_id'     => (int) $roleId,
                'assigned_by' => $assignedByUserId,
                'expires_at'  => $expiresAt,
                'is_active'   => $isActive,
            ];
        }

        // Validate that user will have at least one active, non-expired role
        if (!$hasActiveRole) {
            // Provide more detailed error message for debugging
            $debugInfo = [];
            foreach ($roleAssignments as $assignment) {
                $debugInfo[] = "Role {$assignment['role_id']}: Active={$assignment['is_active']}, Expires={$assignment['expires_at']}";
            }
            
            return redirect()->back()->withInput()->with('error', 'User must have at least one active, non-expired role. Please ensure at least one role is marked as active and either has no expiration date or expires in the future.');
        }

        // Validate the incoming data
        $validationRules = [
            'roles.*' => 'required|integer',
            'expires_at.*' => 'permit_empty|valid_date',
            'is_active.*' => 'permit_empty|in_list[0,1]',
        ];

        // Validate the form data
        if (!$this->validate($validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Additional validation for expiry dates
        foreach ($submittedExpiresAt as $roleId => $expiresAt) {
            if (!empty($expiresAt)) {
                $expiryDate = strtotime($expiresAt);
                if ($expiryDate && $expiryDate <= time()) {
                    return redirect()->back()->withInput()->with('error', 'Expiry date must be in the future.');
                }
            }
        }

        try {
            if ($this->userModel->assignRoles($userId, $roleAssignments)) {
                return redirect()->to(site_url('admin/users'))
                              ->with('success', 'Roles assigned successfully.');
                // return redirect()->to(site_url('admin/users/assign-roles/' . $userId))
                //               ->with('success', 'Roles assigned successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to assign roles. Please check that the user has at least one active role.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Role assignment error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to assign roles: ' . $e->getMessage());
        }
    }

    /**
     * Extend role expiration for a user
     */
    public function extendRoleExpiration($userId, $roleId)
    {
        $user = $this->userModel->find($userId);
        if (empty($user)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found: ' . $userId);
        }

        if ($this->request->getMethod() === 'POST') {
            $newExpiresAt = $this->request->getPost('expires_at');

            if (empty($newExpiresAt)) {
                return redirect()->back()->with('error', 'Expiry date is required.');
            }

            if (strtotime($newExpiresAt) <= time()) {
                return redirect()->back()->with('error', 'Expiry date must be in the future.');
            }

            if ($this->userRoleModel->extendRoleExpiration($userId, $roleId, $newExpiresAt)) {
                return redirect()->to(site_url('admin/users/assign-roles/' . $userId))
                              ->with('success', 'Role expiration extended successfully.');
            } else {
                return redirect()->back()->with('error', 'Failed to extend role expiration.');
            }
        }

        // Get role details
        $role = $this->roleModel->find($roleId);
        $userRole = $this->userRoleModel->where('user_id', $userId)
                                       ->where('role_id', $roleId)
                                       ->first();

        $data = [
            'user' => $user,
            'role' => $role,
            'userRole' => $userRole
        ];

        return view('Modules\RoleManagement\Views\users\extend_role_expiration', $data);
    }

    /**
     * View expired roles for a user
     */
    public function viewExpiredRoles($userId)
    {
        $user = $this->userModel->find($userId);
        if (empty($user)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found: ' . $userId);
        }

        $expiredRoles = $this->userRoleModel->getExpiredUserRoles($userId);

        $data = [
            'user' => $user,
            'expiredRoles' => $expiredRoles
        ];

        return view('Modules\RoleManagement\Views\users\expired_roles', $data);
    }
}