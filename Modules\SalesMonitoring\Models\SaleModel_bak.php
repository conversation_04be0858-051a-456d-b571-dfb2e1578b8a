<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;

class SaleModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'e_sales';
    protected $primaryKey       = 'sales_id';
    protected $useAutoIncrement = true;
    protected $returnType       = \App\Entities\Sale::class;
    protected $useSoftDeletes   = true;
    // protected $protectFields    = true;
    // protected $allowedFields    = ['ff_code','prod_code','buyer_co_name','buyer_type','sale_date','sales_type','intl_booked','intl_undernego','dom_bookd','dom_undernego','retail_bookd','country_to_export','cost','faircode','minor_prod_code','minor_prod_desc','maj_prod_code','maj_desc','exh_co_name','exh_region','continent','date_added','date_modified','deleted_at'];

    protected $allowedFields    = ['cost','status','type','ff_code','buyer_name','buyer_type','sale_date','exh_co_name','exh_region','country','export_continent','minor_prod_code','minor_prod_desc','major_prod_code','major_prod_desc','fair_code','sector','post_event'];

    protected $updatesFields    = [
        'a.sales_id','a.minor_prod_desc', 'a.buyer_name','a.country','a.cost','a.type', 'b.updated_cost', 'a.status', 'b.updated_status', 'a.sale_date AS original_date', 'b.date_modified AS updated_date'
    ];

    // protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // protected array $casts = [];
    // protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'date_added';
    protected $updatedField  = 'date_modified';
    protected $deletedField  = 'deleted_at';

    // Validation
    // protected $validationRules      = [];
    // protected $validationMessages   = [];
    // protected $skipValidation       = false;
    // protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];


    public function findByExhId($ff_code,$fair_code){
        return $this->where(['ff_code'=>$ff_code,'faircode'=>$fair_code])->findAll();
    }

    public function getSales($id){
        return $this->select('sales_id,sale_date,buyer_name,minor_prod_desc,country,cost,type,`status`',FALSE)
            ->orderBy('sale_date','asc')->where('deleted_at IS NULL')
            ->where(['type!='=>'retail','cost!='=>0])
            ->where(['ff_code'=>$id,'fair_code'=>session()->fair_code]);

    }

    public function getSalesUpdatesList($id,$fair_code){
        return $this->db->table($this->table.' a')
            ->select($this->updatesFields)
            ->join('e_sales_updates as b', 'a.sales_id = b.sales_id', 'left')
            ->orderBy('a.sales_id','DESC')->where(['type!='=>'retail','cost!='=>0])
            ->where(['ff_code'=>$id,'a.fair_code'=>$fair_code]);
            // $query = $builder->get();
            // $results['data'] = $query->getRow();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $sql = str_replace("\n", " ", $sql);
            // echo $sql;exit();
    }

    public function getSalesUpdatesSummary($fair_code){
        return $this->db->table($this->table.' a')
                ->select("
                    ff_code,
                    exh_co_name,
                    SUM(IF(type = 'Export' AND status = 'Booked' AND post_event='No', cost, 0)) AS event_export_booked,
                    SUM(IF(type = 'Export' AND status = 'Under Negotiation' AND post_event='No', cost, 0)) AS event_export_undernego,
                    SUM(IF(type = 'Export' AND updated_status != 'Cancelled', updated_cost, 0) + IF(updated_status IS NULL AND type = 'Export' AND status = 'Booked' AND post_event='No', cost, 0)) AS updated_export_booked,
                    SUM(IF(type = 'Export' AND status = 'Under Negotiation' AND updated_status IS NULL AND post_event='No', cost, 0)) AS updated_export_undernego,
                    SUM(IF(type ='Export' AND post_event = 'Yes' AND status = 'Booked' AND updated_status IS NULL , cost, 0)) AS added_export_booked,
                    SUM(IF(type ='Export' AND post_event = 'Yes' AND status = 'Under Negotiation' AND updated_status IS NULL , cost, 0)) AS added_export_undernego,
                    SUM(IF(type = 'Domestic' AND status = 'Booked' AND post_event='No', cost, 0)) AS event_domestic_booked,
                    SUM(IF(type = 'Domestic' AND status = 'Under Negotiation' AND post_event='No', cost, 0)) AS event_domestic_undernego,
                    SUM(IF(type = 'Domestic' AND updated_status != 'Cancelled', updated_cost, 0) + IF(updated_status IS NULL AND type = 'Domestic' AND status = 'Booked' AND post_event='No', cost, 0)) AS updated_domestic_booked,
                    SUM(IF(type = 'Domestic' AND status = 'Under Negotiation' AND updated_status IS NULL AND post_event='No', cost, 0)) AS updated_domestic_undernego,
                    SUM(IF(type ='Domestic' AND post_event = 'Yes' AND status = 'Booked' AND updated_status IS NULL , cost, 0)) AS added_domestic_booked,
                    SUM(IF(type ='Domestic' AND post_event = 'Yes' AND status = 'Under Negotiation' AND updated_status IS NULL , cost, 0)) AS added_domestic_undernego,
                    SUM(IF(type ='Export' AND updated_status = 'Cancelled', cost, 0)) AS export_sales_cancelled,
                    SUM(IF(type ='Domestic' AND updated_status = 'Cancelled', cost, 0)) AS domestic_sales_cancelled
                ")
                ->join('e_sales_updates b', 'a.sales_id = b.sales_id', 'left')
                ->where('a.fair_code', $fair_code)
                ->groupBy('a.ff_code')
                ->orderBy('a.exh_co_name');


    }


}


