<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/roles') ?>">Roles</a></li>
<li class="breadcrumb-item active">Edit</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Edit Role: <?= esc($role['name']) ?></h3>
    </div>
    <form action="<?= route_to('roles.update', $role['id']) ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('success')) : ?>
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Role Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control <?= isInvalid('name') ?>" id="name" placeholder="Enter role name" value="<?= old('name', $role['name']) ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Unique name for the role</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Empty column for layout balance -->
                </div>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" class="form-control <?= isInvalid('description') ?>" id="description" rows="4" placeholder="Enter role description"><?= old('description', $role['description']) ?></textarea>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('description', session("errors")); ?>
                </div>
                <small class="form-text text-muted">Brief description of the role and its purpose</small>
            </div>

            <div class="form-group">
                <label>Permissions</label>
                <div>
                    <?php if (empty($permissions_grouped)) : ?>
                        <div class="alert alert-info">No permissions defined in the system.</div>
                    <?php else : ?>
                        <?php foreach ($permissions_grouped as $category => $perms_in_category) : ?>
                            <div class="card card-secondary">
                                <div class="card-header">
                                    <h4 class="card-title w-100">
                                        <a class="d-block w-100" data-toggle="collapse" href="#collapse<?= url_title($category, '-', true) ?>"
                                           aria-expanded="true" aria-controls="collapse<?= url_title($category, '-', true) ?>">
                                            <?= esc($category) ?>
                                        </a>
                                    </h4>
                                </div>
                                <div id="collapse<?= url_title($category, '-', true) ?>" class="collapse show">
                                    <div class="card-body">
                                        <div class="row">
                                            <?php if (!empty($perms_in_category)) : ?>
                                                <?php foreach ($perms_in_category as $permission) : ?>
                                                    <div class="col-sm-6 col-md-4">
                                                        <div class="custom-control custom-checkbox">
                                                            <?php
                                                            // Check if this permission was previously selected (if form submitted with errors)
                                                            // or if it's currently assigned to the role
                                                            $checked = (in_array($permission['id'], old('permissions', $assignedPermissions))) ? 'checked' : '';
                                                            ?>
                                                            <input class="custom-control-input" type="checkbox" id="permission_<?= $permission['id'] ?>" name="permissions[]" value="<?= $permission['id'] ?>" <?= $checked ?>>
                                                            <label for="permission_<?= $permission['id'] ?>" class="custom-control-label"><?= esc($permission['name']) ?></label>
                                                            <?php if (!empty($permission['description'])): ?>
                                                                <small class="form-text text-muted" style="margin-left: 25px;"><?= esc($permission['description']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php else : ?>
                                                <div class="col-12">No permissions found in this category.</div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Role Information -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title">Record Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Created:</strong><br>
                                    <?= date('F d, Y \a\t g:i A', strtotime($role['created_at'])) ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Last Updated:</strong><br>
                                    <?= date('F d, Y \a\t g:i A', strtotime($role['updated_at'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Role
            </button>
            <a href="<?= route_to('roles.index') ?>" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Character counter for description
    $('#description').on('input', function() {
        var maxLength = 500;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' characters remaining');

        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // Select all permissions in a category
    $('.card-header').on('click', function() {
        var categoryCard = $(this).closest('.card');
        var checkboxes = categoryCard.find('input[type="checkbox"]');
        var allChecked = checkboxes.length === checkboxes.filter(':checked').length;

        // Add a "Select All" button if it doesn't exist
        if (!$(this).find('.select-all-btn').length) {
            $(this).find('.card-title a').after(
                '<button type="button" class="btn btn-sm btn-outline-secondary ml-2 select-all-btn">Select All</button>'
            );
        }
    });

    // Handle select all button
    $(document).on('click', '.select-all-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var categoryCard = $(this).closest('.card');
        var checkboxes = categoryCard.find('input[type="checkbox"]');
        var allChecked = checkboxes.length === checkboxes.filter(':checked').length;

        checkboxes.prop('checked', !allChecked);
        $(this).text(allChecked ? 'Select All' : 'Deselect All');
    });

    // Highlight changes
    $('input, textarea, select').on('change', function() {
        $(this).addClass('border-warning');
    });

    // Form validation
    $('form').on('submit', function(e) {
        var roleName = $('#name').val().trim();

        if (!roleName) {
            e.preventDefault();
            $('#name').addClass('is-invalid');
            $('#name').siblings('.invalid-feedback').text('Role name is required');
            Swal.fire('Error!', 'Please enter a role name.', 'error');
            return false;
        }

        // Check if at least one permission is selected
        var selectedPermissions = $('input[name="permissions[]"]:checked').length;
        if (selectedPermissions === 0) {
            e.preventDefault();
            Swal.fire('Warning!', 'Please select at least one permission for this role.', 'warning');
            return false;
        }
    });

    // Real-time role name validation
    $('#name').on('input', function() {
        var roleName = $(this).val().trim();
        if (roleName.length > 0 && roleName.length < 3) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('Role name must be at least 3 characters');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
<?= $this->endSection() ?>