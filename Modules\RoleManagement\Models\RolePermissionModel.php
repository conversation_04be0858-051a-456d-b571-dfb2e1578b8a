<?php

namespace Modules\RoleManagement\Models;

use CodeIgniter\Model;

class RolePermissionModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'role_permissions';
    protected $primaryKey = 'role_id';
    protected $useAutoIncrement = false;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false; // Roles are usually not soft deleted
    protected $protectFields    = true;
    protected $allowedFields = ['role_id', 'permission_id'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = '';

  
}
