<?php

namespace Modules\Authentication\Config;

$routes->group('auth', ['namespace' => 'Modules\Authentication\Controllers'], function ($routes) {
    $routes->GET('login', 'AuthController::login', ['as' => 'auth.login']);
    $routes->POST('login', 'AuthController::attemptLogin');
    $routes->POST('login', 'AuthController::attemptCinLogin');
    $routes->GET('register', 'AuthController::register', ['as' => 'auth.register','filter'=>'login']);
    $routes->POST('register', 'AuthController::attemptRegister');
    $routes->GET('logout', 'AuthController::logout', ['as' => 'auth.logout']);

    $routes->GET('forgot-password', 'AuthController::forgotPassword', ['as' => 'auth.forgotPassword']);
    $routes->POST('forgot-password', 'AuthController::sendResetLink');
    $routes->GET('reset-password/(:hash)', 'AuthController::resetPassword/$1', ['as' => 'auth.resetPassword']);
    $routes->POST('reset-password/(:hash)', 'AuthController::attemptResetPassword/$1');
});

$routes->group('profile', ['namespace' => 'Modules\Authentication\Controllers'], function ($routes) {
    $routes->GET('/', 'ProfileController::index', ['as' => 'profile.index']);
    $routes->GET('edit', 'ProfileController::edit', ['as' => 'profile.edit']);
    $routes->POST('update', 'ProfileController::update', ['as' => 'profile.update']);
});
