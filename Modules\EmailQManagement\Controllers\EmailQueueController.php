<?php

namespace Modules\EmailQManagement\Controllers;

use App\Controllers\BaseController;
use Mo<PERSON>les\EmailQManagement\Models\EmailQueueModel;
use Modules\EmailQManagement\Libraries\EmailService;
use CodeIgniter\CLI\CommandRunner;
use Her<PERSON>wan\DataTables\DataTable;

class EmailQueueController extends BaseController
{
    protected $emailQueueModel;

    public function __construct()
    {
        $this->emailQueueModel = new EmailQueueModel();
    }

    public function index()
    {
        // if (!hasPermission('manage.email_queue')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage email queue.');
        // }

        $menu = [
        ['label' => 'Dashboard', 'url' => 'dashboard', 'icon' => 'fas fa-tachometer-alt'],
        ['label' => 'Users', 'url' => 'users', 'icon' => 'fas fa-users'],
        ['label' => 'Settings', 'url' => 'settings', 'icon' => 'fas fa-cogs'],
        ];

        // Count statuses
        $data['count'] = [
            'pending' => $this->emailQueueModel->where('status', 'pending')->countAllResults(),
            'sent'    => $this->emailQueueModel->where('status', 'sent')->countAllResults(),
            'failed'  => $this->emailQueueModel->where('status', 'failed')->countAllResults(),
            'total'   => $this->emailQueueModel->countAllResults(),
        ];

        $data = [
            'title' => 'Email Queue Management',
            'page_title' => 'Email Queue Management',
            'count' => $data['count'],
        ];

        return view('Modules\EmailQManagement\Views\index', ['menu' => $menu] + $data);
    }

    /**
     * DataTable endpoint for email queue management
     */
    public function datatable()
    {
        // if (!hasPermission('manage.email_queue')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $builder = $this->emailQueueModel->builder();

        return DataTable::of($builder)
            ->add('checkbox', function ($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            }, 'first')
            ->edit('to_email', function ($row) {
                return '<span title="' . esc($row->to_email) . '">' . esc($row->to_email) . '</span>';
            })
            ->edit('subject', function ($row) {
                $subject = $row->subject ?: '(No Subject)';
                return '<span title="' . esc($subject) . '">' .
                       (strlen($subject) > 50 ? esc(substr($subject, 0, 50)) . '...' : esc($subject)) .
                       '</span>';
            })
            ->edit('template', function ($row) {
                return $row->template ? '<code>' . esc($row->template) . '</code>' : '<span class="text-muted">None</span>';
            })
            ->edit('status', function ($row) {
                $badgeClass = match($row->status) {
                    'sent' => 'badge-success',
                    'failed' => 'badge-danger',
                    'pending' => 'badge-warning',
                    default => 'badge-secondary'
                };
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($row->status) . '</span>';
            })
            ->edit('attempts', function ($row) {
                $class = $row->attempts >= 3 ? 'text-danger' : ($row->attempts > 0 ? 'text-warning' : 'text-muted');
                return '<span class="' . $class . '">' . $row->attempts . '</span>';
            })
            ->edit('error', function ($row) {
                if ($row->error) {
                    $error = strlen($row->error) > 50 ? substr($row->error, 0, 50) . '...' : $row->error;
                    return '<span class="text-danger" title="' . esc($row->error) . '">' . esc($error) . '</span>';
                }
                return '<span class="text-muted">None</span>';
            })
            ->edit('created_at', function ($row) {
                return date('Y-m-d H:i:s', strtotime($row->created_at));
            })
            ->edit('sent_at', function ($row) {
                return $row->sent_at ? date('Y-m-d H:i:s', strtotime($row->sent_at)) : '<span class="text-muted">Not sent</span>';
            })
            ->add('actions', function ($row) {
                $actions = '<div class="btn-group" role="group">';

                // Re-send Now button
                $actions .= '<button type="button" class="btn btn-sm btn-info btn-resend-now" data-id="' . $row->id . '" title="Re-send Now">
                                <i class="fas fa-paper-plane"></i>
                            </button>';

                // Re-queue button
                $actions .= '<button type="button" class="btn btn-sm btn-warning btn-requeue" data-id="' . $row->id . '" title="Re-queue">
                                <i class="fas fa-redo"></i>
                            </button>';

                // Delete button
                $actions .= '<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="' . $row->id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>';

                $actions .= '</div>';
                return $actions;
            })
            ->toJson(true);
    }

    public function retryNow($id)
    {
 
        $email = $this->emailQueueModel->find($id);

        if (!$email) {
            return redirect()->back()->with('error', 'Email not found.');
        }

        $service = new EmailService();
        $result = $service->sendTemplatedEmail($email);

        $this->emailQueueModel->update($id, [
            'status'   => $result === true ? 'sent' : 'failed',
            'error'    => $result === true ? null : $result,
            'attempts' => $email['attempts'] + 1,
            'sent_at'  => $result === true ? date('Y-m-d H:i:s') : null,
        ]);

        return redirect()->back()->with(
            $result === true ? 'message' : 'error',
            $result === true ? 'Email sent successfully!' : "Send failed: $result"
        );
    }

    public function retryLater($id)
    {

        $email = $this->emailQueueModel->find($id);

        if (!$email) {
            return redirect()->back()->with('error', 'Email not found.');
        }

        $this->emailQueueModel->update($id, [
            'status'   => 'pending',
            'error'    => null,
            'attempts' => 0
        ]);

        return redirect()->back()->with('message', 'Email requeued for processing.');
    }

    public function retry($id)
    {
        // if (!hasPermission('manage.email_queue')) {
        //     return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage email queue.');
        // }

        $this->emailQueueModel->update($id, [
            'status' => 'pending',
            'attempts' => 0,
            'error' => null
        ]);

        return redirect()->back()->with('message', 'Email retried.');
    }

    public function delete($id)
    {
        $this->emailQueueModel->delete($id);

        return redirect()->back()->with('message', 'Email deleted.');
    }

    public function bulkAction()
    {
        $action = $this->request->getPost('action');
        $ids = $this->request->getPost('ids[]');

        if (!$ids || !in_array($action, ['retry', 'delete'])) {
            return redirect()->back()->with('error', 'Invalid action or no items selected.');
        }

        // echo $action;
        // echo '<pre>';
        // print_r($ids);
        // echo '</pre>';
        // die;

        if ($action == 'retry') {
            foreach ($ids as $id) {
                $this->emailQueueModel->update($id, [
                    'status' => 'pending',
                    'attempts' => 0,
                    'error' => null
                ]);
            }
            return redirect()->back()->with('message', 'Emails retried.');
        } elseif ($action == 'delete') {
            foreach ($ids as $id) {
                $this->emailQueueModel->delete($id);
            }
            return redirect()->back()->with('message', 'Emails deleted.');
        } else {
            return redirect()->back()->with('error', 'Invalid action.');
        }
        return redirect()->back()->with('error', 'No action taken.');
    }

    public function runQueueNow()
    {
        try {
            $processor = new \Modules\EmailQManagement\Libraries\EmailQueueProcessor();
            $results = $processor->run();

            $successCount = count(array_filter($results, fn($r) => str_starts_with($r['result'], 'Sent')));
            $failCount = count($results) - $successCount;

            return redirect()->back()->with('message', "Queue processed: $successCount sent, $failCount failed.");
        } catch (\Throwable $e) {
            return redirect()->back()->with('error', 'Failed to process queue: ' . $e->getMessage());
        }
    }

    /**
     * Bulk retry emails
     */
    public function bulkRetry()
    {
        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        try {
            $this->emailQueueModel->whereIn('id', $ids)->set([
                'status' => 'pending',
                'attempts' => 0,
                'error' => null
            ])->update();

            return $this->response->setJSON(['success' => true, 'message' => 'Selected emails requeued successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to requeue emails: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk delete emails
     */
    public function bulkDelete()
    {
        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        try {
            $this->emailQueueModel->whereIn('id', $ids)->delete();
            return $this->response->setJSON(['success' => true, 'message' => 'Selected emails deleted successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete emails: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk resend emails
     */
    public function bulkResend()
    {
        $ids = $this->request->getPost('ids');
        if (empty($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No items selected']);
        }

        try {
            $emails = $this->emailQueueModel->whereIn('id', $ids)->findAll();
            $service = new EmailService();
            $successCount = 0;
            $failCount = 0;

            foreach ($emails as $email) {
                $result = $service->sendTemplatedEmail($email);

                $this->emailQueueModel->update($email['id'], [
                    'status'   => $result === true ? 'sent' : 'failed',
                    'error'    => $result === true ? null : $result,
                    'attempts' => $email['attempts'] + 1,
                    'sent_at'  => $result === true ? date('Y-m-d H:i:s') : null,
                ]);

                if ($result === true) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Bulk resend completed: {$successCount} sent, {$failCount} failed"
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to resend emails: ' . $e->getMessage()]);
        }
    }

    public function preview($id)
    {
        $email = $this->emailQueueModel->find($id);

        if (!$email) {
            return redirect()->back()->with('error', 'Email not found.');
        }

        $template = $email['template'] ?? 'default';
        $templateData = json_decode($email['template_data'] ?? '', true) ?? [];

        if (!view()->exists("Modules\\EmailQManagement\\Views\\templates\\$template")) {
            return redirect()->back()->with('error', 'Template not found.');
        }

        $data['email'] = $email;
        $data['content'] = view("Modules\\EmailQManagement\\Views\\templates\\$template", $templateData);

        return view('Modules\EmailQManagement\Views\preview', $data);
    }   

    public function testEmail(){
        $emailService = new EmailService();

        $result = $emailService->sendTemplatedEmail([
            'to'      => '<EMAIL>',
            'subject' => 'Welcome to Our App',
            'heading' => 'Hello, John!',
            'body'    => 'Thanks for signing up. This is a test email using PHPMailer and an HTML template.',
            'template' => 'welcome_test',
            'template_data' => json_encode([
                'name' => 'John',
                'message' => 'Thanks for signing up. This is a test email using PHPMailer and an HTML template.'
            ])
        ]);

        echo $result === true ? 'Email sent successfully!' : $result;
    }

    public function testEmailPlaceholder(){
        $emailService = new EmailService();

        $result = $emailService->sendTemplatedEmail([
            'to_email'      => '<EMAIL>',
            'subject' => 'Welcome to Our App',
            'heading' => 'Hello, John!',
            'body'    => 'Thanks for signing up. This is a test email using PHPMailer and an HTML template.',
            'template' => 'welcome_placeholder',
            'template_data' => json_encode([
                'name' => 'John',
                'message' => 'Thanks for signing up. This is a test email using PHPMailer and an HTML template.'
            ])
        ]);

        echo $result === true ? 'Email sent successfully!' : $result;
    }


}
