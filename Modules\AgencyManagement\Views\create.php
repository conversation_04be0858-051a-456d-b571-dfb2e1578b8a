<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= route_to('agencies.index') ?>">Agency Management</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Create New Agency</h3>
    </div>
    <form action="<?= route_to('agencies.store'); ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Agency Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control <?= isInvalid('name') ?>" id="name" placeholder="Enter agency name" value="<?= old('name') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">The official name of the agency</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="code">Agency Code <span class="text-danger">*</span></label>
                        <input type="text" name="code" class="form-control <?= isInvalid('code') ?>" id="code" placeholder="Enter agency code" value="<?= old('code') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('code', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Unique code identifier for the agency</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="contact_person">Contact Person</label>
                        <input type="text" name="contact_person" class="form-control <?= isInvalid('contact_person') ?>" id="contact_person" placeholder="Enter contact person name" value="<?= old('contact_person') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('contact_person', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Primary contact person for this agency</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="text" name="email" class="form-control <?= isInvalid('email') ?>" id="email" placeholder="Enter email address" value="<?= old('email') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('email', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Official email address of the agency</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="contact_number">Contact Number</label>
                        <input type="text" name="contact_number" class="form-control <?= isInvalid('contact_number') ?>" id="contact_number" placeholder="Enter contact number" value="<?= old('contact_number') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('contact_number', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Phone number for the agency</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Empty column for layout balance -->
                </div>
            </div>

            <div class="form-group">
                <label for="address">Address</label>
                <textarea name="address" class="form-control <?= isInvalid('address') ?>" id="address" rows="3" placeholder="Enter agency address"><?= old('address') ?></textarea>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('address', session("errors")); ?>
                </div>
                <small class="form-text text-muted">Physical address of the agency</small>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" class="form-control <?= isInvalid('description') ?>" id="description" rows="4" placeholder="Enter agency description"><?= old('description') ?></textarea>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('description', session("errors")); ?>
                </div>
                <small class="form-text text-muted">Brief description about the agency and its services</small>
            </div>

            <div class="form-group">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input <?= isInvalid('is_active') ?>" id="is_active" name="is_active" 
                            value="1" <?= old('is_active', '1') ? 'checked' : '' ?>>
                    <label class="custom-control-label" for="is_active">Active</label>
                    <div class="invalid-feedback">
                        <?php echo show_validation_error('is_active', session("errors")); ?>
                    </div>
                    <small class="form-text text-muted">Inactive agencies will not be available for user assignment</small>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create Agency
            </button>
            <a href="<?= route_to('agencies.index') ?>" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Character counter for description
    $('#description').on('input', function() {
        var maxLength = 500;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }
        
        $(this).next('.char-counter').text(remaining + ' characters remaining');
        
        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // Trigger character counter on page load
    $('#description').trigger('input');
});
</script>
<?= $this->endSection() ?>
