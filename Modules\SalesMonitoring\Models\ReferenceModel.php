<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;
use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class ReferenceModel extends Model
{
    protected $DBGroup = 'masterfile';
    protected $table            = 'v_reference';
    protected $primaryKey       = 'id';
    // protected $useAutoIncrement = true;
    protected $returnType       = \Modules\SalesMonitoring\Entities\Reference::class;
    protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = [];


    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }

//----------------------------------------------------------------

    public function get_countries(){
        $this->db->select('countryid,country', FALSE);
        $this->db->from($this->_table);
        // $this->db->where('ff_code', $ff_code, FALSE);
        $q=$this->db->get();
        return $q->result_array();
    }

//----------------------------------------------------------------

    public function get_countries_nophil(){
        $sql = "SELECT countryid,country
            FROM ".$this->_table." 
            WHERE country NOT IN('philippines','not indicated')
            AND status=1
            ORDER BY country;
            ";
        $query = $this->db->query($sql);
        return $query->result_array();
        }    


    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }

    
}
