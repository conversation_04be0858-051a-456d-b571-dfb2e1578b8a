<?= $this->extend('layouts/default'); ?>

<?= $this->section('title'); ?>Receivables - CARMS<?= $this->endSection(); ?>

<?= $this->section('header'); ?>

<?= $this->endSection(); ?>


<?= $this->section("content") ?>




<div class="card">
    <div class="card-header">
        <h3 class="card-title">Exhibitor Login (CIN/CIP) List</h3>
        <a href="<?= route_to('cin.create') ?>" class="btn btn-primary float-right">Add User</a>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Company Name</th>
                    <th>Email</th>
                    <th>Sector</th>
                    <th>CIN</th>
                    <th>CIP</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($cinList as $cin): ?>
                <tr>
                    <td><?= $cin['refno'] ?></td>
                    <td><?= $cin['co_name'] ?></td>
                    <td><?= $cin['co_email'] ?></td>
                    <td><?= $cin['sector'] ?></td>
                    <td><?= $cin['cin'] ?></td>
                    <td><?= $cin['cip'] ?></td>
                    <td>
                        <a href="<?= route_to('user.edit', $cin['refno']) ?>" class="btn btn-info btn-sm">Generate CIP</a>
                        <a href="<?= route_to('user.edit', $cin['refno']) ?>" class="btn btn-warning btn-sm">Edit</a>
                        <a href="<?= route_to('cin.delete', $cin['refno']) ?>" class="btn btn-danger btn-sm">Delete</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>


<?= $this->endSection(); ?>