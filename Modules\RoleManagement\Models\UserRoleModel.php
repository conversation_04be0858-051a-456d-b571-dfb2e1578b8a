<?php

namespace Modules\RoleManagement\Models;

use CodeIgniter\Model;

class UserRoleModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'user_roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields = ['user_id', 'role_id', 'assigned_by', 'expires_at', 'is_active'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'assigned_at';
    protected $updatedField  = 'updated_at';

    // protected $validationRules    = [
    //     'user_id'     => 'required|integer',
    //     'role_id'     => 'required|integer',
    //     'assigned_by' => 'permit_empty|integer',
    //     'expires_at'  => 'permit_empty|valid_date',
    //     'is_active'   => 'permit_empty|in_list[0,1]',
    // ];
    // protected $validationMessages = [];
    // protected $skipValidation     = false;

    

    /**
     * Custom method to handle the unique key constraint for user_id and role_id
     * since CodeIgniter's default `save()` doesn't handle composite unique keys for `insertBatch` well.
     * This method will remove existing entries for a user and then insert new ones.
     *
     * @param int $userId
     * @param array $data An array of role assignments, each containing 'role_id', 'assigned_by', 'expires_at', 'is_active'.
     * @return bool
     */
    public function syncUserRoles(int $userId, array $roleAssignments)
    {
        // Validate that user will have at least one active, non-expired role
        if (!$this->validateMinimumActiveRoles($roleAssignments)) {
            throw new \InvalidArgumentException('User must have at least one active, non-expired role.');
        }

        // Start a transaction
        $this->db->transBegin();

        try {
            // 1. Delete all existing roles for this user
            $this->where('user_id', $userId)->delete();

            // 2. Prepare data for batch insert
            $insertData = [];
            foreach ($roleAssignments as $assignment) {
                // Validate expires_at format if provided
                $expiresAt = null;
                if (!empty($assignment['expires_at'])) {
                    $expiresAt = $this->validateAndFormatDate($assignment['expires_at']);
                }

                $insertData[] = [
                    'user_id'     => $userId,
                    'role_id'     => $assignment['role_id'],
                    'assigned_by' => $assignment['assigned_by'] ?? null,
                    'assigned_at' => date('Y-m-d H:i:s'),
                    'expires_at'  => $expiresAt,
                    'is_active'   => $assignment['is_active'] ?? 1,
                    'created_at'  => date('Y-m-d H:i:s'),
                    'updated_at'  => date('Y-m-d H:i:s'),
                ];
            }

            if (!empty($insertData)) {
                // Insert new roles
                $this->insertBatch($insertData);
            }

            $this->db->transCommit();
            return true;
        } catch (\Exception $e) {
            $this->db->transRollback();
            log_message('error', 'Failed to sync user roles: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate that there's at least one active, non-expired role
     *
     * @param array $roleAssignments
     * @return bool
     */
    private function validateMinimumActiveRoles(array $roleAssignments): bool
    {
        $hasActiveRole = false;
        $currentDateTime = new \DateTime();

        foreach ($roleAssignments as $assignment) {
            $isActive = ($assignment['is_active'] ?? 1) == 1;

            // Check if role is not expired
            $isNotExpired = true;
            if (!empty($assignment['expires_at'])) {
                try {
                    $expiryDateTime = new \DateTime($assignment['expires_at']);
                    $isNotExpired = $expiryDateTime > $currentDateTime;
                } catch (\Exception $e) {
                    // Invalid date format, consider as expired
                    $isNotExpired = false;
                    log_message('warning', 'Invalid expiration date in role assignment: ' . $assignment['expires_at']);
                }
            }

            if ($isActive && $isNotExpired) {
                $hasActiveRole = true;
                break;
            }
        }

        return $hasActiveRole;
    }

    /**
     * Validate and format date for expires_at field
     *
     * @param string $date
     * @return string|null
     */
    private function validateAndFormatDate(string $date): ?string
    {
        if (empty($date)) {
            return null;
        }

        // Try different date formats
        $formats = [
            'Y-m-d\TH:i',        // HTML5 datetime-local format
            'Y-m-d H:i:s',       // Standard datetime format
            'Y-m-d H:i',         // Datetime without seconds
            'Y-m-d',             // Date only
        ];

        $dateTime = null;
        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            if ($dateTime !== false) {
                break;
            }
        }

        // If still no valid date, try generic parsing
        if ($dateTime === false) {
            try {
                $dateTime = new \DateTime($date);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException("Invalid date format for expires_at: {$date}");
            }
        }

        // If only date was provided, set time to end of day
        if (strlen($date) === 10) {
            $dateTime->setTime(23, 59, 59);
        }

        // Validate that the date is in the future
        $currentDateTime = new \DateTime();
        if ($dateTime <= $currentDateTime) {
            throw new \InvalidArgumentException("Expiration date must be in the future: {$date}");
        }

        return $dateTime->format('Y-m-d H:i:s');
    }

    /**
     * Get active roles for a user (non-expired and active)
     *
     * @param int $userId
     * @return array
     */
    public function getActiveUserRoles(int $userId): array
    {
        return $this->db->table('user_roles ur')
                       ->select('ur.*, r.name as role_name, r.description as role_description')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->where('ur.user_id', $userId)
                       ->where('ur.is_active', 1)
                       ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                       ->orderBy('ur.assigned_at', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get expired roles for a user
     *
     * @param int $userId
     * @return array
     */
    public function getExpiredUserRoles(int $userId): array
    {
        return $this->db->table('user_roles ur')
                       ->select('ur.*, r.name as role_name, r.description as role_description')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->where('ur.user_id', $userId)
                       ->where('ur.expires_at IS NOT NULL')
                       ->where('ur.expires_at <= NOW()')
                       ->orderBy('ur.expires_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Extend role expiration date
     *
     * @param int $userId
     * @param int $roleId
     * @param string $newExpiresAt
     * @return bool
     */
    public function extendRoleExpiration(int $userId, int $roleId, string $newExpiresAt): bool
    {
        $expiresAt = $this->validateAndFormatDate($newExpiresAt);

        return $this->where('user_id', $userId)
                   ->where('role_id', $roleId)
                   ->set([
                       'expires_at' => $expiresAt,
                       'updated_at' => date('Y-m-d H:i:s')
                   ])
                   ->update();
    }

    /**
     * Deactivate expired roles (can be run as a scheduled task)
     *
     * @return int Number of roles deactivated
     */
    public function deactivateExpiredRoles(): int
    {
        $affectedRows = $this->where('expires_at IS NOT NULL')
                            ->where('expires_at <= NOW()')
                            ->where('is_active', 1)
                            ->set([
                                'is_active' => 0,
                                'updated_at' => date('Y-m-d H:i:s')
                            ])
                            ->update();

        if ($affectedRows > 0) {
            log_message('info', "Deactivated {$affectedRows} expired user roles.");
        }

        return $affectedRows;
    }

    /**
     * Get all roles assigned to a specific user, including user_roles pivot data.
     * Moved from UserModel for better separation of concerns.
     *
     * @param int $userId
     * @return array
     */
    public function getUserRoles(int $userId): array
    {
        return $this->db->table('user_roles ur')
                        ->select('r.id, r.name, r.description, ur.id as user_role_id, ur.assigned_by, ur.assigned_at, ur.expires_at, ur.is_active')
                        ->join('roles r', 'r.id = ur.role_id')
                        ->where('ur.user_id', $userId)
                        ->orderBy('ur.assigned_at', 'ASC')
                        ->get()
                        ->getResultArray();
    }

    /**
     * Get all roles assigned to a user (simplified version)
     * Moved from UserModel for better separation of concerns.
     *
     * @param int $userId
     * @return array
     */
    public function getUserRolesList(int $userId): array
    {
        return $this->db->table('user_roles ur')
                       ->select('ur.*, r.name as role_name, r.description as role_description')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->where('ur.user_id', $userId)
                       ->where('ur.is_active', 1)
                       ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                       ->orderBy('ur.assigned_at', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Add a single role to a user
     * Moved from UserModel for better separation of concerns.
     *
     * @param int $userId
     * @param int $roleId
     * @param int|null $assignedBy
     * @param string|null $expiresAt
     * @return bool
     */
    public function addUserRole(int $userId, int $roleId, ?int $assignedBy = null, ?string $expiresAt = null): bool
    {
        // Check if role is already assigned and active
        $existing = $this->where('user_id', $userId)
                        ->where('role_id', $roleId)
                        ->where('is_active', 1)
                        ->where('(expires_at IS NULL OR expires_at > NOW())')
                        ->first();

        if ($existing) {
            return true; // Role already assigned and active
        }

        // Validate expiration date if provided
        $validatedExpiresAt = null;
        if ($expiresAt) {
            $validatedExpiresAt = $this->validateAndFormatDate($expiresAt);
        }

        $data = [
            'user_id' => $userId,
            'role_id' => $roleId,
            'assigned_by' => $assignedBy,
            'assigned_at' => date('Y-m-d H:i:s'),
            'expires_at' => $validatedExpiresAt,
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->insert($data) !== false;
    }

    /**
     * Remove a role from a user (deactivate)
     * Moved from UserModel for better separation of concerns.
     *
     * @param int $userId
     * @param int $roleId
     * @return bool
     */
    public function removeUserRole(int $userId, int $roleId): bool
    {
        return $this->where('user_id', $userId)
                   ->where('role_id', $roleId)
                   ->set([
                       'is_active' => 0,
                       'updated_at' => date('Y-m-d H:i:s')
                   ])
                   ->update();
    }

    /**
     * Set user roles (replace all existing roles)
     * Moved from UserModel for better separation of concerns.
     *
     * @param int $userId
     * @param array $roleIds
     * @param int|null $assignedBy
     * @return bool
     */
    public function setUserRoles(int $userId, array $roleIds, ?int $assignedBy = null): bool
    {
        if (empty($roleIds)) {
            throw new \InvalidArgumentException('User must have at least one role assigned.');
        }

        // Prepare role assignments
        $roleAssignments = [];
        foreach ($roleIds as $roleId) {
            $roleAssignments[] = [
                'role_id' => $roleId,
                'assigned_by' => $assignedBy,
                'expires_at' => null, // Default to permanent
                'is_active' => 1
            ];
        }

        return $this->syncUserRoles($userId, $roleAssignments);
    }

    /**
     * Get users with their roles for display purposes
     * Moved from UserModel for better separation of concerns.
     *
     * @return array
     */
    public function getUsersWithRoles(): array
    {
        $userModel = new \Modules\UserManagement\Models\UserModel();
        $users = $userModel->findAll();

        // Add roles for each user
        foreach ($users as &$user) {
            $user['roles'] = $this->getUserRolesList($user['id']);
            // For backward compatibility, set primary role
            $user['role_name'] = !empty($user['roles']) ? $user['roles'][0]['role_name'] : null;
        }

        return $users;
    }

    /**
     * Check if a user has a specific role (active and non-expired)
     *
     * @param int $userId
     * @param int $roleId
     * @return bool
     */
    public function userHasRoleById(int $userId, int $roleId): bool
    {
        $role = $this->where('user_id', $userId)
                    ->where('role_id', $roleId)
                    ->where('is_active', 1)
                    ->where('(expires_at IS NULL OR expires_at > NOW())')
                    ->first();

        return !empty($role);
    }

    /**
     * Get role assignment history for a user
     *
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function getUserRoleHistory(int $userId, int $limit = 50): array
    {
        return $this->db->table('user_roles ur')
                       ->select('ur.*, r.name as role_name, r.description as role_description, u.username as assigned_by_username')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->join('users u', 'u.id = ur.assigned_by', 'left')
                       ->where('ur.user_id', $userId)
                       ->orderBy('ur.created_at', 'DESC')
                       ->limit($limit)
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get users who have a specific role
     *
     * @param int $roleId
     * @param bool $activeOnly
     * @return array
     */
    public function getUsersByRole(int $roleId, bool $activeOnly = true): array
    {
        $builder = $this->db->table('user_roles ur')
                           ->select('ur.*, u.username, u.email, u.first_name, u.last_name')
                           ->join('users u', 'u.id = ur.user_id')
                           ->where('ur.role_id', $roleId);

        if ($activeOnly) {
            $builder->where('ur.is_active', 1)
                   ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())');
        }

        return $builder->orderBy('u.username', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get role assignment statistics
     *
     * @return array
     */
    public function getRoleAssignmentStats(): array
    {
        $stats = [];

        // Total assignments
        $stats['total_assignments'] = $this->countAll();

        // Active assignments
        $stats['active_assignments'] = $this->where('is_active', 1)
                                           ->where('(expires_at IS NULL OR expires_at > NOW())')
                                           ->countAllResults();

        // Expired assignments
        $stats['expired_assignments'] = $this->where('expires_at IS NOT NULL')
                                            ->where('expires_at <= NOW()')
                                            ->countAllResults();

        // Inactive assignments
        $stats['inactive_assignments'] = $this->where('is_active', 0)
                                             ->countAllResults();

        // Assignments expiring soon (within 7 days)
        $stats['expiring_soon'] = $this->where('expires_at IS NOT NULL')
                                      ->where('expires_at > NOW()')
                                      ->where('expires_at <= DATE_ADD(NOW(), INTERVAL 7 DAY)')
                                      ->where('is_active', 1)
                                      ->countAllResults();

        return $stats;
    }

    /**
     * Get roles expiring within specified days
     *
     * @param int $days
     * @return array
     */
    public function getRolesExpiringSoon(int $days = 7): array
    {
        return $this->db->table('user_roles ur')
                       ->select('ur.*, u.username, u.email, r.name as role_name')
                       ->join('users u', 'u.id = ur.user_id')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->where('ur.expires_at IS NOT NULL')
                       ->where('ur.expires_at > NOW()')
                       ->where("ur.expires_at <= DATE_ADD(NOW(), INTERVAL {$days} DAY)")
                       ->where('ur.is_active', 1)
                       ->orderBy('ur.expires_at', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    
}
