<?php

namespace Modules\CinAuthentication\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public array $login = [
        // 'username' => 'required|min_length[3]|max_length[50]',
        'email'    => 'required|valid_email|max_length[100]',
        'password' => 'required',
        // 'role_id'  => 'required|integer',
    ];

    public array $login_errors = [
        // 'username' => [
        //     'required'    => 'The username is required.',
        //     'min_length'  => 'The username must be at least 3 characters.',
        //     'max_length'  => 'The username must not exceed 50 characters.',
        // ],
        'email' => [
            'required'    => 'The email is required.',
            'valid_email' => 'The email must be a valid email address.',
            'max_length'  => 'The email must not exceed 100 characters.',
        ],
        'password' => [
            'required'   => 'The password is required.',
            // 'min_length' => 'The password must be at least 4 characters.',
        ],
        // 'role_id' => [
        //     'required' => 'The role is required.',
        //     'integer'  => 'The role must be a valid integer.',
        // ],
    ];
}
