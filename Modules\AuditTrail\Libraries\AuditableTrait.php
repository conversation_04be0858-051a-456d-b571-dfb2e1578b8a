<?php

namespace Modules\AuditTrail\Libraries;

use Modules\AuditTrail\Libraries\AuditLogger;

/**
 * AuditableTrait
 * 
 * Add this trait to any model to automatically log create, update, and delete events.
 * 
 * Usage:
 * class YourModel extends Model {
 *     use \Modules\AuditTrail\Libraries\AuditableTrait;
 * }
 */
trait AuditableTrait
{
    protected $auditLogger;
    protected $auditableEvents = ['create', 'update', 'delete'];
    protected $auditableFields = []; // Empty means all fields, or specify fields to audit
    protected $auditableExcludeFields = ['created_at', 'updated_at', 'deleted_at']; // Fields to exclude from auditing
    protected $oldData = [];

    /**
     * Initialize audit logging
     */
    protected function initializeAuditLogging()
    {
        if (!$this->auditLogger) {
            $this->auditLogger = new AuditLogger();
        }

        // Register model callbacks for auditing
        if (in_array('create', $this->auditableEvents)) {
            if (!in_array('auditAfterInsert', $this->afterInsert)) {
                $this->afterInsert[] = 'auditAfterInsert';
            }
        }

        if (in_array('update', $this->auditableEvents)) {
            if (!in_array('auditBeforeUpdate', $this->beforeUpdate)) {
                $this->beforeUpdate[] = 'auditBeforeUpdate';
            }
            if (!in_array('auditAfterUpdate', $this->afterUpdate)) {
                $this->afterUpdate[] = 'auditAfterUpdate';
            }
        }

        if (in_array('delete', $this->auditableEvents)) {
            if (!in_array('auditBeforeDelete', $this->beforeDelete)) {
                $this->beforeDelete[] = 'auditBeforeDelete';
            }
            if (!in_array('auditAfterDelete', $this->afterDelete)) {
                $this->afterDelete[] = 'auditAfterDelete';
            }
        }
    }

    /**
     * Get the entity name for auditing
     */
    protected function getAuditableEntityName(): string
    {
        $className = get_class($this);
        $parts = explode('\\', $className);
        $modelName = end($parts);
        
        // Remove 'Model' suffix if present
        if (substr($modelName, -5) === 'Model') {
            $modelName = substr($modelName, 0, -5);
        }
        
        return $modelName;
    }

    /**
     * Filter auditable fields
     */
    protected function filterAuditableFields(array $data): array
    {
        // If specific fields are defined, only include those
        if (!empty($this->auditableFields)) {
            $data = array_intersect_key($data, array_flip($this->auditableFields));
        }

        // Exclude specified fields
        if (!empty($this->auditableExcludeFields)) {
            $data = array_diff_key($data, array_flip($this->auditableExcludeFields));
        }

        return $data;
    }

    /**
     * After insert callback
     */
    public function auditAfterInsert(array $data)
    {
        try {
            if (!$this->auditLogger) {
                $this->initializeAuditLogging();
            }

            $entityName = $this->getAuditableEntityName();

            // Handle different data structures that CodeIgniter might pass
            $entityId = null;
            $newValues = [];

            if (isset($data['id'])) {
                $entityId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            }

            if (isset($data['data'])) {
                $newValues = $this->filterAuditableFields($data['data']);
            } elseif (is_array($data) && !isset($data['id']) && !isset($data['data'])) {
                // Sometimes the data is passed directly
                $newValues = $this->filterAuditableFields($data);
            }

            if ($entityId && !empty($newValues)) {
                $this->auditLogger->logCreate($entityName, $entityId, $newValues);
            }
        } catch (\Exception $e) {
            log_message('error', 'Audit logging failed in afterInsert: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * Before update callback - capture old data
     */
    public function auditBeforeUpdate(array $data)
    {
        try {
            if (!$this->auditLogger) {
                $this->initializeAuditLogging();
            }

            // Handle different data structures
            $entityId = null;
            if (isset($data['id'])) {
                $entityId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            }

            if ($entityId) {
                $oldRecord = $this->find($entityId);
                if ($oldRecord) {
                    $this->oldData[$entityId] = $this->filterAuditableFields($oldRecord);
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Audit logging failed in beforeUpdate: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * After update callback
     */
    public function auditAfterUpdate(array $data)
    {
        try {
            if (!$this->auditLogger) {
                $this->initializeAuditLogging();
            }

            $entityName = $this->getAuditableEntityName();

            // Handle different data structures
            $entityId = null;
            $newValues = [];

            if (isset($data['id'])) {
                $entityId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            }

            if (isset($data['data'])) {
                $newValues = $this->filterAuditableFields($data['data']);
            }

            if ($entityId && !empty($newValues)) {
                $oldValues = $this->oldData[$entityId] ?? [];

                // Only log if there are actual changes
                $changes = array_diff_assoc($newValues, $oldValues);
                if (!empty($changes)) {
                    $this->auditLogger->logUpdate($entityName, $entityId, $oldValues, $newValues);
                }

                // Clean up old data
                unset($this->oldData[$entityId]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Audit logging failed in afterUpdate: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * Before delete callback - capture data to be deleted
     */
    public function auditBeforeDelete(array $data)
    {
        try {
            if (!$this->auditLogger) {
                $this->initializeAuditLogging();
            }

            // Handle different data structures
            $entityId = null;
            if (isset($data['id'])) {
                $entityId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            }

            if ($entityId) {
                $record = $this->find($entityId);
                if ($record) {
                    $this->oldData[$entityId] = $this->filterAuditableFields($record);
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Audit logging failed in beforeDelete: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * After delete callback
     */
    public function auditAfterDelete(array $data)
    {
        try {
            if (!$this->auditLogger) {
                $this->initializeAuditLogging();
            }

            $entityName = $this->getAuditableEntityName();

            // Handle different data structures
            $entityId = null;
            if (isset($data['id'])) {
                $entityId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            }

            if ($entityId) {
                $oldValues = $this->oldData[$entityId] ?? [];

                if (!empty($oldValues)) {
                    $this->auditLogger->logDelete($entityName, $entityId, $oldValues);
                }

                // Clean up old data
                unset($this->oldData[$entityId]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Audit logging failed in afterDelete: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * Get audit logs for this entity
     */
    public function getAuditLogs($entityId = null, $limit = 50)
    {
        if (!$this->auditLogger) {
            $this->initializeAuditLogging();
        }

        $entityName = $this->getAuditableEntityName();
        
        if ($entityId) {
            return $this->auditLogger->getEntityLogs($entityName, $entityId, $limit);
        }

        // If no specific entity ID, get all logs for this entity type
        $auditModel = new \Modules\AuditTrail\Models\AuditLogModel();
        return $auditModel->getLogsByEvent('create', $limit); // or implement a method to get by entity type
    }

    /**
     * Enable/disable specific audit events
     */
    public function setAuditableEvents(array $events)
    {
        $this->auditableEvents = $events;
        $this->initializeAuditLogging();
    }

    /**
     * Set specific fields to audit
     */
    public function setAuditableFields(array $fields)
    {
        $this->auditableFields = $fields;
    }

    /**
     * Set fields to exclude from auditing
     */
    public function setAuditableExcludeFields(array $fields)
    {
        $this->auditableExcludeFields = $fields;
    }
}
