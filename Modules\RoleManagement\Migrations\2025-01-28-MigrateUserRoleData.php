<?php

namespace Modules\RoleManagement\Migrations;

use CodeIgniter\Database\Migration;

class MigrateUserRoleData extends Migration
{
    public function up()
    {
        // Migrate existing user role_id data to user_roles table
        $users = $this->db->table('users')
                          ->select('id, role_id, created_at')
                          ->where('role_id IS NOT NULL')
                          ->where('role_id >', 0)
                          ->get()
                          ->getResultArray();

        if (!empty($users)) {
            $userRolesData = [];
            foreach ($users as $user) {
                $userRolesData[] = [
                    'user_id'     => $user['id'],
                    'role_id'     => $user['role_id'],
                    'assigned_by' => null, // System migration
                    'assigned_at' => $user['created_at'] ?? date('Y-m-d H:i:s'),
                    'expires_at'  => null,
                    'is_active'   => 1,
                    'created_at'  => date('Y-m-d H:i:s'),
                    'updated_at'  => date('Y-m-d H:i:s'),
                ];
            }

            // Insert migrated data
            $this->db->table('user_roles')->insertBatch($userRolesData);
        }

        // Add a backup column for the old role_id before removing it
        $this->forge->addColumn('users', [
            'old_role_id' => [
                'type'     => 'INT',
                'unsigned' => true,
                'null'     => true,
                'after'    => 'role_id',
            ],
        ]);

        // Copy current role_id to backup column
        $this->db->query('UPDATE users SET old_role_id = role_id WHERE role_id IS NOT NULL');
    }

    public function down()
    {
        // Restore role_id from backup if needed
        $this->db->query('UPDATE users SET role_id = old_role_id WHERE old_role_id IS NOT NULL');
        
        // Remove backup column
        $this->forge->dropColumn('users', 'old_role_id');
        
        // Clear user_roles table
        $this->db->table('user_roles')->truncate();
    }
}
