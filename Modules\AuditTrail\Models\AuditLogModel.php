<?php

namespace Modules\AuditTrail\Models;

use CodeIgniter\Model;

class AuditLogModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'audit_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'event',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'created_at',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';
    protected $deletedField  = '';

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get audit trail statistics
     */
    public function getStatistics()
    {
        $today = date('Y-m-d');
        $thisMonth = date('Y-m-01');

        return [
            'total' => $this->countAll(),
            'today' => $this->where('DATE(created_at)', $today)->countAllResults(false),
            'this_month' => $this->where('created_at >=', $thisMonth)->countAllResults(false),
            'by_event' => $this->select('event, COUNT(*) as count')
                               ->groupBy('event')
                               ->orderBy('count', 'DESC')
                               ->findAll(),
            'by_entity' => $this->select('auditable_type, COUNT(*) as count')
                               ->where('auditable_type IS NOT NULL')
                               ->groupBy('auditable_type')
                               ->orderBy('count', 'DESC')
                               ->findAll(),
        ];
    }

    /**
     * Get audit trails for a specific entity
     */
    public function getEntityLogs($entityType, $entityId, $limit = 50)
    {
        return $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                    ->join('users u', 'u.id = audit_logs.user_id', 'left')
                    ->where('audit_logs.auditable_type', $entityType)
                    ->where('audit_logs.auditable_id', $entityId)
                    ->orderBy('audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get audit trails for a specific user
     */
    public function getUserLogs($userId, $limit = 50)
    {
        return $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                    ->join('users u', 'u.id = audit_logs.user_id', 'left')
                    ->where('audit_logs.user_id', $userId)
                    ->orderBy('audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get recent audit trails
     */
    public function getRecentLogs($limit = 100)
    {
        return $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                    ->join('users u', 'u.id = audit_logs.user_id', 'left')
                    ->orderBy('audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Clean old audit logs (older than specified days)
     */
    public function cleanOldLogs($daysToKeep = 365)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        // dd($cutoffDate);
        if ($this->where('created_at <', $cutoffDate)->delete()) {
            return $this->db->affectedRows();
        }
        return 0;
    }

    /**
     * Get audit trails by date range
     */
    public function getLogsByDateRange($startDate, $endDate, $limit = null)
    {
        $builder = $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                        ->join('users u', 'u.id = audit_logs.user_id', 'left')
                        ->where('audit_logs.created_at >=', $startDate)
                        ->where('audit_logs.created_at <=', $endDate)
                        ->orderBy('audit_logs.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get audit trails by event type
     */
    public function getLogsByEvent($event, $limit = 100)
    {
        return $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                    ->join('users u', 'u.id = audit_logs.user_id', 'left')
                    ->where('audit_logs.event', $event)
                    ->orderBy('audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Search audit trails
     */
    public function searchLogs($searchTerm, $limit = 100)
    {
        return $this->select('audit_logs.*, u.username, u.first_name, u.last_name')
                    ->join('users u', 'u.id = audit_logs.user_id', 'left')
                    ->groupStart()
                        ->like('audit_logs.event', $searchTerm)
                        ->orLike('audit_logs.auditable_type', $searchTerm)
                        ->orLike('audit_logs.url', $searchTerm)
                        ->orLike('u.username', $searchTerm)
                        ->orLike('u.first_name', $searchTerm)
                        ->orLike('u.last_name', $searchTerm)
                    ->groupEnd()
                    ->orderBy('audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
}
