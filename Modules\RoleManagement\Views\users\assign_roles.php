<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Role Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Assign Roles to <?= esc($user['username']) ?></h3>
        <div class="card-tools">
            <a href="<?= site_url('admin/users/expired-roles/' . $user['id']) ?>" class="btn btn-warning btn-sm">
                <i class="fas fa-history"></i> View Expired Roles
            </a>
        </div>
    </div>

    <!-- Display flash messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show mx-3 mt-3">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show mx-3 mt-3">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show mx-3 mt-3">
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    <?php endif; ?>

    <form action="<?= site_url('admin/users/save-roles/' . $user['id']) ?>" method="post" id="roleAssignmentForm">
        <?= csrf_field() ?>
        <div class="card-body">
            <!-- User Info -->
            <div class="alert alert-info">
                <h5><i class="icon fas fa-info"></i> Role Assignment Rules</h5>
                <ul class="mb-0">
                    <li><strong>User must have at least one active role</strong></li>
                    <li>Expiry dates must be in the future (leave empty for permanent roles)</li>
                    <li>Expired roles will be automatically deactivated by the system</li>
                    <li>Inactive roles won't grant any permissions</li>
                </ul>
            </div>

            <?php if (!empty($allRoles)): ?>
                <div class="row mb-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllRoles">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllRoles">
                            <i class="fas fa-square"></i> Deselect All
                        </button>
                    </div>
                </div>

                <?php foreach ($allRoles as $role): ?>
                    <?php
                        // Check if this role is currently assigned to the user
                        $isAssigned = isset($assignedRoleData[$role['id']]);
                        $currentExpiry = $isAssigned ? $assignedRoleData[$role['id']]['expires_at'] : '';
                        $currentIsActive = $isAssigned ? $assignedRoleData[$role['id']]['is_active'] : 1;
                        $isExpired = $currentExpiry && strtotime($currentExpiry) <= time();

                        // Format expiry for datetime-local input
                        $formattedExpiry = '';
                        if ($currentExpiry) {
                            $formattedExpiry = date('Y-m-d\TH:i', strtotime($currentExpiry));
                        }
                    ?>
                    <div class="form-group border rounded p-3 mb-3 role-container <?= $isAssigned ? ($isExpired ? 'bg-warning-light' : 'bg-success-light') : 'bg-light' ?>">
                        <div class="form-check mb-2">
                            <input class="form-check-input role-checkbox" type="checkbox" name="roles[]" value="<?= $role['id'] ?>" id="role_<?= $role['id'] ?>"
                                <?= $isAssigned ? 'checked' : '' ?>>
                            <label class="form-check-label font-weight-bold" for="role_<?= $role['id'] ?>">
                                <?= esc($role['name']) ?>
                                <?php if ($isAssigned): ?>
                                    <?php if ($isExpired): ?>
                                        <span class="badge badge-warning ml-2">EXPIRED</span>
                                    <?php elseif ($currentIsActive): ?>
                                        <span class="badge badge-success ml-2">ACTIVE</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary ml-2">INACTIVE</span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </label>
                            <p class="text-muted mb-2"><?= esc($role['description']) ?></p>
                        </div>

                        <div class="row role-details" style="<?= $isAssigned ? '' : 'display: none;' ?>">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expires_at_<?= $role['id'] ?>">
                                        <i class="fas fa-calendar-alt"></i> Expires At
                                    </label>
                                    <input type="datetime-local"
                                           name="expires_at[<?= $role['id'] ?>]"
                                           id="expires_at_<?= $role['id'] ?>"
                                           class="form-control form-control-sm expires-input"
                                           value="<?= old('expires_at.' . $role['id'], $formattedExpiry) ?>"
                                           min="<?= date('Y-m-d\TH:i', strtotime('+1 hour')) ?>">
                                    <small class="form-text text-muted">
                                        Optional: Leave empty for permanent role. Must be in the future.
                                    </small>
                                    <?php if ($isExpired): ?>
                                        <small class="text-danger">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Current expiry: <?= date('M j, Y g:i A', strtotime($currentExpiry)) ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="is_active_<?= $role['id'] ?>">
                                        <i class="fas fa-toggle-on"></i> Status
                                    </label>
                                    <div class="form-check">
                                        <input class="form-check-input active-checkbox"
                                               type="checkbox"
                                               name="is_active[<?= $role['id'] ?>]"
                                               value="1"
                                               id="is_active_<?= $role['id'] ?>"
                                               <?= old('is_active.' . $role['id'], $currentIsActive) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active_<?= $role['id'] ?>">
                                            Is Active
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Uncheck to temporarily deactivate this role.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <?php if ($isAssigned): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Assigned: <?= date('M j, Y g:i A', strtotime($assignedRoleData[$role['id']]['assigned_at'])) ?>
                                    <?php if ($assignedRoleData[$role['id']]['assigned_by']): ?>
                                        by User ID: <?= $assignedRoleData[$role['id']]['assigned_by'] ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    No roles available. Please create some roles first.
                </div>
            <?php endif; ?>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-success" <?= empty($allRoles) ? 'disabled' : '' ?>>
                <i class="fas fa-save"></i> Save Role Assignments
            </button>
            <a href="<?= site_url('admin/users') ?>" class="btn btn-secondary ml-2">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle role checkbox changes
    document.querySelectorAll('.role-checkbox').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            toggleRoleDetails(this);
        });
    });

    // Select/Deselect all functionality
    document.getElementById('selectAllRoles').addEventListener('click', function() {
        document.querySelectorAll('.role-checkbox').forEach(function(checkbox) {
            checkbox.checked = true;
            toggleRoleDetails(checkbox);
        });
    });

    document.getElementById('deselectAllRoles').addEventListener('click', function() {
        document.querySelectorAll('.role-checkbox').forEach(function(checkbox) {
            checkbox.checked = false;
            toggleRoleDetails(checkbox);
        });
    });

    // Function to show/hide role details
    function toggleRoleDetails(checkbox) {
        const container = checkbox.closest('.role-container');
        const details = container.querySelector('.role-details');
        const activeCheckbox = container.querySelector('.active-checkbox');

        if (checkbox.checked) {
            details.style.display = '';
            // Default to active for newly selected roles
            if (!activeCheckbox.checked && !container.classList.contains('bg-success-light')) {
                activeCheckbox.checked = true;
            }
        } else {
            details.style.display = 'none';
        }
    }

    // Form validation
    document.getElementById('roleAssignmentForm').addEventListener('submit', function(e) {
        const selectedRoles = document.querySelectorAll('.role-checkbox:checked');

        if (selectedRoles.length === 0) {
            e.preventDefault();
            alert('Please select at least one role for the user.');
            return false;
        }

        // Check if at least one active, non-expired role is selected
        let hasActiveRole = false;
        let debugInfo = [];

        selectedRoles.forEach(function(roleCheckbox) {
            const container = roleCheckbox.closest('.role-container');
            const activeCheckbox = container.querySelector('.active-checkbox');
            const expiresInput = container.querySelector('.expires-input');
            const roleId = roleCheckbox.value;

            const isActive = activeCheckbox.checked;
            const expiresAt = expiresInput.value;
            const isNotExpired = !expiresAt || new Date(expiresAt) > new Date();

            debugInfo.push(`Role ${roleId}: Active=${isActive}, Expires=${expiresAt || 'Never'}, Valid=${isActive && isNotExpired}`);

            if (isActive && isNotExpired) {
                hasActiveRole = true;
            }
        });

        console.log('Role validation debug:', debugInfo);

        if (!hasActiveRole) {
            e.preventDefault();
            alert('User must have at least one active, non-expired role.\n\nDebug info:\n' + debugInfo.join('\n'));
            return false;
        }

        // Validate expiry dates are in the future
        let hasInvalidDate = false;
        selectedRoles.forEach(function(roleCheckbox) {
            const container = roleCheckbox.closest('.role-container');
            const expiresInput = container.querySelector('.expires-input');

            if (expiresInput.value && new Date(expiresInput.value) <= new Date()) {
                hasInvalidDate = true;
            }
        });

        if (hasInvalidDate) {
            e.preventDefault();
            alert('All expiry dates must be in the future.');
            return false;
        }
    });
});
</script>

<style>
.bg-success-light {
    background-color: #d4edda !important;
}
.bg-warning-light {
    background-color: #fff3cd !important;
}
</style>
<?= $this->endSection() ?>