<?php

namespace Modules\MenuManagement\Migrations;

use CodeIgniter\Database\Migration;

class AddMenuPermissions extends Migration
{
    public function up()
    {
        // Insert menu management permissions
        $permissions = [
            [
                'name' => 'menu.manage',
                'description' => 'Manage menu items (create, edit, delete)',
                'category' => 'Menu Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'menu.view',
                'description' => 'View menu management interface',
                'category' => 'Menu Management',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('permissions')->insertBatch($permissions);
    }

    public function down()
    {
        // Remove menu management permissions
        $this->db->table('permissions')
                 ->whereIn('name', ['menu.manage', 'menu.view'])
                 ->delete();
    }
}
