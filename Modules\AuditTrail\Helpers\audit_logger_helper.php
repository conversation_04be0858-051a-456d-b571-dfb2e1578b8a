<?php

/**
 * Audit Logger Helper
 * Provides helper functions for audit trail logging.
 */

if (!function_exists('audit_log')) {
    /**
     * Log an audit trail entry.
     *
     * @param string $event
     * @param string|null $auditableType
     * @param int|null $auditableId
     * @param array|null $oldValues
     * @param array|null $newValues
     * @return bool
     */
    function audit_log(
        string $event,
        ?string $auditableType = null,
        ?int $auditableId = null,
        ?array $oldValues = null,
        ?array $newValues = null
    ): bool {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->log($event, $auditableType, $auditableId, $oldValues, $newValues);
    }
}

if (!function_exists('audit_create')) {
    /**
     * Log a create event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $newValues
     * @return bool
     */
    function audit_create(string $auditableType, int $auditableId, array $newValues): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logCreate($auditableType, $auditableId, $newValues);
    }
}

if (!function_exists('audit_update')) {
    /**
     * Log an update event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $oldValues
     * @param array $newValues
     * @return bool
     */
    function audit_update(string $auditableType, int $auditableId, array $oldValues, array $newValues): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logUpdate($auditableType, $auditableId, $oldValues, $newValues);
    }
}

if (!function_exists('audit_delete')) {
    /**
     * Log a delete event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $oldValues
     * @return bool
     */
    function audit_delete(string $auditableType, int $auditableId, array $oldValues): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logDelete($auditableType, $auditableId, $oldValues);
    }
}

if (!function_exists('audit_login')) {
    /**
     * Log a login event.
     *
     * @param int $userId
     * @return bool
     */
    function audit_login(int $userId): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logLogin($userId);
    }
}

if (!function_exists('audit_logout')) {
    /**
     * Log a logout event.
     *
     * @param int $userId
     * @return bool
     */
    function audit_logout(int $userId): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logLogout($userId);
    }
}

if (!function_exists('audit_view')) {
    /**
     * Log a view event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @return bool
     */
    function audit_view(string $auditableType, int $auditableId): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logView($auditableType, $auditableId);
    }
}

if (!function_exists('audit_bulk_operation')) {
    /**
     * Log a bulk operation.
     *
     * @param string $action
     * @param string $auditableType
     * @param array $auditableIds
     * @param array|null $additionalData
     * @return bool
     */
    function audit_bulk_operation(string $action, string $auditableType, array $auditableIds, ?array $additionalData = null): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logBulkOperation($action, $auditableType, $auditableIds, $additionalData);
    }
}

if (!function_exists('audit_file_operation')) {
    /**
     * Log a file operation.
     *
     * @param string $action
     * @param string $filename
     * @param array|null $metadata
     * @return bool
     */
    function audit_file_operation(string $action, string $filename, ?array $metadata = null): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logFileOperation($action, $filename, $metadata);
    }
}

if (!function_exists('audit_system_event')) {
    /**
     * Log a system event.
     *
     * @param string $event
     * @param array|null $data
     * @return bool
     */
    function audit_system_event(string $event, ?array $data = null): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logSystemEvent($event, $data);
    }
}

if (!function_exists('audit_failed_login')) {
    /**
     * Log a failed login attempt.
     *
     * @param string $username
     * @param string $reason
     * @return bool
     */
    function audit_failed_login(string $username, string $reason = ''): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logFailedLogin($username, $reason);
    }
}

if (!function_exists('audit_permission_change')) {
    /**
     * Log a permission change.
     *
     * @param string $action
     * @param int $userId
     * @param array $permissions
     * @return bool
     */
    function audit_permission_change(string $action, int $userId, array $permissions): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logPermissionChange($action, $userId, $permissions);
    }
}

if (!function_exists('audit_role_change')) {
    /**
     * Log a role change.
     *
     * @param string $action
     * @param int $userId
     * @param array $roles
     * @return bool
     */
    function audit_role_change(string $action, int $userId, array $roles): bool
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->logRoleChange($action, $userId, $roles);
    }
}

if (!function_exists('get_audit_entity_logs')) {
    /**
     * Get audit logs for a specific entity.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param int $limit
     * @return array
     */
    function get_audit_entity_logs(string $auditableType, int $auditableId, int $limit = 50): array
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->getEntityLogs($auditableType, $auditableId, $limit);
    }
}

if (!function_exists('get_audit_user_logs')) {
    /**
     * Get audit logs for a specific user.
     *
     * @param int $userId
     * @param int $limit
     * @return array
     */
    function get_audit_user_logs(int $userId, int $limit = 50): array
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->getUserLogs($userId, $limit);
    }
}

if (!function_exists('get_audit_recent_logs')) {
    /**
     * Get recent audit logs.
     *
     * @param int $limit
     * @return array
     */
    function get_audit_recent_logs(int $limit = 100): array
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->getRecentLogs($limit);
    }
}

if (!function_exists('get_audit_statistics')) {
    /**
     * Get audit statistics.
     *
     * @return array
     */
    function get_audit_statistics(): array
    {
        $auditLogger = new \Modules\AuditTrail\Libraries\AuditLogger();
        return $auditLogger->getStatistics();
    }
}

// Legacy function for backward compatibility
if (!function_exists('log_audit_action')) {
    /**
     * Legacy function for backward compatibility.
     *
     * @deprecated Use audit_log() instead
     */
    function log_audit_action(
        string $action,
        string $entityType,
        ?int $entityId = null,
        ?array $oldValue = null,
        ?array $newValue = null
    ): bool {
        return audit_log($action, $entityType, $entityId, $oldValue, $newValue);
    }
}