<?php

namespace Modules\Authentication\Services;

use Modules\UserManagement\Models\UserModel;

class UserService
{
    protected UserModel $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function getUserById(int $userId): ?array
    {
        return $this->userModel->find($userId);
    }

    public function getUserByUsername(string $username): ?array
    {
        return $this->userModel->where('username', $username)->first();
    }

    public function getUserByEmail(string $email): ?array
    {
        return $this->userModel->where('email', $email)->first();
    }

    public function getCurrentUserFullName(){
        $userId = session()->get('user_id');
        if (!$userId) return null;

        $user = $this->userModel
            ->select('name, first_name, last_name')
            ->where('id', $userId)
            ->first();
        
        if (!$user['first_name']) {
            $user['first_name'] = '';
        }
        if (!$user['last_name']) {
            $user['last_name'] = '';
        }
        return trim($user['first_name'] . ' ' . $user['last_name']);
    }

    public function getCurrentUserName(){
        $userId = session()->get('user_id');
        if (!$userId) return null;

        $user = $this->userModel
            ->select('username')
            ->where('id', $userId)
            ->first();
        return $user['username'] ?? null;
    }

    public function getCurrentUserEmail(){
        $userId = session()->get('user_id');
        if (!$userId) return null;

        $user = $this->userModel
            ->select('email')
            ->where('id', $userId)
            ->first();
        return $user['email'] ?? null;
    }

    public function getCurrentUser(){
        $userId = session()->get('user_id');
        if (!$userId) return null;

        $user = $this->userModel->find($userId);
        return $user;
    }

}

