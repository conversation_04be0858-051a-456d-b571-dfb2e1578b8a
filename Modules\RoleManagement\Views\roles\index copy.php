<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Role Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-4 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?= $statistics['total'] ?></h3>
                <p>Total Roles</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?= $statistics['with_permissions'] ?></h3>
                <p>With Permissions</p>
            </div>
            <div class="icon">
                <i class="fas fa-shield-alt"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?= $statistics['without_permissions'] ?></h3>
                <p>Without Permissions</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Role Management</h3>
        <div class="card-tools">
            <?php if (hasPermission('role.manage')) : ?>
                <a href="<?= route_to('roles.create') ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Role
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <?php if (hasPermission('role.manage')) : ?>
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="btn-group">
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete', 'This will permanently delete all selected roles.')">
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table id="rolesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <?php if (hasPermission('role.manage')) : ?>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <?php endif; ?>
                        <th>Role Name</th>
                        <th>Description</th>
                        <th>Created</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Role Details Modal -->
<div class="modal fade" id="roleModal" tabindex="-1" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roleModalLabel">Role Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="roleModalBody">
                <!-- Role details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables JS -->
<script src="<?= base_url('assets/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable with Hermawan DataTables
    var roleTable = $('#rolesTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        // Export buttons removed as requested
        ajax: {
            url: '<?= site_url('admin/roles/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[2, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('role.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'name' }
            ,{ data: 'description' }
            ,{ data: 'created_date' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // No export buttons to add

    // Select all checkbox functionality
    $('#select-all').on('click', function() {
        var rows = roleTable.rows({ 'search': 'applied' }).nodes();
        $('input[type="checkbox"]', rows).prop('checked', this.checked);
    });

    // Handle individual checkbox clicks
    $('#rolesTable tbody').on('change', 'input[type="checkbox"]', function() {
        if (!this.checked) {
            var el = $('#select-all').get(0);
            if (el && el.checked && ('indeterminate' in el)) {
                el.indeterminate = true;
            }
        }
    });

    // Delete role functionality
    $(document).on('click', '#deleteRole', function() {
        var roleId = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= route_to('roles.ajax_delete') ?>',
                    type: 'POST',
                    data: {
                        id: roleId,
                        [csrfName]: csrfHash
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Deleted!', response.message, 'success');
                            roleTable.ajax.reload();
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                        // Update CSRF token
                        csrfHash = response.csrfHash || csrfHash;
                    },
                    error: function() {
                        Swal.fire('Error!', 'Something went wrong!', 'error');
                    }
                });
            }
        });
    });
});

// View role details
function viewRole(id) {
    $.ajax({
        url: '<?= site_url('admin/roles/show') ?>/' + id,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var role = response.role;
                var permissionsHtml = '';

                if (role.permissions && role.permissions.length > 0) {
                    permissionsHtml = '<ul class="list-unstyled">';
                    role.permissions.forEach(function(permission) {
                        permissionsHtml += '<li><i class="fas fa-check text-success"></i> ' + permission.name + '</li>';
                    });
                    permissionsHtml += '</ul>';
                } else {
                    permissionsHtml = '<p class="text-muted">No permissions assigned</p>';
                }

                var modalBody = `
                    <div class="row">
                        <div class="col-md-6"><strong>Role Name:</strong></div>
                        <div class="col-md-6">${role.name}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Description:</strong></div>
                        <div class="col-md-6">${role.description || 'N/A'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Created:</strong></div>
                        <div class="col-md-6">${new Date(role.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12"><strong>Permissions:</strong></div>
                        <div class="col-12 mt-2">${permissionsHtml}</div>
                    </div>
                `;
                $('#roleModalBody').html(modalBody);
                $('#roleModal').modal('show');
            } else {
                Swal.fire('Error!', response.error, 'error');
            }
        },
        error: function() {
            Swal.fire('Error!', 'Failed to load role details!', 'error');
        }
    });
}

function bulkAction(action, message) {
    var selectedIds = [];
    $('.select-item:checked').each(function() {
        selectedIds.push($(this).val());
    });

    if (selectedIds.length === 0) {
        alert('Please select at least one role.');
        return;
    }

    Swal.fire({
        title: 'Are you sure?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, proceed!'
    }).then((result) => {
        if (result.isConfirmed) {
            var url = '<?= route_to('roles.bulk_delete') ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    ids: selectedIds,
                    [csrfName]: csrfHash
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Success!', response.message, 'success');
                        $('#rolesTable').DataTable().ajax.reload();
                        $('#select-all').prop('checked', false);
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                    // Update CSRF token
                    csrfHash = response.csrfHash || csrfHash;
                },
                error: function() {
                    Swal.fire('Error!', 'Something went wrong!', 'error');
                }
            });
        }
    });
}
// CSRF token variables
var csrfName = '<?= csrf_token() ?>';
var csrfHash = '<?= csrf_hash() ?>';
</script>
<?= $this->endSection() ?>