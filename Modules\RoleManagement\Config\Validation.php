<?php

namespace Modules\RoleManagement\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public array $role = [
        'name'        => 'required|min_length[3]|max_length[100]|is_unique[permissions.name,id,{id}]',
        'description' => 'permit_empty|max_length[255]',
        'category'    => 'required|min_length[1]|max_length[100]', // New rule for category
    ];

    public array $role_errors = [
        'name' => [
            'is_unique' => 'This permission name already exists.'
        ],
        'category' => [
            'required' => 'Permission category is required.'
        ]
    ];

    public array $create_role = [
        'name'        => 'required|min_length[3]|max_length[100]|is_unique[roles.name]',
        'description' => 'permit_empty|max_length[255]',
        'permissions' => 'permit_empty|array',
    ];

    // public array $create_role_errors = [
    //     'name' => [
    //         'is_unique' => 'This role name already exists.'
    //     ]
    // ];

    public array $update_role = [
        'name'        => 'required|min_length[3]|max_length[100]|is_unique[roles.name,id,{id}]',
        'description' => 'permit_empty|max_length[255]',
    ];

    public array $update_role_errors = [
        'name' => [
            'is_unique' => 'This role name already exists.'
        ],
    ];

    public array $create_permission = [
        'name'        => 'required|min_length[3]|max_length[100]|is_unique[permissions.name]',
        'description' => 'permit_empty|max_length[255]',
        'category'    => 'required|min_length[1]|max_length[100]',
    ];

    public array $create_permission_errors = [
        'name' => [
            'required' => 'Permission name is required.',
            'is_unique' => 'This permission name already exists.'
        ],
        // 'name' => [
        //     'is_unique' => 'This permission name already exists.'
        // ],
        'category' => [
            'required' => 'Permission category is required.'
        ]
    ];

    public array $update_permission = [
        'name'        => 'required|min_length[3]|max_length[100]|is_unique[permissions.name,id,{id}]',
        'description' => 'permit_empty|max_length[255]',
        'category'    => 'required|min_length[1]|max_length[100]',
    ];
    public array $update_permission_errors = [
        'name' => [
            'is_unique' => 'This permission name already exists.'
        ],
        'category' => [
            'required' => 'Permission category is required.'
        ]
    ];
    

}
