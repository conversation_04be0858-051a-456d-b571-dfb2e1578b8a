@charset "utf-8";
/* CSS Document */
      
			
			
			                  /*Preloader Demo */

/******************************************************************************/
/******************************************************************************/



#loading {
	background-color: #122737;
	height: 50%;
	width: 30%;
	z-index: 1;
	margin-top: 0px;
	top: 0px;
}
#ts-preloader-absolute {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 100px;
	width: 100px;
	margin-top: -50px;
	margin-left: -50px;
}
.tsperloader {
	position: absolute;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-bottom: 20px solid #006DF0;
}
.tsperloader:nth-child(25) {
 bottom: 0px;
 left: 80px;
 -webkit-animation: animate_25 3s infinite ease-in-out;
 animation: animate_25 3s infinite ease-in-out;
}
.tsperloader:nth-child(24) {
 bottom: 0px;
 left: 60px;
 -webkit-animation: animate_24 3s infinite ease-in-out;
 animation: animate_24 3s infinite ease-in-out;
}
.tsperloader:nth-child(23) {
 bottom: 0px;
 left: 40px;
 -webkit-animation: animate_23 3s infinite ease-in-out;
 animation: animate_23 3s infinite ease-in-out;
}
.tsperloader:nth-child(22) {
 bottom: 0px;
 left: 20px;
 -webkit-animation: animate_22 3s infinite ease-in-out;
 animation: animate_22 3s infinite ease-in-out;
}
.tsperloader:nth-child(21) {
 bottom: 0px;
 left: 0px;
 -webkit-animation: animate_21 3s infinite ease-in-out;
 animation: animate_21 3s infinite ease-in-out;
}
.tsperloader:nth-child(20) {
 bottom: 0px;
 left: 70px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_20 3s infinite ease-in-out;
 animation: animate_20 3s infinite ease-in-out;
}
.tsperloader:nth-child(19) {
 bottom: 0px;
 left: 50px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_19 3s infinite ease-in-out;
 animation: animate_19 3s infinite ease-in-out;
}
.tsperloader:nth-child(18) {
 bottom: 0px;
 left: 30px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_18 3s infinite ease-in-out;
 animation: animate_18 3s infinite ease-in-out;
}
.tsperloader:nth-child(17) {
 bottom: 0px;
 left: 10px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_17 3s infinite ease-in-out;
 animation: animate_17 3s infinite ease-in-out;
}
.tsperloader:nth-child(16) {
 bottom: 20px;
 left: 70px;
 -webkit-animation: animate_16 3s infinite ease-in-out;
 animation: animate_16 3s infinite ease-in-out;
}
.tsperloader:nth-child(15) {
 bottom: 20px;
 left: 50px;
 -webkit-animation: animate_15 3s infinite ease-in-out;
 animation: animate_15 3s infinite ease-in-out;
}
.tsperloader:nth-child(14) {
 bottom: 20px;
 left: 30px;
 -webkit-animation: animate_14 3s infinite ease-in-out;
 animation: animate_14 3s infinite ease-in-out;
}
.tsperloader:nth-child(13) {
 bottom: 20px;
 left: 10px;
 -webkit-animation: animate_13 3s infinite ease-in-out;
 animation: animate_13 3s infinite ease-in-out;
}
.tsperloader:nth-child(12) {
 bottom: 20px;
 left: 60px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_12 3s infinite ease-in-out;
 animation: animate_12 3s infinite ease-in-out;
}
.tsperloader:nth-child(11) {
 bottom: 20px;
 left: 40px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_11 3s infinite ease-in-out;
 animation: animate_11 3s infinite ease-in-out;
}
.tsperloader:nth-child(10) {
 bottom: 20px;
 left: 20px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_10 3s infinite ease-in-out;
 animation: animate_10 3s infinite ease-in-out;
}
.tsperloader:nth-child(9) {
 bottom: 40px;
 left: 60px;
 -webkit-animation: animate_9 3s infinite ease-in-out;
 animation: animate_9 3s infinite ease-in-out;
}
.tsperloader:nth-child(8) {
 bottom: 40px;
 left: 40px;
 -webkit-animation: animate_8 3s infinite ease-in-out;
 animation: animate_8 3s infinite ease-in-out;
}
.tsperloader:nth-child(7) {
 bottom: 40px;
 left: 20px;
 -webkit-animation: animate_7 3s infinite ease-in-out;
 animation: animate_7 3s infinite ease-in-out;
}
.tsperloader:nth-child(6) {
 bottom: 40px;
 left: 50px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_6 3s infinite ease-in-out;
 animation: animate_6 3s infinite ease-in-out;
}
.tsperloader:nth-child(5) {
 bottom: 40px;
 left: 30px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_5 3s infinite ease-in-out;
 animation: animate_5 3s infinite ease-in-out;
}
.tsperloader:nth-child(4) {
 bottom: 60px;
 left: 50px;
 -webkit-animation: animate_4 3s infinite ease-in-out;
 animation: animate_4 3s infinite ease-in-out;
}
.tsperloader:nth-child(3) {
 bottom: 60px;
 left: 30px;
 -webkit-animation: animate_3 3s infinite ease-in-out;
 animation: animate_3 3s infinite ease-in-out;
}
.tsperloader:nth-child(2) {
 bottom: 60px;
 left: 40px;
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
 -webkit-animation: animate_2 3s infinite ease-in-out;
 animation: animate_2 3s infinite ease-in-out;
}
.tsperloader:nth-child(1) {
 bottom: 80px;
 left: 40px;
 -webkit-animation: animate_1 3s infinite ease-in-out;
 animation: animate_1 3s infinite ease-in-out;
}
 @-webkit-keyframes animate_1 {
50% {
 -ms-transform: translate(0, -100px) rotate(180deg);
 -webkit-transform: translate(0, -100px) rotate(180deg);
 transform: translate(0, -100px) rotate(180deg);
}
}
@keyframes animate_1 {
50% {
 -ms-transform: translate(0, -100px) rotate(180deg);
 -webkit-transform: translate(0, -100px) rotate(180deg);
 transform: translate(0, -100px) rotate(180deg);
}
}
 @-webkit-keyframes animate_2 {
50% {
 -ms-transform: translate(0, -80px) rotate(180deg);
 -webkit-transform: translate(0, -80px) rotate(180deg);
 transform: translate(0, -80px) rotate(180deg);
}
}
@keyframes animate_2 {
50% {
 -ms-transform: translate(0, -80px) rotate(180deg);
 -webkit-transform: translate(0, -80px) rotate(180deg);
 transform: translate(0, -80px) rotate(180deg);
}
}
 @-webkit-keyframes animate_3 {
50% {
 -ms-transform: translate(-100px, -100px) rotate(180deg);
 -webkit-transform: translate(-100px, -100px) rotate(180deg);
 transform: translate(-100px, -100px) rotate(180deg);
}
}
@keyframes animate_3 {
50% {
 -ms-transform: translate(-100px, -100px) rotate(180deg);
 -webkit-transform:translate(-100px, -100px) rotate(180deg);
 transform: translate(-100px, -100px) rotate(180deg);
}
}
 @-webkit-keyframes animate_4 {
50% {
 -ms-transform: translate(100px, -100px) rotate(180deg);
 -webkit-transform: translate(100px, -100px) rotate(180deg);
 transform: translate(100px, -100px) rotate(180deg);
}
}
@keyframes animate_4 {
50% {
 -ms-transform: translate(100px, -100px) rotate(180deg);
 -webkit-transform:translate(100px, -100px) rotate(180deg);
 transform: translate(100px, -100px) rotate(180deg);
}
}
 @-webkit-keyframes animate_5 {
50% {
 -ms-transform: translate(-40px, -100px) rotate(180deg);
 -webkit-transform: translate(-40px, -100px) rotate(180deg);
 transform: translate(-40px, -100px) rotate(180deg);
}
}
@keyframes animate_5 {
50% {
 -ms-transform: translate(-40px, -100px) rotate(180deg);
 -webkit-transform: translate(-40px, -100px) rotate(180deg);
 transform: translate(-40px, -100px) rotate(180deg);
}
}
 @-webkit-keyframes animate_6 {
50% {
 -ms-transform: translate(40px, -100px) rotate(180deg);
 -webkit-transform: translate(40px, -100px) rotate(180deg);
 transform: translate(40px, -100px) rotate(180deg);
}
}
@keyframes animate_6 {
50% {
 -ms-transform: translate(40px, -100px) rotate(180deg);
 -webkit-transform: translate(40px, -100px) rotate(180deg);
 transform: translate(40px, -100px) rotate(180deg);
}
}
 @-webkit-keyframes animate_7 {
50% {
 -ms-transform: translate(-80px, -60px) rotate(180deg);
 -webkit-transform: translate(-80px, -60px) rotate(180deg);
 transform: translate(-80px, -60px) rotate(180deg);
}
}
@keyframes animate_7 {
50% {
 -ms-transform: translate(-80px, -60px) rotate(180deg);
 -webkit-transform: translate(-80px, -60px) rotate(180deg);
 transform: translate(-80px, -60px) rotate(180deg);
}
}
 @-webkit-keyframes animate_8 {
50% {
 -ms-transform: translate(0, -60px) rotate(180deg);
 -webkit-transform: translate(0, -60px) rotate(180deg);
 transform: translate(0, -60px) rotate(180deg);
}
}
@keyframes animate_8 {
50% {
 -ms-transform: translate(0, -60px) rotate(180deg);
 -webkit-transform: translate(0, -60px) rotate(180deg);
 transform: translate(0, -60px) rotate(180deg);
}
}
 @-webkit-keyframes animate_9 {
50% {
 -ms-transform: translate(80px, -60px) rotate(180deg);
 -webkit-transform: translate(80px, -60px) rotate(180deg);
 transform: translate(80px, -60px) rotate(180deg);
}
}
@keyframes animate_9 {
50% {
 -ms-transform: translate(80px, -60px) rotate(180deg);
 -webkit-transform: translate(80px, -60px) rotate(180deg);
 transform: translate(80px, -60px) rotate(180deg);
}
}
 @-webkit-keyframes animate_10 {
50% {
 -ms-transform: translate(-100px, -40px) rotate(180deg);
 -webkit-transform: translate(-100px, -40px) rotate(180deg);
 transform: translate(-100px, -40px) rotate(180deg);
}
}
@keyframes animate_10 {
50% {
 -ms-transform: translate(-100px, -40px) rotate(180deg);
 -webkit-transform: translate(-100px, -40px) rotate(180deg);
 transform: translate(-100px, -40px) rotate(180deg);
}
}
 @-webkit-keyframes animate_11 {
50% {
 -ms-transform: translate(0, -40px) rotate(180deg);
 -webkit-transform: translate(0, -40px) rotate(180deg);
 transform: translate(0, -40px) rotate(180deg);
}
}
@keyframes animate_11 {
50% {
 -ms-transform: translate(0, -40px) rotate(180deg);
 -webkit-transform: translate(0, -40px) rotate(180deg);
 transform: translate(0, -40px) rotate(180deg);
}
}
 @-webkit-keyframes animate_12 {
50% {
 -ms-transform: translate(100px, -40px) rotate(180deg);
 -webkit-transform: translate(100px, -40px) rotate(180deg);
 transform: translate(100px, -40px) rotate(180deg);
}
}
@keyframes animate_12 {
50% {
 -ms-transform: translate(100px, -40px) rotate(180deg);
 -webkit-transform: translate(100px, -40px) rotate(180deg);
 transform: translate(100px, -40px) rotate(180deg);
}
}
 @-webkit-keyframes animate_13 {
50% {
 -ms-transform: translate(80px, -80px) rotate(180deg);
 -webkit-transform: translate(80px, -80px) rotate(180deg);
 transform: translate(80px, -80px) rotate(180deg);
}
}
@keyframes animate_13 {
50% {
 -ms-transform: translate(80px, -80px) rotate(180deg);
 -webkit-transform: translate(80px, -80px) rotate(180deg);
 transform: translate(80px, -80px) rotate(180deg);
}
}
 @-webkit-keyframes animate_14 {
50% {
 -ms-transform: translate(80px, -40px) rotate(180deg);
 -webkit-transform: translate(80px, -40px) rotate(180deg);
 transform: translate(80px, -40px) rotate(180deg);
}
}
@keyframes animate_14 {
50% {
 -ms-transform: translate(80px, -40px) rotate(180deg);
 -webkit-transform: translate(80px, -40px) rotate(180deg);
 transform: translate(80px, -40px) rotate(180deg);
}
}
 @-webkit-keyframes animate_15 {
50% {
 -ms-transform: translate(-60px, -80px) rotate(180deg);
 -webkit-transform: translate(-60px, -80px) rotate(180deg);
 transform: translate(-60px, -80px) rotate(180deg);
}
}
@keyframes animate_15 {
50% {
 -ms-transform: translate(-60px, -80px) rotate(180deg);
 -webkit-transform: translate(-60px, -80px) rotate(180deg);
 transform: translate(-60px, -80px) rotate(180deg);
}
}
 @-webkit-keyframes animate_16 {
50% {
 -ms-transform: translate(-100px, -40px) rotate(180deg);
 -webkit-transform: translate(-100px, -40px) rotate(180deg);
 transform: translate(-100px, -40px) rotate(180deg);
}
}
@keyframes animate_16 {
50% {
 -ms-transform: translate(-100px, -40px) rotate(180deg);
 -webkit-transform: translate(-100px, -40px) rotate(180deg);
 transform: translate(-100px, -40px) rotate(180deg);
}
}
 @-webkit-keyframes animate_17 {
50% {
 -ms-transform: translate(-100px, -20px) rotate(180deg);
 -webkit-transform: translate(-100px, -20px) rotate(180deg);
 transform: translate(-100px, -20px) rotate(180deg);
}
}
@keyframes animate_17 {
50% {
 -ms-transform: translate(-100px, -20px) rotate(180deg);
 -webkit-transform: translate(-100px, -20px) rotate(180deg);
 transform: translate(-100px, -20px) rotate(180deg);
}
}
 @-webkit-keyframes animate_18 {
50% {
 -ms-transform: translate(-60px, -20px) rotate(180deg);
 -webkit-transform: translate(-60px, -20px) rotate(180deg);
 transform: translate(-60px, -20px) rotate(180deg);
}
}
@keyframes animate_18 {
50% {
 -ms-transform: translate(-60px, -20px) rotate(180deg);
 -webkit-transform: translate(-60px, -20px) rotate(180deg);
 transform: translate(-60px, -20px) rotate(180deg);
}
}
 @-webkit-keyframes animate_19 {
50% {
 -ms-transform: translate(0, -20px) rotate(180deg);
 -webkit-transform: translate(0, -20px) rotate(180deg);
 transform: translate(0, -20px) rotate(180deg);
}
}
@keyframes animate_19 {
50% {
 -ms-transform: translate(0, -20px) rotate(180deg);
 -webkit-transform: translate(0, -20px) rotate(180deg);
 transform: translate(0, -20px) rotate(180deg);
}
}
 @-webkit-keyframes animate_20 {
50% {
 -ms-transform: translate(60px, -20px) rotate(180deg);
 -webkit-transform: translate(60px, -20px) rotate(180deg);
 transform: translate(60px, -20px) rotate(180deg);
}
}
@keyframes animate_20 {
50% {
 -ms-transform: translate(60px, -20px) rotate(180deg);
 -webkit-transform: translate(60px, -20px) rotate(180deg);
 transform: translate(60px, -20px) rotate(180deg);
}
}
 @-webkit-keyframes animate_21 {
50% {
 -ms-transform: translate(-80px, 30px) rotate(180deg);
 -webkit-transform: translate(-80px, 30px) rotate(180deg);
 transform: translate(-80px, 30px) rotate(180deg);
}
}
@keyframes animate_21 {
50% {
 -ms-transform: translate(-80px, 30px) rotate(180deg);
 -webkit-transform: translate(-80px, 30px) rotate(180deg);
 transform: translate(-80px, 30px) rotate(180deg);
}
}
 @-webkit-keyframes animate_22 {
50% {
 -ms-transform: translate(-40px, 30px) rotate(180deg);
 -webkit-transform: translate(-40px, 30px) rotate(180deg);
 transform: translate(-40px, 30px) rotate(180deg);
}
}
@keyframes animate_22 {
50% {
 -ms-transform: translate(-40px, 30px) rotate(180deg);
 -webkit-transform: translate(-40px, 30px) rotate(180deg);
 transform: translate(-40px, 30px) rotate(180deg);
}
}
 @-webkit-keyframes animate_23 {
50% {
 -ms-transform: translate(0, 30px) rotate(180deg);
 -webkit-transform: translate(0, 30px) rotate(180deg);
 transform: translate(0, 30px) rotate(180deg);
}
}
@keyframes animate_23 {
50% {
 -ms-transform: translate(0, 30px) rotate(180deg);
 -webkit-transform: translate(0, 30px) rotate(180deg);
 transform: translate(0, 30px) rotate(180deg);
}
}
 @-webkit-keyframes animate_24 {
50% {
 -ms-transform: translate(40px, 30px) rotate(180deg);
 -webkit-transform: translate(40px, 30px) rotate(180deg);
 transform: translate(40px, 30px) rotate(180deg);
}
}
@keyframes animate_24 {
50% {
 -ms-transform: translate(40px, 30px) rotate(180deg);
 -webkit-transform: translate(40px, 30px) rotate(180deg);
 transform: translate(40px, 30px) rotate(180deg);
}
}
 @-webkit-keyframes animate_25 {
50% {
 -ms-transform: translate(80px, 30px) rotate(180deg);
 -webkit-transform: translate(80px, 30px) rotate(180deg);
 transform: translate(80px, 30px) rotate(180deg);
}
}
@keyframes animate_25 {
50% {
 -ms-transform: translate(80px, 30px) rotate(180deg);
 -webkit-transform: translate(80px, 30px) rotate(180deg);
 transform: translate(80px, 30px) rotate(180deg);
}
}
                              /*Preloader Demo 1*/

/******************************************************************************/
/******************************************************************************/

#ts-preloader-absolute01 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
	-ms-transform: rotate(-135deg);
	-webkit-transform: rotate(-135deg);
	transform: rotate(-135deg);
}
.tsperloader1 {
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	position: absolute;
	border-top: 5px solid #006DF0;
	border-bottom: 5px solid transparent;
	border-left:  5px solid #FFF;
	border-right: 5px solid transparent;
	-webkit-animation: animate 2s infinite;
	animation: animate 2s infinite;
}
#tsperloader1_one {
	left: 75px;
	top: 75px;
	width: 50px;
	height: 50px;
}
#tsperloader1_two {
	left: 65px;
	top: 65px;
	width: 70px;
	height: 70px;
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
#tsperloader1_three {
	left: 55px;
	top: 55px;
	width: 90px;
	height: 90px;
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
#tsperloader1_four {
	left: 45px;
	top: 45px;
	width: 110px;
	height: 110px;
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
 @-webkit-keyframes animate {
 50% {
 -ms-transform: rotate(360deg) scale(0.8);
 -webkit-transform: rotate(360deg) scale(0.8);
 transform: rotate(360deg) scale(0.8);
}
}
 @keyframes animate {
 50% {
 -ms-transform: rotate(360deg) scale(0.8);
 -webkit-transform: rotate(360deg) scale(0.8);
 transform: rotate(360deg) scale(0.8);
}
}




                              /*Preloader Demo 2*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute02 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.tsperloader2 {
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	position: absolute;
	border-left: 5px solid #006DF0;
	border-right: 5px solid #006DF0;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	-webkit-animation: animate 2s infinite;
	animation: animate 2s infinite;
}
#tsperloader2_one {
	left: 75px;
	top: 75px;
	width: 50px;
	height: 50px;
}
#tsperloader2_two {
	left: 65px;
	top: 65px;
	width: 70px;
	height: 70px;
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}
#tsperloader2_three {
	left: 55px;
	top: 55px;
	width: 90px;
	height: 90px;
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
#tsperloader2_four {
	left: 45px;
	top: 45px;
	width: 110px;
	height: 110px;
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
 @-webkit-keyframes animate {
 50% {
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
}
 100% {
 -ms-transform: rotate(0deg);
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
}
 @keyframes animate {
 50% {
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 transform: rotate(180deg);
}
 100% {
 -ms-transform: rotate(0deg);
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
}




                              /*Preloader Demo 3*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute03 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 50px;
	width: 150px;
	margin-top: -25px;
	margin-left: -75px;
}
.tsperloader3 {
	width: 8px;
	height: 50px;
	margin-right:5px;
	background-color: #006DF0;
	-webkit-animation: animate 1s infinite;
	animation: animate 1s infinite;
	float: left;
}
.tsperloader3:last-child {
	margin-right: 0px;
}
 .tsperloader3:nth-child(10) {
 -webkit-animation-delay: 0.9s;
 animation-delay: 0.9s;
}
.tsperloader3:nth-child(9) {
 -webkit-animation-delay: 0.8s;
 animation-delay: 0.8s;
}
.tsperloader3:nth-child(8) {
 -webkit-animation-delay: 0.7s;
 animation-delay: 0.7s;
}
.tsperloader3:nth-child(7) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader3:nth-child(6) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader3:nth-child(5) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader3:nth-child(4) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader3:nth-child(3) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
.tsperloader3:nth-child(2) {
 -webkit-animation-delay: 0.1s;
 animation-delay: 0.1s;
}
 @-webkit-keyframes animate .tsperloader3 {
 50% {
 -ms-transform: translateX(-25px) scaleY(2);
 -webkit-transform: translateX(-25px) scaleY(2);
 transform: translateX(-25px) scaleY(2);
}
}
 @keyframes animate .tsperloader3 {
 50% {
 -ms-transform: translateX(-25px) scaleY(2);
 -webkit-transform: translateX(-25px) scaleY(2);
 transform: translateX(-25px) scaleY(2);
}
}



                              /*Preloader Demo 4*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute05 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 150px;
	width: 150px;
	margin-top: -75px;
	margin-left: -75px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
}
.tsperloader5 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	position: absolute;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	-webkit-animation: animate 0.8s infinite;
	animation: animate 0.8s infinite;
}
#tsperloader5_one {
	top: 19px;
	left: 19px;
}
#tsperloader5_two {
	top: 0px;
	left: 65px;
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}
#tsperloader5_three {
	top: 19px;
	left: 111px;
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
#tsperloader5_four {
	top: 65px;
	left: 130px;
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
#tsperloader5_five {
	top: 111px;
	left: 111px;
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
#tsperloader5_six {
	top: 130px;
	left: 65px;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
#tsperloader5_seven {
	top: 111px;
	left: 19px;
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
#tsperloader5_eight {
	top: 65px;
	left: 0px;
	-webkit-animation-delay: 0.7s;
	animation-delay: 0.7s;
}
 @-webkit-keyframes animate {
 25% {
 -ms-transform: scale(1.5);
 -webkit-transform: scale(1.5);
 transform: scale(1.5);
}
 75% {
 -ms-transform: scale(0);
 -webkit-transform: scale(0);
 transform: scale(0);
}
}
 @keyframes animate {
 50% {
 -ms-transform: scale(1.5, 1.5);
 -webkit-transform: scale(1.5, 1.5);
 transform: scale(1.5, 1.5);
}
 100% {
 -ms-transform: scale(1, 1);
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
}
}







                              /*Preloader Demo 5*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute06 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 118px;
	width: 118px;
	margin-top: -59px;
	margin-left: -59px;
}
.tsperloader6 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	margin-right: 20px;
	float: left;
	margin-bottom: 20px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
}
.tsperloader6:nth-child(3n+0) {
 margin-right: 0px;
}
#tsperloader6_one {
	-webkit-animation: animate 1s -0.9s ease-in-out infinite;
	animation: animate 1s -0.9s ease-in-out infinite;
}
#tsperloader6_two {
	-webkit-animation: animate 1s -0.8s ease-in-out infinite;
	animation: animate 1s -0.8s ease-in-out infinite;
}
#tsperloader6_three {
	-webkit-animation: animate 1s -0.7s ease-in-out infinite;
	animation: animate 1s -0.7s ease-in-out infinite;
}
#tsperloader6_four {
	-webkit-animation: animate 1s -0.6s ease-in-out infinite;
	animation: animate 1s -0.6s ease-in-out infinite;
}
#tsperloader6_five {
	-webkit-animation: animate 1s -0.5s ease-in-out infinite;
	animation: animate 1s -0.5s ease-in-out infinite;
}
#tsperloader6_six {
	-webkit-animation: animate 1s -0.4s ease-in-out infinite;
	animation: animate 1s -0.4s ease-in-out infinite;
}
#tsperloader6_seven {
	-webkit-animation: animate 1s -0.3s ease-in-out infinite;
	animation: animate 1s -0.3s ease-in-out infinite;
}
#tsperloader6_eight {
	-webkit-animation: animate 1s -0.2s ease-in-out infinite;
	animation: animate 1s -0.2s ease-in-out infinite;
}
#tsperloader6_nine {
	-webkit-animation: animate 1s -0.1s ease-in-out infinite;
	animation: animate 1s -0.1s ease-in-out infinite;
}
 @-webkit-keyframes animate {
 50% {
 -ms-transform: scale(1.5, 1.5);
 -webkit-transform: scale(1.5, 1.5);
 transform: scale(1.5, 1.5);
}
 100% {
 -ms-transform: scale(1, 1);
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
}
}
 @keyframes animate {
 50% {
 -ms-transform: scale(1.5, 1.5);
 -webkit-transform: scale(1.5, 1.5);
 transform: scale(1.5, 1.5);
}
 100% {
 -ms-transform: scale(1, 1);
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
}
}




                              /*Preloader Demo 6*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute07 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 20px;
	width: 100px;
	margin-top: -10px;
	margin-left: -50px;
}
.tsperloader7 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	margin-right: 20px;
	margin-bottom: 20px;
	position: absolute;
}
#tsperloader7_one {
	-webkit-animation: tsperloader7 2s linear infinite;
	animation: tsperloader7 2s linear infinite;
}
#tsperloader7_two {
 -webkit-animation: tsperloader7 2s linear infinite -.4s;
 animation: tsperloader7 2s linear infinite -.4s;
}
#tsperloader7_three {
 -webkit-animation: tsperloader7 2s linear infinite -.8s;
 animation: tsperloader7 2s linear infinite -.8s;
}
#tsperloader7_four {
	-webkit-animation: tsperloader7 2s linear infinite -1.2s;
	animation: tsperloader7 2s linear infinite -1.2s;
}
#tsperloader7_five {
	-webkit-animation: tsperloader7 2s linear infinite -1.6s;
	animation: tsperloader7 2s linear infinite -1.6s;
}
 @-webkit-keyframes tsperloader7 {
 0% {
left: 100px;
top:0
}
 80% {
left: 0;
top:0;
}
 85% {
left: 0;
top: -20px;
width: 20px;
height: 20px;
}
 90% {
width: 40px;
height: 15px;
}
 95% {
left: 100px;
top: -20px;
width: 20px;
height: 20px;
}
 100% {
left: 100px;
top:0;
}
}
@keyframes tsperloader7 {
 0% {
left: 100px;
top:0
}
 80% {
left: 0;
top:0;
}
 85% {
left: 0;
top: -20px;
width: 20px;
height: 20px;
}
 90% {
width: 40px;
height: 15px;
}
 95% {
left: 100px;
top: -20px;
width: 20px;
height: 20px;
}
 100% {
left: 100px;
top:0;
}
}





                              /*Preloader Demo 7*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute08 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 20px;
	width: 140px;
	margin-top: -10px;
	margin-left: -70px;
	-webkit-animation: ts-preloader-absolute 1s infinite;
	animation: ts-preloader-absolute 1s infinite;
}
.tsperloader8 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	float: left;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	margin-right: 20px;
	margin-bottom: 20px;
}
.tsperloader8:last-child {
	margin-right: 0px;
}
#tsperloader8_one {
	-webkit-animation: tsperloader8_one 1s infinite;
	animation: tsperloader8_one 1s infinite;
}
#tsperloader8_two {
	-webkit-animation: tsperloader8_two 1s infinite;
	animation: tsperloader8_two 1s infinite;
}
#tsperloader8_three {
	-webkit-animation: tsperloader8_three 1s infinite;
	animation: tsperloader8_three 1s infinite;
}
#tsperloader8_four {
	-webkit-animation: tsperloader8_four 1s infinite;
	animation: tsperloader8_four 1s infinite;
}
 @-webkit-keyframes ts-preloader-absolute {
100% {
 -ms-transform: rotate(360deg);
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
@keyframes ts-preloader-absolute {
100% {
 -ms-transform: rotate(360deg);
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
 @-webkit-keyframes tsperloader8_one {
50% {
 -ms-transform: translate(20px, 20px);
 -webkit-transform: translate(20px, 20px);
 transform: translate(20px, 20px);
}
}
@keyframes tsperloader8_one {
50% {
 -ms-transform: translate(20px, 20px);
 -webkit-transform: translate(20px, 20px);
 transform: translate(20px, 20px);
}
}
 @-webkit-keyframes tsperloader8_two {
50% {
 -ms-transform: translate(-20px, 20px);
 -webkit-transform: translate(-20px, 20px);
 transform: translate(-20px, 20px);
}
}
@keyframes tsperloader8_two {
50% {
 -ms-transform: translate(-20px, 20px);
 -webkit-transform: translate(-20px, 20px);
 transform: translate(-20px, 20px);
}
}
 @-webkit-keyframes tsperloader8_three {
50% {
 -ms-transform: translate(20px, -20px);
 -webkit-transform: translate(20px, -20px);
 transform: translate(20px, -20px);
}
}
@keyframes tsperloader8_three {
50% {
 -ms-transform: translate(20px, -20px);
 -webkit-transform: translate(20px, -20px);
 transform: translate(20px, -20px);
}
}
 @-webkit-keyframes tsperloader8_four {
50% {
 -ms-transform: translate(-20px, -20px);
 -webkit-transform: translate(-20px, -20px);
 transform: translate(-20px, -20px);
}
}
@keyframes tsperloader8_four {
50% {
 -ms-transform: translate(-20px, -20px);
 -webkit-transform: translate(-20px, -20px);
 transform: translate(-20px, -20px);
}
}







                              /*Preloader Demo 8*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute09 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 60px;
	width: 60px;
	margin-top: -30px;
	margin-left: -30px;
	-webkit-animation: ts-preloader-absolute 1s infinite;
	animation: ts-preloader-absolute 1s infinite;
}
.tsperloader9 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	float: left;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	margin-right: 20px;
	margin-bottom: 20px;
}
.tsperloader9:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader9_one {
	-webkit-animation: tsperloader9_one 1s infinite;
	animation: tsperloader9_one 1s infinite;
}
#tsperloader9_two {
	-webkit-animation: tsperloader9_two 1s infinite;
	animation: tsperloader9_two 1s infinite;
}
#tsperloader9_three {
	-webkit-animation: tsperloader9_three 1s infinite;
	animation: tsperloader9_three 1s infinite;
}
#tsperloader9_four {
	-webkit-animation: tsperloader9_four 1s infinite;
	animation: tsperloader9_four 1s infinite;
}
 @-webkit-keyframes ts-preloader-absolute {
100% {
 -ms-transform: rotate(360deg);
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
@keyframes ts-preloader-absolute {
100% {
 -ms-transform: rotate(360deg);
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
 @-webkit-keyframes tsperloader9_one {
50% {
 -ms-transform: translate(20px, 20px);
 -webkit-transform: translate(20px, 20px);
 transform: translate(20px, 20px);
}
}
@keyframes tsperloader9_one {
50% {
 -ms-transform: translate(20px, 20px);
 -webkit-transform: translate(20px, 20px);
 transform: translate(20px, 20px);
}
}
 @-webkit-keyframes tsperloader9_two {
50% {
 -ms-transform: translate(-20px, 20px);
 -webkit-transform: translate(-20px, 20px);
 transform: translate(-20px, 20px);
}
}
@keyframes tsperloader9_two {
50% {
 -ms-transform: translate(-20px, 20px);
 -webkit-transform: translate(-20px, 20px);
 transform: translate(-20px, 20px);
}
}
 @-webkit-keyframes tsperloader9_three {
50% {
 -ms-transform: translate(20px, -20px);
 -webkit-transform: translate(20px, -20px);
 transform: translate(20px, -20px);
}
}
@keyframes tsperloader9_three {
50% {
 -ms-transform: translate(20px, -20px);
 -webkit-transform: translate(20px, -20px);
 transform: translate(20px, -20px);
}
}
 @-webkit-keyframes tsperloader9_four {
50% {
 -ms-transform: translate(-20px, -20px);
 -webkit-transform: translate(-20px, -20px);
 transform: translate(-20px, -20px);
}
}
@keyframes tsperloader9_four {
50% {
 -ms-transform: translate(-20px, -20px);
 -webkit-transform: translate(-20px, -20px);
 transform: translate(-20px, -20px);
}
}


                              /*Preloader Demo 9*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute-one01 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 300px;
	width: 50px;
	margin-top: -150px;
	margin-left: -25px;
}
#ts-preloader-absolute-two02 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 300px;
	width: 50px;
	margin-top: -150px;
	margin-left: 50px;
}
.tsperloader10-one {
	width: 18px;
	height: 18px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	margin-right: 15px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	-webkit-animation: tsperloader10-one 1s infinite;
	animation: tsperloader10-one 1s infinite;
}
.tsperloader10-two {
	width: 18px;
	height: 18px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	margin-right: 15px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	-webkit-animation: tsperloader10-two 1s infinite;
	animation: tsperloader10-two 1s infinite;
}
 .tsperloader10-one:nth-child(9) {
 -webkit-animation-delay: 0.9s;
 animation-delay: 0.9s;
}
.tsperloader10-one:nth-child(8) {
 -webkit-animation-delay: 0.8s;
 animation-delay: 0.8s;
}
.tsperloader10-one:nth-child(7) {
 -webkit-animation-delay: 0.7s;
 animation-delay: 0.7s;
}
.tsperloader10-one:nth-child(6) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader10-one:nth-child(5) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader10-one:nth-child(4) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader10-one:nth-child(3) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader10-one:nth-child(2) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
 .tsperloader10-two:nth-child(9) {
 -webkit-animation-delay: 0.9s;
 animation-delay: 0.9s;
}
.tsperloader10-two:nth-child(8) {
 -webkit-animation-delay: 0.8s;
 animation-delay: 0.8s;
}
.tsperloader10-two:nth-child(7) {
 -webkit-animation-delay: 0.7s;
 animation-delay: 0.7s;
}
.tsperloader10-two:nth-child(6) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader10-two:nth-child(5) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader10-two:nth-child(4) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader10-two:nth-child(3) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader10-two:nth-child(2) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
 @-webkit-keyframes tsperloader10-one {
50% {
 -ms-transform: translate(100px, 0);
 -webkit-transform: translate(100px, 0);
 transform: translate(100px, 0);
}
}
@keyframes tsperloader10-one {
50% {
 -ms-transform: translate(100px, 0);
 -webkit-transform: translate(100px, 0);
 transform: translate(100px, 0);
}
}
 @-webkit-keyframes tsperloader10-two {
50% {
 -ms-transform: translate(-100px, 0);
 -webkit-transform: translate(-100px, 0);
 transform: translate(-100px, 0);
}
}
@keyframes tsperloader10-two {
50% {
 -ms-transform: translate(-100px, 0);
 -webkit-transform: translate(-100px, 0);
 transform: translate(-100px, 0);
}
}



                              /*Preloader Demo 10*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute11 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 50px;
	width: 300px;
	margin-top: -25px;
	margin-left: -150px;
}
.tsperloader11 {
	width: 18px;
	height: 18px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	margin-right: 15px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	-webkit-animation: tsperloader11 1s infinite;
	animation: tsperloader11 1s infinite;
}
.tsperloader11:last-child {
	margin-right: 0px;
}
 .tsperloader11:nth-child(9) {
 -webkit-animation-delay: 0.9s;
 animation-delay: 0.9s;
}
.tsperloader11:nth-child(8) {
 -webkit-animation-delay: 0.8s;
 animation-delay: 0.8s;
}
.tsperloader11:nth-child(7) {
 -webkit-animation-delay: 0.7s;
 animation-delay: 0.7s;
}
.tsperloader11:nth-child(6) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader11:nth-child(5) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader11:nth-child(4) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader11:nth-child(3) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader11:nth-child(2) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
 @-webkit-keyframes tsperloader11 {
50% {
 -ms-transform: translate(0, -50px);
 -webkit-transform: translate(0, -50px);
 transform: translate(0, -50px);
}
}
@keyframes tsperloader11 {
50% {
 -ms-transform: translate(0, -50px);
 -webkit-transform: translate(0, -50px);
 transform: translate(0, -50px);
}
}



                              /*Preloader Demo 11*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute12 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 50px;
	width: 200px;
	margin-top: -25px;
	margin-left: -100px;
}
.tsperloader12 {
	width: 20px;
	height:20px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
}
#first_tsperloader12 {
	-webkit-animation: first_tsperloader12 2s infinite;
	animation: first_tsperloader12 2s infinite;
}
#second_tsperloader12 {
	-webkit-animation: second_tsperloader12 2s infinite;
	animation: second_tsperloader12 2s infinite;
}
 @-webkit-keyframes first_tsperloader12 {
 25% {
 -ms-transform: translate(90px, 0) scale(2);
 -webkit-transform: translate(90px, 0) scale(2);
 transform: translate(90px, 0) scale(2);
}
 50% {
 -ms-transform: translate(180px, 0) scale(1);
 -webkit-transform: translate(180px, 0) scale(1);
 transform: translate(180px, 0) scale(1);
}
 75% {
 -ms-transform: translate(90px, 0) scale(2);
 -webkit-transform: translate(90px, 0) scale(2);
 transform: translate(90px, 0) scale(2);
}
}
@keyframes first_tsperloader12 {
 25% {
 -ms-transform: translate(90px, 0) scale(2);
 -webkit-transform: translate(90px, 0) scale(2);
 transform: translate(90px, 0) scale(2);
}
 50% {
 -ms-transform: translate(180px, 0) scale(1);
 -webkit-transform: translate(180px, 0) scale(1);
 transform: translate(180px, 0) scale(1);
}
 75% {
 -ms-transform: translate(90px, 0) scale(2);
 -webkit-transform: translate(90px, 0) scale(2);
 transform: translate(90px, 0) scale(2);
}
}
 @-webkit-keyframes second_tsperloader12 {
 25% {
 -ms-transform: translate(-90px, 0) scale(2);
 -webkit-transform: translate(-90px, 0) scale(2);
 transform: translate(-90px, 0) scale(2);
}
 50% {
 -ms-transform: translate(-180px, 0) scale(1);
 -webkit-transform: translate(-180px, 0) scale(1);
 transform: translate(-180px, 0) scale(1);
}
 75% {
 -ms-transform: translate(-90px, 0) scale(2);
 -webkit-transform: translate(-90px, 0) scale(2);
 transform: translate(-90px, 0) scale(2);
}
}
@keyframes second_tsperloader12 {
 25% {
 -ms-transform: translate(-90px, 0) scale(2);
 -webkit-transform: translate(-90px, 0) scale(2);
 transform: translate(-90px, 0) scale(2);
}
 50% {
 -ms-transform: translate(-180px, 0) scale(1);
 -webkit-transform: translate(-180px, 0) scale(1);
 transform: translate(-180px, 0) scale(1);
}
 75% {
 -ms-transform: translate(-90px, 0) scale(2);
 -webkit-transform: translate(-90px, 0) scale(2);
 transform: translate(-90px, 0) scale(2);
}
}




                              /*Preloader Demo 12*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute13 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 150px;
	width: 150px;
	margin-top: -75px;
	margin-left: -75px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}
.tsperloader13 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	position: absolute;
	left: 65px;
	top: 65px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
}
.tsperloader13:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader13_one {
	-webkit-animation: tsperloader13_one 2s infinite;
	animation: tsperloader13_one 2s infinite;
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
#tsperloader13_two {
	-webkit-animation: tsperloader13_two 2s infinite;
	animation: tsperloader13_two 2s infinite;
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
#tsperloader13_three {
	-webkit-animation: tsperloader13_three 2s infinite;
	animation: tsperloader13_three 2s infinite;
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
#tsperloader13_four {
	-webkit-animation: tsperloader13_four 2s infinite;
	animation: tsperloader13_four 2s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
#tsperloader13_five {
	-webkit-animation: tsperloader13_five 2s infinite;
	animation: tsperloader13_five 2s infinite;
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
#tsperloader13_six {
	-webkit-animation: tsperloader13_six 2s infinite;
	animation: tsperloader13_six 2s infinite;
	-webkit-animation-delay: 0.7s;
	animation-delay: 0.7s;
}
#tsperloader13_seven {
	-webkit-animation: tsperloader13_seven 2s infinite;
	animation: tsperloader13_seven 2s infinite;
	-webkit-animation-delay: 0.8s;
	animation-delay: 0.8s;
}
#tsperloader13_eight {
	-webkit-animation: tsperloader13_eight 2s infinite;
	animation: tsperloader13_eight 2s infinite;
	-webkit-animation-delay: 0.9s;
	animation-delay: 0.9s;
}
#tsperloader13_big {
	position: absolute;
	width: 50px;
	height: 50px;
	left: 50px;
	top: 50px;
	-webkit-animation: tsperloader13_big 2s infinite;
	animation: tsperloader13_big 2s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
 @-webkit-keyframes tsperloader13_big {
 50% {
-webkit-transform: scale(0.5);
}
}
 @keyframes tsperloader13_big {
 50% {
 transform: scale(0.5);
 -webkit-transform: scale(0.5);
}
}
 @-webkit-keyframes tsperloader13_one {
 50% {
-webkit-transform: translate(-65px, -65px);
}
}
 @keyframes tsperloader13_one {
 50% {
 transform: translate(-65px, -65px);
 -webkit-transform: translate(-65px, -65px);
}
}
 @-webkit-keyframes tsperloader13_two {
 50% {
-webkit-transform: translate(0, -65px);
}
}
 @keyframes tsperloader13_two {
 50% {
 transform: translate(0, -65px);
 -webkit-transform: translate(0, -65px);
}
}
 @-webkit-keyframes tsperloader13_three {
 50% {
-webkit-transform: translate(65px, -65px);
}
}
 @keyframes tsperloader13_three {
 50% {
 transform: translate(65px, -65px);
 -webkit-transform: translate(65px, -65px);
}
}
 @-webkit-keyframes tsperloader13_four {
 50% {
-webkit-transform: translate(65px, 0);
}
}
 @keyframes tsperloader13_four {
 50% {
 transform: translate(65px, 0);
 -webkit-transform: translate(65px, 0);
}
}
 @-webkit-keyframes tsperloader13_five {
 50% {
-webkit-transform: translate(65px, 65px);
}
}
 @keyframes tsperloader13_five {
 50% {
 transform: translate(65px, 65px);
 -webkit-transform: translate(65px, 65px);
}
}
 @-webkit-keyframes tsperloader13_six {
 50% {
-webkit-transform: translate(0, 65px);
}
}
 @keyframes tsperloader13_six {
 50% {
 transform:  translate(0, 65px);
 -webkit-transform:  translate(0, 65px);
}
}
 @-webkit-keyframes tsperloader13_seven {
 50% {
-webkit-transform: translate(-65px, 65px);
}
}
 @keyframes tsperloader13_seven {
 50% {
 transform: translate(-65px, 65px);
 -webkit-transform: translate(-65px, 65px);
}
}
 @-webkit-keyframes tsperloader13_eight {
 50% {
-webkit-transform: translate(-65px, 0);
}
}
 @keyframes tsperloader13_eight {
 50% {
 transform: translate(-65px, 0);
 -webkit-transform: translate(-65px, 0);
}
}




                              /*Preloader Demo 13*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute14 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 150px;
	width: 150px;
	margin-top: -75px;
	margin-left: -75px;
}
.tsperloader14 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	float: left;
	margin-right: 20px;
	margin-top: 65px;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
}
#tsperloader14_one {
	-webkit-animation: tsperloader14_one 1.5s infinite;
	animation: tsperloader14_one 1.5s infinite;
}
#tsperloader14_two {
	-webkit-animation: tsperloader14_two 1.5s infinite;
	animation: tsperloader14_two 1.5s infinite;
	-webkit-animation-delay: 0.25s;
	animation-delay: 0.25s;
}
#tsperloader14_three {
	-webkit-animation: tsperloader14_three 1.5s infinite;
	animation: tsperloader14_three 1.5s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
 @-webkit-keyframes tsperloader14_one {
75% {
-webkit-transform: scale(0);
}
}
 @keyframes tsperloader14_one {
 75% {
 transform: scale(0);
 -webkit-transform: scale(0);
}
}
 @-webkit-keyframes tsperloader14_two {
 75% {
-webkit-transform: scale(0);
}
}
 @keyframes tsperloader14_two {
 75% {
 transform: scale(0);
 -webkit-transform:  scale(0);
}
}
 @-webkit-keyframes tsperloader14_three {
 75% {
-webkit-transform: scale(0);
}
}
 @keyframes tsperloader14_three {
 75% {
 transform: scale(0);
 -webkit-transform: scale(0);
}
}




                              /*Preloader Demo 14*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute-one15 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 300px;
	width: 50px;
	margin-top: -150px;
	margin-left: -25px;
}
#ts-preloader-absolute-two15 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 300px;
	width: 50px;
	margin-top: -150px;
	margin-left: 50px;
}
.tsperloader15-one {
	width: 18px;
	height: 18px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	margin-right: 15px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-animation: tsperloader15-one 1s infinite;
	animation: tsperloader15-one 1s infinite;
}
.tsperloader15-two {
	width: 18px;
	height: 18px;
	background-color: #006DF0;
	float: left;
	margin-top: 15px;
	margin-right: 15px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-animation: tsperloader15-two 1s infinite;
	animation: tsperloader15-two 1s infinite;
}
 .tsperloader15-one:nth-child(6) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader15-one:nth-child(5) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader15-one:nth-child(4) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader15-one:nth-child(3) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader15-one:nth-child(2) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
 .tsperloader15-two:nth-child(9) {
 -webkit-animation-delay: 0.9s;
 animation-delay: 0.9s;
}
.tsperloader15-two:nth-child(8) {
 -webkit-animation-delay: 0.8s;
 animation-delay: 0.8s;
}
.tsperloader15-two:nth-child(7) {
 -webkit-animation-delay: 0.7s;
 animation-delay: 0.7s;
}
.tsperloader15-two:nth-child(6) {
 -webkit-animation-delay: 0.6s;
 animation-delay: 0.6s;
}
.tsperloader15-two:nth-child(5) {
 -webkit-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader15-two:nth-child(4) {
 -webkit-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
.tsperloader15-two:nth-child(3) {
 -webkit-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
.tsperloader15-two:nth-child(2) {
 -webkit-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
 @-webkit-keyframes tsperloader15-one {
50% {
 -ms-transform: translate(100px, 0);
 -webkit-transform: translate(100px, 0);
 transform: translate(100px, 0);
}
}
@keyframes tsperloader15-one {
50% {
 -ms-transform: translate(100px, 0);
 -webkit-transform: translate(100px, 0);
 transform: translate(100px, 0);
}
}
 @-webkit-keyframes tsperloader15-two {
50% {
 -ms-transform: translate(-100px, 0);
 -webkit-transform: translate(-100px, 0);
 transform: translate(-100px, 0);
}
}
@keyframes tsperloader15-two {
50% {
 -ms-transform: translate(-100px, 0);
 -webkit-transform: translate(-100px, 0);
 transform: translate(-100px, 0);
}
}




                              /*Preloader Demo 15*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute16 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 150px;
	width: 150px;
	margin-top: -75px;
	margin-left: -75px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}
.tsperloader16 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	margin-right: 110px;
	float: left;
	margin-bottom: 110px;
}
.tsperloader16:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader16_one {
	-webkit-animation: tsperloader16_one 2s infinite;
	animation: tsperloader16_one 2s infinite;
}
#tsperloader16_two {
	-webkit-animation: tsperloader16_two 2s infinite;
	animation: tsperloader16_two 2s infinite;
}
#tsperloader16_three {
	-webkit-animation: tsperloader16_three 2s infinite;
	animation: tsperloader16_three 2s infinite;
}
#tsperloader16_four {
	-webkit-animation: tsperloader16_four 2s infinite;
	animation: tsperloader16_four 2s infinite;
}
#tsperloader16_big {
	-webkit-animation: tsperloader16_big 0.5s infinite;
	animation: tsperloader16_big 0.5s infinite;
	position: absolute;
	width: 50px;
	height: 50px;
	left: 50px;
	top: 50px;
}
 @-webkit-keyframes tsperloader16_big {
 25% {
-webkit-transform:  scale(0.5);
}
}
 @keyframes tsperloader16_big {
 25% {
 transform:  scale(0.5);
 -webkit-transform:   scale(0.5);
}
}
 @-webkit-keyframes tsperloader16_one {
 25% {
-webkit-transform: translate(130px, 0) rotate(-90deg);
}
 50% {
-webkit-transform: translate(130px, 130px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(0, 130px) rotate(-270deg);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader16_one {
 25% {
 transform: translate(130px, 0) rotate(-90deg);
 -webkit-transform: translate(130px, 0) rotate(-90deg);
}
 50% {
 transform: translate(130px, 130px) rotate(-180deg);
 -webkit-transform: translate(130px, 130px) rotate(-180deg);
}
 75% {
 transform: translate(0, 130px) rotate(-270deg);
 -webkit-transform: translate(0, 130px) rotate(-270deg);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader16_two {
 25% {
-webkit-transform: translate(0, 130px) rotate(-90deg);
}
 50% {
-webkit-transform: translate(-130px, 130px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(-130px, 0) rotate(-270deg);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader16_two {
 25% {
 transform: translate(0, 130px) rotate(-90deg);
 -webkit-transform: translate(0, 130px) rotate(-90deg);
}
 50% {
 transform: translate(-130px, 130px) rotate(-180deg);
 -webkit-transform: translate(-130px, 130px) rotate(-180deg);
}
 75% {
 transform: translate(-130px, 0) rotate(-270deg);
 -webkit-transform: translate(-130px, 0) rotate(-270deg);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader16_three {
 25% {
-webkit-transform: translate(0, -130px) rotate(-90deg);
}
 50% {
-webkit-transform: translate(130px, -130px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(130px, 0) rotate(-270deg);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader16_three {
 25% {
 transform: translate(0, -130px) rotate(-90deg);
 -webkit-transform: translate(0, -130px) rotate(-90deg);
}
 50% {
 transform: translate(130px, -130px) rotate(-180deg);
 -webkit-transform: translate(130px, -130px) rotate(-180deg);
}
 75% {
 transform:  translate(130px, 0) rotate(-270deg);
 -webkit-transform: translate(130px, 0) rotate(-270deg);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader16_four {
 25% {
-webkit-transform: translate(-130px, 0) rotate(-90deg);
}
 50% {
-webkit-transform: translate(-130px, -130px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(0, -130px) rotate(-270deg);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader16_four {
 25% {
 transform: translate(-130px, 0) rotate(-90deg);
 -webkit-transform: translate(-130px, 0) rotate(-90deg);
}
 50% {
 transform: translate(-130px, -130px) rotate(-180deg);
 -webkit-transform: translate(-130px, -130px) rotate(-180deg);
}
 75% {
 transform: translate(0, -130px) rotate(-270deg);
 -webkit-transform: translate(0, -130px) rotate(-270deg);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}



                              /*Preloader Demo 16*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute17 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 50px;
	width: 50px;
	margin-top: -25px;
	margin-left: -25px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-animation: ts-preloader-absolute 1.5s infinite;
	animation: ts-preloader-absolute 1.5s infinite;
}
.tsperloader17 {
	width: 25px;
	height: 25px;
	background-color: #006DF0;
	float: left;
}
#tsperloader17_one {
	-webkit-animation: tsperloader17_one 1.5s infinite;
	animation: tsperloader17_one 1.5s infinite;
}
#tsperloader17_two {
	-webkit-animation: tsperloader17_two 1.5s infinite;
	animation: tsperloader17_two 1.5s infinite;
}
#tsperloader17_three {
	-webkit-animation: tsperloader17_three 1.5s infinite;
	animation: tsperloader17_three 1.5s infinite;
}
#tsperloader17_four {
	-webkit-animation: tsperloader17_four 1.5s infinite;
	animation: tsperloader17_four 1.5s infinite;
}
 @-webkit-keyframes ts-preloader-absolute {
 100% {
-webkit-transform: rotate(-45deg);
}
}
 @keyframes ts-preloader-absolute {
 100% {
 transform:  rotate(-45deg);
 -webkit-transform:  rotate(-45deg);
}
}
 @-webkit-keyframes tsperloader17_one {
 25% {
-webkit-transform: translate(0, -50px) rotate(-180deg);
}
 100% {
-webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @keyframes tsperloader17_one {
 25% {
 transform: translate(0, -50px) rotate(-180deg);
 -webkit-transform: translate(0, -50px) rotate(-180deg);
}
 100% {
 transform: translate(0, 0) rotate(-180deg);
 -webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @-webkit-keyframes tsperloader17_two {
 25% {
-webkit-transform: translate(50px, 0) rotate(-180deg);
}
 100% {
-webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @keyframes tsperloader17_two {
 25% {
 transform: translate(50px, 0) rotate(-180deg);
 -webkit-transform: translate(50px, 0) rotate(-180deg);
}
 100% {
 transform: translate(0, 0) rotate(-180deg);
 -webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @-webkit-keyframes tsperloader17_three {
 25% {
-webkit-transform: translate(-50px, 0) rotate(-180deg);
}
 100% {
-webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @keyframes tsperloader17_three {
 25% {
 transform:  translate(-50px, 0) rotate(-180deg);
 -webkit-transform:  translate(-50px, 0) rotate(-180deg);
}
 100% {
 transform: translate(0, 0) rotate(-180deg);
 -webkit-transform: rtranslate(0, 0) rotate(-180deg);
}
}
 @-webkit-keyframes tsperloader17_four {
 25% {
-webkit-transform: translate(0, 50px) rotate(-180deg);
}
 100% {
-webkit-transform: translate(0, 0) rotate(-180deg);
}
}
 @keyframes tsperloader17_four {
 25% {
 transform: translate(0, 50px) rotate(-180deg);
 -webkit-transform: translate(0, 50px) rotate(-180deg);
}
 100% {
 transform: translate(0, 0) rotate(-180deg);
 -webkit-transform: translate(0, 0) rotate(-180deg);
}
}




                              /*Preloader Demo 17*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute18 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 100px;
	width: 100px;
	margin-top: -50px;
	margin-left: -50px;
}
.tsperloader18 {
	width: 25px;
	height: 25px;
	background-color: #006DF0;
	margin-right: 50px;
	float: left;
	margin-bottom: 50px;
}
.tsperloader18:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader18_one {
	-webkit-animation: tsperloader18_one 2s infinite;
	animation: tsperloader18_one 2s infinite;
}
#tsperloader18_two {
	-webkit-animation: tsperloader18_two 2s infinite;
	animation: tsperloader18_two 2s infinite;
}
#tsperloader18_three {
	-webkit-animation: tsperloader18_three 2s infinite;
	animation: tsperloader18_three 2s infinite;
}
#tsperloader18_four {
	-webkit-animation: tsperloader18_four 2s infinite;
	animation: tsperloader18_four 2s infinite;
}
 @-webkit-keyframes tsperloader18_one {
 25% {
-webkit-transform: translate(75px, 0) rotate(-90deg) scale(0.5);
}
 50% {
-webkit-transform: translate(75px, 75px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(0, 75px) rotate(-270deg) scale(0.5);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader18_one {
 25% {
 transform: translate(75px, 0) rotate(-90deg) scale(0.5);
 -webkit-transform: translate(75px, 0) rotate(-90deg) scale(0.5);
}
 50% {
 transform: translate(75px, 75px) rotate(-180deg);
 -webkit-transform: translate(75px, 75px) rotate(-180deg);
}
 75% {
 transform: translate(0, 75px) rotate(-270deg) scale(0.5);
 -webkit-transform: translate(0, 75px) rotate(-270deg) scale(0.5);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader18_two {
 25% {
-webkit-transform: translate(0, 75px) rotate(-90deg) scale(0.5);
}
 50% {
-webkit-transform: translate(-75px, 75px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(-75px, 0) rotate(-270deg) scale(0.5);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader18_two {
 25% {
 transform: translate(0, 75px) rotate(-90deg) scale(0.5);
 -webkit-transform: translate(0, 75px) rotate(-90deg) scale(0.5);
}
 50% {
 transform: translate(-75px, 75px) rotate(-180deg);
 -webkit-transform: translate(-75px, 75px) rotate(-180deg);
}
 75% {
 transform: translate(-75px, 0) rotate(-270deg) scale(0.5);
 -webkit-transform: translate(-75px, 0) rotate(-270deg) scale(0.5);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader18_three {
 25% {
-webkit-transform: translate(0, -75px) rotate(-90deg) scale(0.5);
}
 50% {
-webkit-transform: translate(75px, -75px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(75px, 0) rotate(-270deg) scale(0.5);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader18_three {
 25% {
 transform: translate(0, -75px) rotate(-90deg) scale(0.5);
 -webkit-transform: translate(0, -75px) rotate(-90deg) scale(0.5);
}
 50% {
 transform: translate(75px, -75px) rotate(-180deg);
 -webkit-transform: translate(75px, -75px) rotate(-180deg);
}
 75% {
 transform:  translate(75px, 0) rotate(-270deg) scale(0.5);
 -webkit-transform: translate(75px, 0) rotate(-270deg) scale(0.5);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}
 @-webkit-keyframes tsperloader18_four {
 25% {
-webkit-transform: translate(-75px, 0) rotate(-90deg) scale(0.5);
}
 50% {
-webkit-transform: translate(-75px, -75px) rotate(-180deg);
}
 75% {
-webkit-transform:  translate(0, -75px) rotate(-270deg) scale(0.5);
}
 100% {
-webkit-transform: rotate(-360deg);
}
}
 @keyframes tsperloader18_four {
 25% {
 transform: translate(-75px, 0) rotate(-90deg) scale(0.5);
 -webkit-transform: translate(-75px, 0) rotate(-90deg) scale(0.5);
}
 50% {
 transform: translate(-75px, -75px) rotate(-180deg);
 -webkit-transform: translate(-75px, -75px) rotate(-180deg);
}
 75% {
 transform: translate(0, -75px) rotate(-270deg) scale(0.5);
 -webkit-transform: translate(0, -75px) rotate(-270deg) scale(0.5);
}
 100% {
 transform: rotate(-360deg);
 -webkit-transform: rotate(-360deg);
}
}



                              /*Preloader Demo 18*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute19 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 118px;
	width: 72px;
	margin-top: -59px;
	margin-left: -36px;
}
.tsperloader19 {
	width: 26px;
	height: 26px;
	background-color:#006DF0;
	margin-right: 20px;
	float: left;
	margin-bottom: 20px;
}
.tsperloader19:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader19_one {
	-webkit-animation: tsperloader19_one 1s infinite;
	animation: tsperloader19_one 1s infinite;
}
#tsperloader19_two {
	-webkit-animation: tsperloader19_two 1s infinite;
	animation: tsperloader19_two 1s infinite;
}
#tsperloader19_three {
	-webkit-animation: tsperloader19_three 1s infinite;
	animation: tsperloader19_three 1s infinite;
}
#tsperloader19_four {
	-webkit-animation: tsperloader19_four 1s infinite;
	animation: tsperloader19_four 1s infinite;
}
#tsperloader19_five {
	-webkit-animation: tsperloader19_five 1s infinite;
	animation: tsperloader19_five 1s infinite;
}
#tsperloader19_six {
	-webkit-animation: tsperloader19_six 1s infinite;
	animation: tsperloader19_six 1s infinite;
}
 @-webkit-keyframes tsperloader19_one {
 50% {
 -ms-transform: translate(-100px, 46px) rotate(-179deg);
 -webkit-transform: translate(-100px, 46px) rotate(-179deg);
 transform: translate(-100px, 46px) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_one {
 50% {
 -ms-transform: translate(-100px, 46px) rotate(-179deg);
 -webkit-transform: translate(-100px, 46px) rotate(-179deg);
 transform: translate(-100px, 46px) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @-webkit-keyframes tsperloader19_two {
 50% {
 -ms-transform: translate(100px, 46px) rotate(179deg);
 -webkit-transform: translate(100px, 46px) rotate(179deg);
 transform: translate(100px, 46px) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_two {
 50% {
 -ms-transform: translate(100px, 46px) rotate(179deg);
 -webkit-transform: translate(100px, 46px) rotate(179deg);
 transform: translate(100px, 46px) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @-webkit-keyframes tsperloader19_three {
 50% {
 -ms-transform: translate(-100px, 0) rotate(-179deg);
 -webkit-transform: translate(-100px, 0) rotate(-179deg);
 transform: translate(-100px, 0) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_three {
 50% {
 -ms-transform: translate(-100px, 0) rotate(-179deg);
 -webkit-transform: translate(-100px, 0) rotate(-179deg);
 transform: translate(-100px, 0) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @-webkit-keyframes tsperloader19_four {
 50% {
 -ms-transform: translate(100px, 0) rotate(179deg);
 -webkit-transform: translate(100px, 0) rotate(179deg);
 transform: translate(100px, 0) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_four {
 50% {
 -ms-transform: translate(100px, 0) rotate(179deg);
 -webkit-transform: translate(100px, 0) rotate(179deg);
 transform: translate(100px, 0) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @-webkit-keyframes tsperloader19_five {
 50% {
 -ms-transform: translate(-100px, -46px) rotate(-179deg);
 -webkit-transform: translate(-100px, -46px) rotate(-179deg);
 transform: translate(-100px, -46px) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_five {
 50% {
 -ms-transform: translate(-100px, -46px) rotate(-179deg);
 -webkit-transform: translate(-100px, -46px) rotate(-179deg);
 transform: translate(-100px, -46px) rotate(-179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @-webkit-keyframes tsperloader19_six {
 50% {
 -ms-transform: translate(100px, -46px) rotate(179deg);
 -webkit-transform: translate(100px, -46px) rotate(179deg);
 transform: translate(100px, -46px) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}
 @keyframes tsperloader19_six {
 50% {
 -ms-transform: translate(100px, -46px) rotate(179deg);
 -webkit-transform: translate(100px, -46px) rotate(179deg);
 transform: translate(100px, -46px) rotate(179deg);
}
 100% {
 -ms-transform: translate(0, 0);
 -webkit-transform: translate(0, 0);
 transform: translate(0, 0);
}
}





                              /*Preloader Demo 19*/

/******************************************************************************/
/******************************************************************************/







#ts-preloader-absolute20 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 118px;
	width: 118px;
	margin-top: -59px;
	margin-left: -59px;
}
.tsperloader20 {
	width: 20px;
	height: 20px;
	background-color: #006DF0;
	margin-right: 20px;
	float: left;
	margin-bottom: 20px;
}
.tsperloader20:nth-child(3n+0) {
 margin-right: 0px;
}
#tsperloader20_one {
	-webkit-animation: animate 1s -0.9s ease-in-out infinite;
	animation: animate 1s -0.9s ease-in-out infinite;
}
#tsperloader20_two {
	-webkit-animation: animate 1s -0.8s ease-in-out infinite;
	animation: animate 1s -0.8s ease-in-out infinite;
}
#tsperloader20_three {
	-webkit-animation: animate 1s -0.7s ease-in-out infinite;
	animation: animate 1s -0.7s ease-in-out infinite;
}
#tsperloader20_four {
	-webkit-animation: animate 1s -0.6s ease-in-out infinite;
	animation: animate 1s -0.6s ease-in-out infinite;
}
#tsperloader20_five {
	-webkit-animation: animate 1s -0.5s ease-in-out infinite;
	animation: animate 1s -0.5s ease-in-out infinite;
}
#tsperloader20_six {
	-webkit-animation: animate 1s -0.4s ease-in-out infinite;
	animation: animate 1s -0.4s ease-in-out infinite;
}
#tsperloader20_seven {
	-webkit-animation: animate 1s -0.3s ease-in-out infinite;
	animation: animate 1s -0.3s ease-in-out infinite;
}
#tsperloader20_eight {
	-webkit-animation: animate 1s -0.2s ease-in-out infinite;
	animation: animate 1s -0.2s ease-in-out infinite;
}
#tsperloader20_nine {
	-webkit-animation: animate 1s -0.1s ease-in-out infinite;
	animation: animate 1s -0.1s ease-in-out infinite;
}
 @-webkit-keyframes animate {
 50% {
 -ms-transform: scale(1.5, 1.5);
 -webkit-transform: scale(1.5, 1.5);
 transform: scale(1.5, 1.5);
}
 100% {
 -ms-transform: scale(1, 1);
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
}
}
 @keyframes animate {
 50% {
 -ms-transform: scale(1.5, 1.5);
 -webkit-transform: scale(1.5, 1.5);
 transform: scale(1.5, 1.5);
}
 100% {
 -ms-transform: scale(1, 1);
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
}
}




                              /*Preloader Demo 20*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute21 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 100px;
	width: 100px;
	margin-top: -50px;
	margin-left: -50px;
}
.tsperloader21 {
	width: 25px;
	height: 25px;
	background-color: #006DF0;
	margin-right: auto;
	margin-left: auto;
	border: 4px solid rgba #006DF0;
	left: 37px;
	top: 37px;
	position: absolute;
}
#first_tsperloader21 {
	-webkit-animation: first_tsperloader21 1s infinite;
	animation: first_tsperloader21 1s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
#second_tsperloader21 {
	-webkit-animation: second_tsperloader21 1s infinite;
	animation: second_tsperloader21 1s infinite;
}
#third_tsperloader21 {
	-webkit-animation: third_tsperloader21 1s infinite;
	animation: third_tsperloader21 1s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
#forth_tsperloader21 {
	-webkit-animation: forth_tsperloader21 1s infinite;
	animation: forth_tsperloader21 1s infinite;
}
 @-webkit-keyframes first_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(150%, 150%) scale(2, 2);
 -webkit-transform: translate(150%, 150%) scale(2, 2);
 transform: translate(150%, 150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
@keyframes first_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(150%, 150%) scale(2, 2);
 -webkit-transform: translate(150%, 150%) scale(2, 2);
 transform: translate(150%, 150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
 @-webkit-keyframes second_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(-150%, 150%) scale(2, 2);
 -webkit-transform: translate(-150%, 150%) scale(2, 2);
 transform: translate(-150%, 150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
@keyframes second_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(-150%, 150%) scale(2, 2);
 -webkit-transform: translate(-150%, 150%) scale(2, 2);
 transform: translate(-150%, 150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
 @-webkit-keyframes third_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(-150%, -150%) scale(2, 2);
 -webkit-transform: translate(-150%, -150%) scale(2, 2);
 transform: translate(-150%, -150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
@keyframes third_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(-150%, -150%) scale(2, 2);
 -webkit-transform: translate(-150%, -150%) scale(2, 2);
 transform: translate(-150%, -150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
 @-webkit-keyframes forth_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(150%, -150%) scale(2, 2);
 -webkit-transform: translate(150%, -150%) scale(2, 2);
 transform: translate(150%, -150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}
@keyframes forth_tsperloader21 {
 0% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
 50% {
 -ms-transform: translate(150%, -150%) scale(2, 2);
 -webkit-transform: translate(150%, -150%) scale(2, 2);
 transform: translate(150%, -150%) scale(2, 2);
}
 100% {
 -ms-transform: translate(1, 1) scale(1, 1);
 -webkit-transform: translate(1, 1) scale(1, 1);
 transform: translate(1, 1) scale(1, 1);
}
}




                              /*Preloader Demo 21*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute22 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.tsperloader22 {
	width: 50px;
	height: 50px;
	background-color: rgba(255, 255, 255, 0);
	margin-right: auto;
	margin-left: auto;
	border: 4px solid #006DF0;
	left: 73px;
	top: 73px;
	position: absolute;
}
#first_tsperloader22 {
	-webkit-animation: first_tsperloader22_animate 1s infinite ease-in-out;
	animation: first_tsperloader22_animate 1s infinite ease-in-out;
}
#second_tsperloader22 {
	-webkit-animation: second_tsperloader22 1s forwards, second_tsperloader22_animate 1s infinite ease-in-out;
	animation: second_tsperloader22 1s forwards, second_tsperloader22_animate 1s infinite ease-in-out;
}
#third_tsperloader22 {
	-webkit-animation: third_tsperloader22 1s forwards, third_tsperloader22_animate 1s infinite ease-in-out;
	animation: third_tsperloader22 1s forwards, third_tsperloader22_animate 1s infinite ease-in-out;
}
 @-webkit-keyframes second_tsperloader22 {
 100% {
width: 100px;
height:100px;
left: 48px;
top: 48px;
}
}
@keyframes second_tsperloader22 {
100% {
width: 100px;
height:100px;
left: 48px;
top: 48px;
}
}
 @-webkit-keyframes third_tsperloader22 {
 100% {
width: 150px;
height:150px;
left: 23px;
top: 23px;
}
}
@keyframes third_tsperloader22 {
100% {
width: 150px;
height:150px;
left: 23px;
top: 23px;
}
}
 @-webkit-keyframes first_tsperloader22_animate {
 0% {
-webkit-transform: perspective(100px);
}
 50% {
-webkit-transform: perspective(100px) rotateY(-180deg);
}
 100% {
-webkit-transform: perspective(100px) rotateY(-180deg) rotateX(-180deg);
}
}
 @keyframes first_tsperloader22_animate {
 0% {
 transform: perspective(100px) rotateX(0deg) rotateY(0deg);
 -webkit-transform: perspective(100px) rotateX(0deg) rotateY(0deg);
}
50% {
 transform: perspective(100px) rotateX(-180deg) rotateY(0deg);
 -webkit-transform: perspective(100px) rotateX(-180deg) rotateY(0deg);
}
100% {
 transform: perspective(100px) rotateX(-180deg) rotateY(-180deg);
 -webkit-transform: perspective(100px) rotateX(-180deg) rotateY(-180deg);
}
}
 @-webkit-keyframes second_tsperloader22_animate {
 0% {
-webkit-transform: perspective(200px);
}
 50% {
-webkit-transform: perspective(200px) rotateY(180deg);
}
 100% {
-webkit-transform: perspective(200px) rotateY(180deg) rotateX(180deg);
}
}
 @keyframes second_tsperloader22_animate {
 0% {
 transform: perspective(200px) rotateX(0deg) rotateY(0deg);
 -webkit-transform: perspective(200px) rotateX(0deg) rotateY(0deg);
}
50% {
 transform: perspective(200px) rotateX(180deg) rotateY(0deg);
 -webkit-transform: perspective(200px) rotateX(180deg) rotateY(0deg);
}
100% {
 transform: perspective(200px) rotateX(180deg) rotateY(180deg);
 -webkit-transform: perspective(200px) rotateX(180deg) rotateY(180deg);
}
}
 @-webkit-keyframes third_tsperloader22_animate {
 0% {
-webkit-transform: perspective(300px);
}
 50% {
-webkit-transform: perspective(300px) rotateY(-180deg);
}
 100% {
-webkit-transform: perspective(300px) rotateY(-180deg) rotateX(-180deg);
}
}
 @keyframes third_tsperloader22_animate {
 0% {
 transform: perspective(300px) rotateX(0deg) rotateY(0deg);
 -webkit-transform: perspective(300px) rotateX(0deg) rotateY(0deg);
}
50% {
 transform: perspective(300px) rotateX(-180deg) rotateY(0deg);
 -webkit-transform: perspective(300px) rotateX(-180deg) rotateY(0deg);
}
100% {
 transform: perspective(300px) rotateX(-180deg) rotateY(-180deg);
 -webkit-transform: perspective(300px) rotateX(-180deg) rotateY(-180deg);
}
}



                              /*Preloader Demo 22*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute23 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 150px;
	width: 150px;
	margin-top: -75px;
	margin-left: -75px;
	-ms-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}
.tsperloader23 {
	width: 20px;
	height: 20px;
	background-color:#006DF0;
	position: absolute;
	left: 65px;
	top: 65px;
}
.tsperloader23:nth-child(2n+0) {
 margin-right: 0px;
}
#tsperloader23_one {
	-webkit-animation: tsperloader23_one 2s infinite;
	animation: tsperloader23_one 2s infinite;
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
#tsperloader23_two {
	-webkit-animation: tsperloader23_two 2s infinite;
	animation: tsperloader23_two 2s infinite;
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
#tsperloader23_three {
	-webkit-animation: tsperloader23_three 2s infinite;
	animation: tsperloader23_three 2s infinite;
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
#tsperloader23_four {
	-webkit-animation: tsperloader23_four 2s infinite;
	animation: tsperloader23_four 2s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
#tsperloader23_five {
	-webkit-animation: tsperloader23_five 2s infinite;
	animation: tsperloader23_five 2s infinite;
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
#tsperloader23_six {
	-webkit-animation: tsperloader23_six 2s infinite;
	animation: tsperloader23_six 2s infinite;
	-webkit-animation-delay: 0.7s;
	animation-delay: 0.7s;
}
#tsperloader23_seven {
	-webkit-animation: tsperloader23_seven 2s infinite;
	animation: tsperloader23_seven 2s infinite;
	-webkit-animation-delay: 0.8s;
	animation-delay: 0.8s;
}
#tsperloader23_eight {
	-webkit-animation: tsperloader23_eight 2s infinite;
	animation: tsperloader23_eight 2s infinite;
	-webkit-animation-delay: 0.9s;
	animation-delay: 0.9s;
}
#tsperloader23_big {
	position: absolute;
	width: 50px;
	height: 50px;
	left: 50px;
	top: 50px;
	-webkit-animation: tsperloader23_big 2s infinite;
	animation: tsperloader23_big 2s infinite;
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
 @-webkit-keyframes tsperloader23_big {
 50% {
-webkit-transform: scale(0.5);
}
}
 @keyframes tsperloader23_big {
 50% {
 transform: scale(0.5);
 -webkit-transform: scale(0.5);
}
}
 @-webkit-keyframes tsperloader23_one {
 50% {
-webkit-transform: translate(-65px, -65px);
}
}
 @keyframes tsperloader23_one {
 50% {
 transform: translate(-65px, -65px);
 -webkit-transform: translate(-65px, -65px);
}
}
 @-webkit-keyframes tsperloader23_two {
 50% {
-webkit-transform: translate(0, -65px);
}
}
 @keyframes tsperloader23_two {
 50% {
 transform: translate(0, -65px);
 -webkit-transform: translate(0, -65px);
}
}
 @-webkit-keyframes tsperloader23_three {
 50% {
-webkit-transform: translate(65px, -65px);
}
}
 @keyframes tsperloader23_three {
 50% {
 transform: translate(65px, -65px);
 -webkit-transform: translate(65px, -65px);
}
}
 @-webkit-keyframes tsperloader23_four {
 50% {
-webkit-transform: translate(65px, 0);
}
}
 @keyframes tsperloader23_four {
 50% {
 transform: translate(65px, 0);
 -webkit-transform: translate(65px, 0);
}
}
 @-webkit-keyframes tsperloader23_five {
 50% {
-webkit-transform: translate(65px, 65px);
}
}
 @keyframes tsperloader23_five {
 50% {
 transform: translate(65px, 65px);
 -webkit-transform: translate(65px, 65px);
}
}
 @-webkit-keyframes tsperloader23_six {
 50% {
-webkit-transform: translate(0, 65px);
}
}
 @keyframes tsperloader23_six {
 50% {
 transform:  translate(0, 65px);
 -webkit-transform:  translate(0, 65px);
}
}
 @-webkit-keyframes tsperloader23_seven {
 50% {
-webkit-transform: translate(-65px, 65px);
}
}
 @keyframes tsperloader23_seven {
 50% {
 transform: translate(-65px, 65px);
 -webkit-transform: translate(-65px, 65px);
}
}
 @-webkit-keyframes tsperloader23_eight {
 50% {
-webkit-transform: translate(-65px, 0);
}
}
 @keyframes tsperloader23_eight {
 50% {
 transform: translate(-65px, 0);
 -webkit-transform: translate(-65px, 0);
}
}



                              /*Preloader Demo 23*/

/******************************************************************************/
/******************************************************************************/



#ts-preloader-absolute24 {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
#tsperloader24 {
	width: 80px;
	height: 80px;
	background-color: #006DF0;
	-webkit-animation: animate 1s infinite ease-in-out;
	animation: animate 1s infinite ease-in-out;
	margin-right: auto;
	margin-left: auto;
	margin-top: 60px;
}
@-webkit-keyframes animate {
 0% {
-webkit-transform: perspective(160px);
}
 50% {
-webkit-transform: perspective(160px) rotateY(-180deg);
}
 100% {
-webkit-transform: perspective(160px) rotateY(-180deg) rotateX(-180deg);
}
}
 @keyframes animate {
 0% {
 transform: perspective(160px) rotateX(0deg) rotateY(0deg);
 -webkit-transform: perspective(160px) rotateX(0deg) rotateY(0deg);
}
50% {
 transform: perspective(160px) rotateX(-180deg) rotateY(0deg);
 -webkit-transform: perspective(160px) rotateX(-180deg) rotateY(0deg);
}
100% {
 transform: perspective(160px) rotateX(-180deg) rotateY(-180deg);
 -webkit-transform: perspective(160px) rotateX(-180deg) rotateY(-180deg);
}
}



                              /*Preloader Demo 24*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute25 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}


[class^="tsperloader_loader"] {
 margin: 50px auto;
 width: 60px;
 height: 30px;
}
[class^="tsperloader_loader"] > div {
 float: left;
 background: #006DF0;
 height: 100%;
 width: 5px;
 margin-right: 1px;
 display: inline-block;
}
[class^="tsperloader_loader"] .tsperloader25_1 {
 -webkit-animation-delay: 0.05s;
 -moz-animation-delay: 0.05s;
 -o-animation-delay: 0.05s;
 animation-delay: 0.05s;
}
[class^="tsperloader_loader"] .tsperloader25_2 {
 -webkit-animation-delay: 0.1s;
 -moz-animation-delay: 0.1s;
 -o-animation-delay: 0.1s;
 animation-delay: 0.1s;
}
[class^="tsperloader_loader"] .tsperloader25_3 {
 -webkit-animation-delay: 0.15s;
 -moz-animation-delay: 0.15s;
 -o-animation-delay: 0.15s;
 animation-delay: 0.15s;
}
[class^="tsperloader_loader"] .tsperloader25_4 {
 -webkit-animation-delay: 0.2s;
 -moz-animation-delay: 0.2s;
 -o-animation-delay: 0.2s;
 animation-delay: 0.2s;
}
[class^="tsperloader_loader"] .tsperloader25_5 {
 -webkit-animation-delay: 0.25s;
 -moz-animation-delay: 0.25s;
 -o-animation-delay: 0.25s;
 animation-delay: 0.25s;
}
[class^="tsperloader_loader"] .tsperloader25_6 {
 -webkit-animation-delay: 0.3s;
 -moz-animation-delay: 0.3s;
 -o-animation-delay: 0.3s;
 animation-delay: 0.3s;
}
[class^="tsperloader_loader"] .tsperloader25_7 {
 -webkit-animation-delay: 0.35s;
 -moz-animation-delay: 0.35s;
 -o-animation-delay: 0.35s;
 animation-delay: 0.35s;
}
[class^="tsperloader_loader"] .tsperloader25_8 {
 -webkit-animation-delay: 0.4s;
 -moz-animation-delay: 0.4s;
 -o-animation-delay: 0.4s;
 animation-delay: 0.4s;
}
[class^="tsperloader_loader"] .tsperloader25_9 {
 -webkit-animation-delay: 0.45s;
 -moz-animation-delay: 0.45s;
 -o-animation-delay: 0.45s;
 animation-delay: 0.45s;
}
[class^="tsperloader_loader"] .tsperloader25_10 {
 -webkit-animation-delay: 0.5s;
 -moz-animation-delay: 0.5s;
 -o-animation-delay: 0.5s;
 animation-delay: 0.5s;
}
.tsperloader_loader > div {
	-webkit-animation: loading 1.5s infinite ease-in-out;
	-moz-animation: loading 1.5s infinite ease-in-out;
	-o-animation: loading 1.5s infinite ease-in-out;
	animation: loading 1.5s infinite ease-in-out;
	-webkit-transform: scaleY(0.05) translateX(-10px);
	-moz-transform: scaleY(0.05) translateX(-10px);
	-ms-transform: scaleY(0.05) translateX(-10px);
	-o-transform: scaleY(0.05) translateX(-10px);
	transform: scaleY(0.05) translateX(-10px);
}
 @-webkit-keyframes loading {
 50% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #56D7C6;
}
}
@-moz-keyframes loading {
 50% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #56D7C6;
}
}
@-o-keyframes loading {
 50% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #56D7C6;
}
}
@keyframes loading {
 50% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #56D7C6;
}
}





                              /*Preloader Demo 25*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute26 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.tsperloader_loader2 > div {
	-webkit-animation: loading2 1.5s infinite ease-in-out;
	-moz-animation: loading2 1.5s infinite ease-in-out;
	-o-animation: loading2 1.5s infinite ease-in-out;
	animation: loading2 1.5s infinite ease-in-out;
	-webkit-transform: scaleY(0.05) translateX(-5px);
	-moz-transform: scaleY(0.05) translateX(-5px);
	-ms-transform: scaleY(0.05) translateX(-5px);
	-o-transform: scaleY(0.05) translateX(-5px);
	transform: scaleY(0.05) translateX(-5px);
}
 @-webkit-keyframes loading2 {
 10% {
 background: #56D7C6;
}
 15% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #56D7C6;
}
 90%, 100% {
 -webkit-transform: scaleY(0.05) translateX(-5px);
 -moz-transform: scaleY(0.05) translateX(-5px);
 -ms-transform: scaleY(0.05) translateX(-5px);
 -o-transform: scaleY(0.05) translateX(-5px);
 transform: scaleY(0.05) translateX(-5px);
}
}
@-moz-keyframes loading2 {
 10% {
 background: #006DF0;
}
 15% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #006DF0;
}
 90%, 100% {
 -webkit-transform: scaleY(0.05) translateX(-5px);
 -moz-transform: scaleY(0.05) translateX(-5px);
 -ms-transform: scaleY(0.05) translateX(-5px);
 -o-transform: scaleY(0.05) translateX(-5px);
 transform: scaleY(0.05) translateX(-5px);
}
}
@-o-keyframes loading2 {
 10% {
 background: #006DF0;
}
 15% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #006DF0;
}
 90%, 100% {
 -webkit-transform: scaleY(0.05) translateX(-5px);
 -moz-transform: scaleY(0.05) translateX(-5px);
 -ms-transform: scaleY(0.05) translateX(-5px);
 -o-transform: scaleY(0.05) translateX(-5px);
 transform: scaleY(0.05) translateX(-5px);
}
}
@keyframes loading2 {
 10% {
 background: #006DF0;
}
 15% {
 -webkit-transform: scaleY(1.2) translateX(10px);
 -moz-transform: scaleY(1.2) translateX(10px);
 -ms-transform: scaleY(1.2) translateX(10px);
 -o-transform: scaleY(1.2) translateX(10px);
 transform: scaleY(1.2) translateX(10px);
 background: #006DF0;
}
 90%, 100% {
 -webkit-transform: scaleY(0.05) translateX(-5px);
 -moz-transform: scaleY(0.05) translateX(-5px);
 -ms-transform: scaleY(0.05) translateX(-5px);
 -o-transform: scaleY(0.05) translateX(-5px);
 transform: scaleY(0.05) translateX(-5px);
}
}





                              /*Preloader Demo 26*/

/******************************************************************************/
/******************************************************************************/







#ts-preloader-absolute27 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.tsperloader_loader3 {
	height: 40px;
}
.tsperloader_loader3 > div {
	position: relative;
	bottom: 0;
	margin-top: 35px;
	height: 5px;
	-webkit-animation: loading5 1.5s infinite ease-in-out;
	-moz-animation: loading5 1.5s infinite ease-in-out;
	-o-animation: loading5 1.5s infinite ease-in-out;
	animation: loading5 1.5s infinite ease-in-out;
}
.tsperloader_loader3 .tsperloader26_1 {
	-webkit-animation-delay: -1.5s;
	-moz-animation-delay: -1.5s;
	-o-animation-delay: -1.5s;
	animation-delay: -1.5s;
}
.tsperloader_loader3 .tsperloader26_2 {
	-webkit-animation-delay: -1.4s;
	-moz-animation-delay: -1.4s;
	-o-animation-delay: -1.4s;
	animation-delay: -1.4s;
}
.tsperloader_loader3 .tsperloader26_3 {
	-webkit-animation-delay: -1.3s;
	-moz-animation-delay: -1.3s;
	-o-animation-delay: -1.3s;
	animation-delay: -1.3s;
}
.tsperloader_loader3 .tsperloader26_4 {
	-webkit-animation-delay: -1.2s;
	-moz-animation-delay: -1.2s;
	-o-animation-delay: -1.2s;
	animation-delay: -1.2s;
}
.tsperloader_loader3 .tsperloader26_5 {
	-webkit-animation-delay: -1.1s;
	-moz-animation-delay: -1.1s;
	-o-animation-delay: -1.1s;
	animation-delay: -1.1s;
}
.tsperloader_loader3 .tsperloader26_6 {
	-webkit-animation-delay: -1s;
	-moz-animation-delay: -1s;
	-o-animation-delay: -1s;
	animation-delay: -1s;
}
.tsperloader_loader3 .tsperloader26_7 {
	-webkit-animation-delay: -0.9s;
	-moz-animation-delay: -0.9s;
	-o-animation-delay: -0.9s;
	animation-delay: -0.9s;
}
.tsperloader_loader3 .tsperloader26_8 {
	-webkit-animation-delay: -0.8s;
	-moz-animation-delay: -0.8s;
	-o-animation-delay: -0.8s;
	animation-delay: -0.8s;
}
.tsperloader_loader3 .tsperloader26_9 {
	-webkit-animation-delay: -0.7s;
	-moz-animation-delay: -0.7s;
	-o-animation-delay: -0.7s;
	animation-delay: -0.7s;
}
.tsperloader_loader3 .tsperloader26_10 {
	-webkit-animation-delay: -0.6s;
	-moz-animation-delay: -0.6s;
	-o-animation-delay: -0.6s;
	animation-delay: -0.6s;
}
.tsperloader_loader3 .tsperloader26_11 {
	-webkit-animation-delay: -0.5s;
	-moz-animation-delay: -0.5s;
	-o-animation-delay: -0.5s;
	animation-delay: -0.5s;
}
 @-webkit-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@-moz-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@-o-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}




                              /*Preloader Demo 27*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute28 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.tsperloader_loader4 .tsperloader26_1 {
	-webkit-animation-delay: -1.5s;
	-moz-animation-delay: -1.5s;
	-o-animation-delay: -1.5s;
	animation-delay: -1.5s;
}
.tsperloader_loader4 .tsperloader26_2 {
	-webkit-animation-delay: -1.4s;
	-moz-animation-delay: -1.4s;
	-o-animation-delay: -1.4s;
	animation-delay: -1.4s;
}
.tsperloader_loader4 .tsperloader26_3 {
	-webkit-animation-delay: -1.3s;
	-moz-animation-delay: -1.3s;
	-o-animation-delay: -1.3s;
	animation-delay: -1.3s;
}
.tsperloader_loader4 .tsperloader26_4 {
	-webkit-animation-delay: -1.2s;
	-moz-animation-delay: -1.2s;
	-o-animation-delay: -1.2s;
	animation-delay: -1.2s;
}
.tsperloader_loader4 .tsperloader26_5 {
	-webkit-animation-delay: -1.1s;
	-moz-animation-delay: -1.1s;
	-o-animation-delay: -1.1s;
	animation-delay: -1.1s;
}
.tsperloader_loader4 .tsperloader26_6 {
	-webkit-animation-delay: -1s;
	-moz-animation-delay: -1s;
	-o-animation-delay: -1s;
	animation-delay: -1s;
}
.tsperloader_loader4 .tsperloader26_7 {
	-webkit-animation-delay: -0.9s;
	-moz-animation-delay: -0.9s;
	-o-animation-delay: -0.9s;
	animation-delay: -0.9s;
}
.tsperloader_loader4 .tsperloader26_8 {
	-webkit-animation-delay: -0.8s;
	-moz-animation-delay: -0.8s;
	-o-animation-delay: -0.8s;
	animation-delay: -0.8s;
}
.tsperloader_loader4 .tsperloader26_9 {
	-webkit-animation-delay: -0.7s;
	-moz-animation-delay: -0.7s;
	-o-animation-delay: -0.7s;
	animation-delay: -0.7s;
}
.tsperloader_loader4 .tsperloader26_10 {
	-webkit-animation-delay: -0.6s;
	-moz-animation-delay: -0.6s;
	-o-animation-delay: -0.6s;
	animation-delay: -0.6s;
}
.tsperloader_loader4 .tsperloader26_11 {
	-webkit-animation-delay: -0.5s;
	-moz-animation-delay: -0.5s;
	-o-animation-delay: -0.5s;
	animation-delay: -0.5s;
}
 @-webkit-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@-moz-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@-o-keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
@keyframes loading5 {
 50% {
 height: 100%;
 margin-top: 0;
 background: #006DF0;
}
}
.tsperloader_loader4 {
	height: 40px;
	width: 90px;
	overflow: hidden;
}
.tsperloader_loader4 > div {
	width: 8px;
	position: relative;
	bottom: -2px;
	margin-top: 37px;
	height: 3px;
	transform: skewY(0deg);
	-webkit-animation: loading6 1.5s infinite ease-in-out;
	-moz-animation: loading6 1.5s infinite ease-in-out;
	-o-animation: loading6 1.5s infinite ease-in-out;
	animation: loading6 1.5s infinite ease-in-out;
}
 @-webkit-keyframes loading6 {
 25% {
 transform: skewY(25deg);
}
 50% {
 height: 100%;
 transform: skewY(0);
 margin-top: 0;
 background: #006DF0;
}
 75% {
 transform: skewY(-25deg);
}
}
@-moz-keyframes loading6 {
 25% {
 transform: skewY(25deg);
}
 50% {
 height: 100%;
 transform: skewY(0);
 margin-top: 0;
 background: #006DF0;
}
 75% {
 transform: skewY(-25deg);
}
}
@-o-keyframes loading6 {
 25% {
 transform: skewY(25deg);
}
 50% {
 height: 100%;
 transform: skewY(0);
 margin-top: 0;
 background: #006DF0;
}
 75% {
 transform: skewY(-25deg);
}
}
@keyframes loading6 {
 25% {
 transform: skewY(25deg);
}
 50% {
 height: 100%;
 transform: skewY(0);
 margin-top: 0;
 background: #006DF0;
}
 75% {
 transform: skewY(-25deg);
}
}







                              /*Preloader Demo 28*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute29 {
	position: absolute;
	left: 50%;
	top: 75%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
#tsperloader29 span {
	background: #006DF0 none repeat scroll 0 0;
	border-radius: 0;
	display: inline-block;
	height: 15px;
	width: 15px;
}
 #tsperloader29 span:nth-child(1) {
 -webkit-animation: temp 1s 0.05s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.05s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(2) {
 -webkit-animation: temp 1s 0.1s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.1s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(3) {
 -webkit-animation: temp 1s 0.15s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.15s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(4) {
 -webkit-animation: temp 1s 0.2s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.2s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(5) {
 -webkit-animation: temp 1s 0.25s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.25s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(6) {
 -webkit-animation: temp 1s 0.3s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.3s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(7) {
 -webkit-animation: temp 1s 0.35s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.35s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(8) {
 -webkit-animation: temp 1s 0.4s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.4s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(9) {
 -webkit-animation: temp 1s 0.45s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.45s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(10) {
 -webkit-animation: temp 1s 0.5s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.5s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(11) {
 -webkit-animation: temp 1s 0.55s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.55s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(12) {
 -webkit-animation: temp 1s 0.6s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.6s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(13) {
 -webkit-animation: temp 1s 0.65s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.65s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(14) {
 -webkit-animation: temp 1s 0.7s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.7s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(15) {
 -webkit-animation: temp 1s 0.75s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.75s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(16) {
 -webkit-animation: temp 1s 0.8s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.8s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(17) {
 -webkit-animation: temp 1s 0.85s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.85s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(18) {
 -webkit-animation: temp 1s 0.9s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.9s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(19) {
 -webkit-animation: temp 1s 0.95s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 0.95s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
#tsperloader29 span:nth-child(20) {
 -webkit-animation: temp 1s 1s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 animation: temp 1s 1s infinite cubic-bezier(0.005, 0.56, 0.58, 1.59);
 width: 7.5px;
 height: 7.5px;
 margin: 0 2px;
}
@-webkit-keyframes scale {
 0% {
 -webkit-transform: scale(0);
 transform: scale(0);
}
 25% {
 -webkit-transform: scale(0.9, 0.9);
 transform: scale(0.9, 0.9);
 background: #006DF0;
}
 50% {
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
 margin: 0 3px;
 background: #006DF0;
}
 100% {
 -webkit-transform: scale(0);
 transform: scale(0);
}
}
@keyframes scale {
 0% {
 -webkit-transform: scale(0);
 transform: scale(0);
}
 25% {
 -webkit-transform: scale(0.9, 0.9);
 transform: scale(0.9, 0.9);
 background: #006DF0;
}
 50% {
 -webkit-transform: scale(1, 1);
 transform: scale(1, 1);
 margin: 0 3px;
 background: #006DF0;
}
 100% {
 -webkit-transform: scale(0);
 transform: scale(0);
}
}
@-webkit-keyframes rotateY {
 0% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
}
 50% {
 -webkit-transform: rotateY(90deg);
 transform: rotateY(90deg);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
}
}
@keyframes rotateY {
 0% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
}
 50% {
 -webkit-transform: rotateY(90deg);
 transform: rotateY(90deg);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
}
}
@-webkit-keyframes rotateX {
 0% {
 -webkit-transform: rotateX(0deg);
 transform: rotateX(0deg);
}
 50% {
 -webkit-transform: rotateX(90deg) scale(0.5, 0.5);
 transform: rotateX(90deg) scale(0.5, 0.5);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateX(0deg);
 transform: rotateX(0deg);
}
}
@keyframes rotateX {
 0% {
 -webkit-transform: rotateX(0deg);
 transform: rotateX(0deg);
}
 50% {
 -webkit-transform: rotateX(90deg) scale(0.5, 0.5);
 transform: rotateX(90deg) scale(0.5, 0.5);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateX(0deg);
 transform: rotateX(0deg);
}
}
@-webkit-keyframes push {
 0% {
 -webkit-transform: translateX(0px) scale(0.9, 0.6);
 transform: translateX(0px) scale(0.9, 0.6);
}
 50% {
 -webkit-transform: translateY(-20px) scale(0.7, 1.1);
 transform: translateY(-20px) scale(0.7, 1.1);
 background: #006DF0;
}
 100% {
 -webkit-transform: translateX(0px) scale(0.9, 0.6);
 transform: translateX(0px) scale(0.9, 0.6);
}
}
@keyframes push {
 0% {
 -webkit-transform: translateX(0px) scale(0.9, 0.6);
 transform: translateX(0px) scale(0.9, 0.6);
}
 50% {
 -webkit-transform: translateY(-20px) scale(0.7, 1.1);
 transform: translateY(-20px) scale(0.7, 1.1);
 background: #006DF0;
}
 100% {
 -webkit-transform: translateX(0px) scale(0.9, 0.6);
 transform: translateX(0px) scale(0.9, 0.6);
}
}
@-webkit-keyframes rotateZ {
 0% {
 -webkit-transform: rotateZ(-20deg);
 transform: rotateZ(-20deg);
}
 50% {
 -webkit-transform: rotateZ(20deg) scaleY(1.2);
 transform: rotateZ(20deg) scaleY(1.2);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateZ(-20deg);
 transform: rotateZ(-20deg);
}
}
@keyframes rotateZ {
 0% {
 -webkit-transform: rotateZ(-20deg);
 transform: rotateZ(-20deg);
}
 50% {
 -webkit-transform: rotateZ(20deg) scaleY(1.2);
 transform: rotateZ(20deg) scaleY(1.2);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateZ(-20deg);
 transform: rotateZ(-20deg);
}
}
@-webkit-keyframes cuve {
 0% {
 -webkit-transform: rotateY(-90deg) perspective(50px);
 transform: rotateY(-90deg) perspective(50px);
 background: #006DF0;
}
 50% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateY(90deg) perspective(50px);
 transform: rotateY(90deg) perspective(50px);
 -webkit-transform-origin: 100% 50%;
 transform-origin: 100% 50%;
 background: #006DF0;
}
}
@keyframes cuve {
 0% {
 -webkit-transform: rotateY(-90deg) perspective(50px);
 transform: rotateY(-90deg) perspective(50px);
 background: #006DF0;
}
 50% {
 -webkit-transform: rotateY(0deg);
 transform: rotateY(0deg);
 background: #006DF0;
}
 100% {
 -webkit-transform: rotateY(90deg) perspective(50px);
 transform: rotateY(90deg) perspective(50px);
 -webkit-transform-origin: 100% 50%;
 transform-origin: 100% 50%;
 background: #006DF0;
}
}
@-webkit-keyframes temp {
 50% {
 -webkit-transform: scale(1, 5);
 transform: scale(1, 5);
 background: #006DF0;
}
}
@keyframes temp {
 50% {
 -webkit-transform: scale(1, 5);
 transform: scale(1, 5);
 background: #006DF0;
}
}






                              /*Preloader Demo 29 */

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute30 {
	position: absolute;
	left: 60%;
	top: 74%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
#absolute30 span {
	display: inline-block;
	height: 15px;
	width: 15px;
	background: #006DF0;
	border-radius: 0px;
}
 #absolute30 span:nth-child(1) {
 -webkit-border-radius: 0;
 -webkit-background-clip: padding-box;
 -moz-border-radius: 0;
 -moz-background-clip: padding;
 border-radius: 0;
 background-clip: padding-box;
 border-radius: 500px;
 -webkit-animation: scale 1s 0.1s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
 animation: scale 1s 0.1s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
#absolute30 span:nth-child(2) {
 -webkit-border-radius: 0;
 -webkit-background-clip: padding-box;
 -moz-border-radius: 0;
 -moz-background-clip: padding;
 border-radius: 0;
 background-clip: padding-box;
 border-radius: 500px;
 -webkit-animation: scale 1s 0.2s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
 animation: scale 1s 0.2s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
#absolute30 span:nth-child(3) {
 -webkit-border-radius: 0;
 -webkit-background-clip: padding-box;
 -moz-border-radius: 0;
 -moz-background-clip: padding;
 border-radius: 0;
 background-clip: padding-box;
 border-radius: 500px;
 -webkit-animation: scale 1s 0.3s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
 animation: scale 1s 0.3s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
#absolute30 span:nth-child(4) {
 -webkit-border-radius: 0;
 -webkit-background-clip: padding-box;
 -moz-border-radius: 0;
 -moz-background-clip: padding;
 border-radius: 0;
 background-clip: padding-box;
 border-radius: 500px;
 -webkit-animation: scale 1s 0.4s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
 animation: scale 1s 0.4s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
#absolute30 span:nth-child(5) {
 -webkit-border-radius: 0;
 -webkit-background-clip: padding-box;
 -moz-border-radius: 0;
 -moz-background-clip: padding;
 border-radius: 0;
 background-clip: padding-box;
 border-radius: 500px;
 -webkit-animation: scale 1s 0.5s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
 animation: scale 1s 0.5s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
}



                              /*Preloader Demo 30*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute31 {
	position: absolute;
	left: 60%;
	top: 74%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
#absolute31 span {
	display: inline-block;
	height: 15px;
	width: 15px;
	background: #006DF0;
	border-radius: 0px;
}
 #absolute31 span:nth-child(1) {
 -webkit-animation: rotateY 4s 0.3s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateY 4s 0.3s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute31 span:nth-child(2) {
 -webkit-animation: rotateY 4s 0.6s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateY 4s 0.6s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute31 span:nth-child(3) {
 -webkit-animation: rotateY 4s 0.9s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateY 4s 0.9s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute31 span:nth-child(4) {
 -webkit-animation: rotateY 4s 1.2s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateY 4s 1.2s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute31 span:nth-child(5) {
 -webkit-animation: rotateY 4s 1.5s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateY 4s 1.5s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}






                              /*Preloader Demo 31*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute32 {
	position: absolute;
	left: 60%;
	top: 74%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
#absolute32 span {
	display: inline-block;
	height: 15px;
	width: 15px;
	background: #006DF0;
	border-radius: 0px;
}
 #absolute32 span:nth-child(1) {
 -webkit-animation: rotateX 2s 0.1s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateX 2s 0.1s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute32 span:nth-child(2) {
 -webkit-animation: rotateX 2s 0.2s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateX 2s 0.2s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute32 span:nth-child(3) {
 -webkit-animation: rotateX 2s 0.3s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateX 2s 0.3s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute32 span:nth-child(4) {
 -webkit-animation: rotateX 2s 0.4s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateX 2s 0.4s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}
#absolute32 span:nth-child(5) {
 -webkit-animation: rotateX 2s 0.5s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
 animation: rotateX 2s 0.5s infinite cubic-bezier(0.65, 0.03, 0.735, 0.045);
}






                              /*Preloader Demo 32*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute33 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.clear-loading {
	text-align: center;
	margin: 0 auto;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-o-box-sizing: border-box;
	box-sizing: border-box;
}
.loading-effect-33 {
	width: 100px;
	height: 100px;
}
.loading-effect-33 span {
	display: block;
	-webit-border-radius: 50%;
	-moz-border-radius: 50%;
	-ms-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	border: 4px solid #fff;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}
.loading-effect-33 span:first-child {
	width: 100%;
	height: 100%;
	border-color: #006DF0;
	border-left-color: transparent;
	top: 0;
	left: 0;
	-webkit-animation: effect-1-1 4s infinite linear;
	-moz-animation: effect-1-1 4s infinite linear;
	-ms-animation: effect-1-1 4s infinite linear;
	-o-animation: effect-1-1 4s infinite linear;
	animation: effect-1-1 4s infinite linear;
}
.loading-effect-33 span:nth-child(2) {
 width: 75%;
 height: 75%;
 border-color: #006DF0;
 border-right-color: transparent;
 top: 12.5%;
 left: 12.5%;
 -webkit-animation: effect-1-2 3s infinite linear;
 -moz-animation: effect-1-2 3s infinite linear;
 -ms-animation: effect-1-2 3s infinite linear;
 -o-animation: effect-1-2 3s infinite linear;
 animation: effect-1-2 3s infinite linear;
}
.loading-effect-33 span:last-child {
	width: 50%;
	height: 50%;
	border-color: #32465f;
	border-bottom-color: transparent;
	top: 25%;
	left: 25%;
	-webkit-animation: effect-1-1 4s infinite linear;
	-moz-animation: effect-1-1 4s infinite linear;
	-ms-animation: effect-1-1 4s infinite linear;
	-o-animation: effect-1-1 4s infinite linear;
	animation: effect-1-1 4s infinite linear;
}
@-webkit-keyframes effect-1-1 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}
@keyframes effect-1-1 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}
@-webkit-keyframes effect-1-2 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(-360deg);
	-moz-transform: rotate(-360deg);
	-ms-transform: rotate(-360deg);
	-o-transform: rotate(-360deg);
	transform: rotate(-360deg);
}
}
@keyframes effect-1-2 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(-360deg);
	-moz-transform: rotate(-360deg);
	-ms-transform: rotate(-360deg);
	-o-transform: rotate(-360deg);
	transform: rotate(-360deg);
}
}







                              /*Preloader Demo 33*/

/******************************************************************************/
/******************************************************************************/







#ts-preloader-absolute34 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.loading-effect-34 {
	width: 100px;
	height: 100px;
}
.loading-effect-34 > span, .loading-effect-34 > span:before, .loading-effect-34 > span:after {
	content: "";
	display: block;
	border-radius: 50%;
	border: 2px solid #006DF0;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}
.loading-effect-34 > span {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	border-left-color: transparent;
	-webkit-animation: effect-2 2s infinite linear;
	-moz-animation: effect-2 2s infinite linear;
	-ms-animation: effect-2 2s infinite linear;
	-o-animation: effect-2 2s infinite linear;
	animation: effect-2 2s infinite linear;
}
.loading-effect-34 > span:before {
	width: 75%;
	height: 75%;
	border-right-color: transparent;
}
.loading-effect-34 > span:after {
	width: 50%;
	height: 50%;
	border-bottom-color: transparent;
}
@-webkit-keyframes effect-2 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}
@keyframes effect-2 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}







                              /*Preloader Demo 34*/

/******************************************************************************/
/******************************************************************************/







#ts-preloader-absolute35 {
	position: absolute;
	left: 50%;
	top: 60%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}
.loading-effect-35 {
	width: 100px;
	height: 100px;
}
.loading-effect-35 > div {
	width: 100%;
	height: 100%;
	border: 1px solid #006DF0;
	border-radius: 50%;
}
.loading-effect-35 span {
	background: #fff;
	display: block;
	width: 15%;
	height: 15%;
	border-radius: 50%;
	position: absolute;
	left: 50%;
	-webkit-transform: translate(-50%, 0);
	-moz-transform: translate(-50%, 0);
	-ms-transform: translate(-50%, 0);
	-o-transform: translate(-50%, 0);
	transform: translate(-50%, 0);
	-webikt-transform-origin: 0 49px;
	-moz-transform-origin: 0 49px;
	-o-transform-origin: 0 49px;
	transform-origin: 0 49px;
}
.loading-effect-35 span:first-child {
	background: #19be9b;
	-webkit-animation: effect-4-1 1.5s infinite linear;
	-moz-animation: effect-4-1 1.5s infinite linear;
	-ms-animation: effect-4-1 1.5s infinite linear;
	-o-animation: effect-4-1 1.5s infinite linear;
	animation: effect-4-1 1.5s infinite linear;
}
.loading-effect-35 span:nth-child(2) {
 background: #006DF0;
 -webkit-animation: effect-4-1 2s infinite linear;
 -moz-animation: effect-4-1 2s infinite linear;
 -ms-animation: effect-4-1 2s infinite linear;
 -o-animation: effect-4-1 2s infinite linear;
 animation: effect-4-1 2s infinite linear;
}
.loading-effect-35 span:nth-child(3) {
 background: #e64b3c;
 -webkit-animation: effect-4-1 2.5s infinite linear;
 -moz-animation: effect-4-1 2.5s infinite linear;
 -ms-animation: effect-4-1 2.5s infinite linear;
 -o-animation: effect-4-1 2.5s infinite linear;
 animation: effect-4-1 2.5s infinite linear;
}
.loading-effect-35 span:nth-child(4) {
 background: #32465f;
 -webkit-animation: effect-4-1 3s infinite linear;
 -moz-animation: effect-4-1 3s infinite linear;
 -ms-animation: effect-4-1 3s infinite linear;
 -o-animation: effect-4-1 3s infinite linear;
 animation: effect-4-1 3s infinite linear;
}
@-webkit-keyframes effect-4-1 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}
@keyframes effect-4-1 {
 from {
 -webkit-transform: rotate(0deg);
 -moz-transform: rotate(0deg);
 -ms-transform: rotate(0deg);
 -o-transform: rotate(0deg);
 transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	transform: rotate(360deg);
}
}







                              /*Preloader Demo 35*/

/******************************************************************************/
/******************************************************************************/









#ts-preloader-absolute36 {
	height: 200px;
	left: 67%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 68%;
	width: 200px;
}



@-webkit-keyframes fade {
 from {
opacity: 1;
}
to {
	opacity: 0.2;
}
}
@keyframes fade {
 from {
opacity: 1;
}
to {
	opacity: 0.2;
}
}
 @-webkit-keyframes rotate {
 from {
-webkit-transform: rotate(0deg);
}
to {
	-webkit-transform: rotate(360deg);
}
}
@keyframes rotate {
 from {
transform: rotate(0deg);
}
to {
	transform: rotate(360deg);
}
}



@-webkit-keyframes windcatcherSpin {
 from {
-webkit-transform: rotateY(0deg) rotateX(-20deg);
}
to {
	-webkit-transform: rotateY(360deg) rotateX(-20deg);
}
}
@keyframes windcatcherSpin {
 from {
transform: rotateY(0deg) rotateX(-20deg);
}
to {
	transform: rotateY(360deg) rotateX(-20deg);
}
}
 @-webkit-keyframes windcatcherBg {
 0% {
background-color: rgb(255, 0, 0);
}
 50% {
background-color: rgb(150, 0, 0);
}
 51% {
background-color: rgb(255, 100, 100);
}
 70% {
background-color: rgb(255, 25, 25);
}
 100% {
background-color: #006DF0;
}
}
@keyframes windcatcherBg {
 0% {
background-color: rgb(255, 0, 0);
}
 50% {
background-color: rgb(150, 0, 0);
}
 51% {
background-color: rgb(255, 100, 100);
}
 70% {
background-color: rgb(255, 25, 25);
}
 100% {
background-color: rgb(255, 0, 0);
}
}
.tsspinner.windcatcher {
	width: 4em;
	perspective: 50em;
	-webkit-animation: rotate 4s linear infinite;
	animation: rotate 4s linear infinite;
}
.tsspinner.windcatcher .tsblade {
	height: 0.5em;
	background: red;
	margin-bottom: 0.1em;
	-webkit-animation: windcatcherSpin 4s linear infinite, windcatcherBg 2s linear infinite;
	animation: windcatcherSpin 4s linear infinite, windcatcherBg 2s linear infinite;
}
 .tsspinner.windcatcher .tsblade:nth-child(1) {
-webkit-animation-delay: 0s;
animation-delay: 0s;
}
.tsspinner.windcatcher .tsblade:nth-child(2) {
-webkit-animation-delay: 0.25s;
animation-delay: 0.25s;
}
.tsspinner.windcatcher .tsblade:nth-child(3) {
-webkit-animation-delay: 0.5s;
animation-delay: 0.5s;
}
.tsspinner.windcatcher .tsblade:nth-child(4) {
-webkit-animation-delay: 0.75s;
animation-delay: 0.75s;
}
.tsspinner.windcatcher .tsblade:nth-child(5) {
-webkit-animation-delay: 1s;
animation-delay: 1s;
}
.tsspinner.windcatcher .tsblade:nth-child(6) {
-webkit-animation-delay: 1.25s;
animation-delay: 1.25s;
}
.tsspinner.windcatcher .tsblade:nth-child(7) {
-webkit-animation-delay: 1.5s;
animation-delay: 1.5s;
}
.tsspinner.windcatcher .tsblade:nth-child(8) {
-webkit-animation-delay: 1.75s;
animation-delay: 1.75s;
}





                              /*Preloader Demo 36*/

/******************************************************************************/
/******************************************************************************/







#ts-preloader-absolute37 {
	position: absolute;
	left: 76%;
	top: 74%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}



@-webkit-keyframes slabMove {
 0% {
-webkit-transform: translateY(0) rotateX(30deg);
opacity: 0;
}
 10% {
-webkit-transform: translateY(-48%);
opacity: 1;
}
 90% {
-webkit-transform: translateY(-422%);
opacity: 0.1;
}
 100% {
-webkit-transform: translateY(-480%);
opacity: 0;
}
}
@keyframes slabMove {
 0% {
transform: translateY(0) rotateX(30deg);
opacity: 0;
}
 10% {
transform: translateY(-48%);
opacity: 1;
}
 90% {
transform: translateY(-422%);
opacity: 0.1;
}
 100% {
transform: translateY(-480%);
opacity: 0;
}
}
.tsspinner.tsslabs {
	width: 4em;
	height: 4em;
	-webkit-transform: perspective(15em) rotateX(65deg) rotateZ(-30deg);
	transform: perspective(15em) rotateX(65deg) rotateZ(-30deg);
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d;
}
.tsspinner.tsslabs .tsslab {
	position: absolute;
	width: 4em;
	height: 4em;
	background: #006DF0;
	opacity: 0;
	box-shadow: -0.08em 0.15em 0 #006DF0;
	-webkit-transform-origin: 50% 0%;
	transform-origin: 50% 0%;
	-webkit-animation: slabMove 4s linear infinite;
	animation: slabMove 4s linear infinite;
}
 .tsspinner.tsslabs .tsslab:nth-child(1) {
-webkit-animation-delay: 0s;
animation-delay: 0s;
}
.tsspinner.tsslabs .tsslab:nth-child(2) {
-webkit-animation-delay: 1s;
animation-delay: 1s;
}
.tsspinner.tsslabs .tsslab:nth-child(3) {
-webkit-animation-delay: 2s;
animation-delay: 2s;
}
.tsspinner.tsslabs .tsslab:nth-child(4) {
-webkit-animation-delay: 3s;
animation-delay: 3s;
}








                              /*Preloader Demo 37*/

/******************************************************************************/
/******************************************************************************/










#ts-preloader-absolute38 {
	height: 200px;
	left: 66%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 60%;
	width: 200px;
}

@-webkit-keyframes newtonBallFirst {
 0% {
-webkit-transform: rotate(70deg);
}
 50% {
-webkit-transform: rotate(0deg);
}
 100% {
-webkit-transform: rotate(0deg);
}
}
@-webkit-keyframes newtonBallMiddle {
 0% {
-webkit-transform: rotate(0deg);
}
 50% {
-webkit-transform: rotate(0deg);
}
 51% {
-webkit-transform: rotate(-0.5deg);
}
 52% {
-webkit-transform: rotate(0.5deg);
}
 53% {
-webkit-transform: rotate(0deg);
}
 100% {
-webkit-transform: rotate(0deg);
}
}
@-webkit-keyframes newtonBallLast {
 0% {
-webkit-transform: rotate(0deg);
}
 50% {
-webkit-transform: rotate(0deg);
}
 100% {
-webkit-transform: rotate(-70deg);
}
}
@keyframes newtonBallFirst {
 0% {
transform: rotate(70deg);
}
 50% {
transform: rotate(0deg);
}
 100% {
transform: rotate(0deg);
}
}
@keyframes newtonBallMiddle {
 0% {
transform: rotate(0deg);
}
 50% {
transform: rotate(0deg);
}
 51% {
transform: rotate(-0.5deg);
}
 52% {
transform: rotate(0.5deg);
}
 53% {
transform: rotate(0deg);
}
 100% {
transform: rotate(0deg);
}
}
@keyframes newtonBallLast {
 0% {
transform: rotate(0deg);
}
 50% {
transform: rotate(0deg);
}
 100% {
transform: rotate(-70deg);
}
}
.tsspinner.newton .tsball {
	position: relative;
	display: inline-block;
	width: 1em;
	height: 6em;
	-webkit-transform-origin: 50% 0%;
	transform-origin: 50% 0%;
}
 .tsspinner.newton .tsball::after {
 content: '';
 position: absolute;
 left: 0;
 bottom: 0;
 width: 1em;
 height: 1em;
 border-radius: 100%;
 background: radial-gradient(circle at 40% 40%, #03a9f4 39%, #03a9f4 42%);
}
 .tsspinner.newton .tsball::before {
 content: '';
 position: absolute;
 width: 0.08em;
 margin-left: -0.04em;
 top: 0;
 left: 50%;
 bottom: 1em;
 background: linear-gradient(transparent, #006DF0);
}
.tsspinner.newton .tsball {
	-webkit-animation: newtonBallMiddle 1s infinite alternate;
	animation: newtonBallMiddle 1s infinite alternate;
}
.tsspinner.newton .tsball:first-child {
	-webkit-animation: newtonBallFirst 1s ease-in infinite alternate;
	animation: newtonBallFirst 1s ease-in infinite alternate;
}
.tsspinner.newton .tsball:first-child::after {
 -webkit-animation: newtonBallLast 1s ease-out infinite alternate-reverse;
 animation: newtonBallLast 1s ease-out infinite alternate-reverse;
}
.tsspinner.newton .tsball:last-child {
	-webkit-animation: newtonBallLast 1s ease-out infinite alternate;
	animation: newtonBallLast 1s ease-out infinite alternate;
}
.tsspinner.newton .tsball:last-child::after {
 -webkit-animation: newtonBallFirst 1s ease-in infinite alternate-reverse;
 animation: newtonBallFirst 1s ease-in infinite alternate-reverse;
}






                              /*Preloader Demo 38*/

/******************************************************************************/
/******************************************************************************/








#ts-preloader-absolute39 {
	height: 200px;
	left: 68%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 69%;
	width: 200px;
}


@-webkit-keyframes clockHandRotate {
 from {
-webkit-transform: rotate(0deg) translateY(6%);
}
to {
	-webkit-transform: rotate(360deg) translateY(6%);
}
}
@keyframes clockHandRotate {
 from {
transform: rotate(0deg) translateY(6%);
}
to {
	transform: rotate(360deg) translateY(6%);
}
}
 @-webkit-keyframes bounce {
 0%, 20%, 50%, 80%, 100% {
 -webkit-transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
 -webkit-transform: translate3d(0, 0, 0);
}
 40% {
 -webkit-transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 -webkit-transform: translate3d(0, -2em, 0);
}
 70% {
 -webkit-transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 -webkit-transform: translate3d(0, -1em, 0);
}
 90% {
 -webkit-transform: translate3d(0, -0.25em, 0);
}
}
@keyframes bounce {
 0%, 20%, 50%, 80%, 100% {
 transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
 transform: translate3d(0, 0, 0);
}
 40% {
 transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 transform: translate3d(0, -2em, 0);
}
 70% {
 transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 transform: translate3d(0, -1em, 0);
}
 90% {
 transform: translate3d(0, -0.25em, 0);
}
}
 @-webkit-keyframes clockShadowFade {
 0%, 20%, 50%, 80%, 100% {
 -webkit-transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
 opacity: 1;
}
 40% {
 -webkit-transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 opacity: 0.2;
}
 70% {
 -webkit-transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 opacity: 0.4;
}
 90% {
 opacity: 0.8;
}
}
@keyframes clockShadowFade {
 0%, 20%, 50%, 80%, 100% {
 transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
 opacity: 1;
}
 40% {
 transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 opacity: 0.2;
}
 70% {
 transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
 opacity: 0.4;
}
 90% {
 opacity: 0.8;
}
}
.tsspinner.tsclock {
	width: 4em;
	height: 4em;
	position: relative;
}
.tsspinner.tsclock .tsdial {
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, white, rgb(210, 210, 210));
	border: 0.2em solid #006DF0;
	border-radius: 100%;
	box-sizing: border-box;
	-webkit-animation: bounce 1.5s infinite;
	animation: bounce 1.5s infinite;
}
.tsspinner.tsclock .tsdial .hand {
	position: absolute;
	bottom: 2em;
	width: 0.2em;
	left: 50%;
	margin-left: -0.1em;
	background-color: #006DF0;
	border-radius: 0 0 0.2em 0.2em;
	-webkit-transform-origin: 50% 100%;
	transform-origin: 50% 100%;
}
.tsspinner.tsclock .tsdial .hour.hand {
	height: 1em;
	-webkit-animation: clockHandRotate 12s linear infinite;
	animation: clockHandRotate 12s linear infinite;
}
.tsspinner.tsclock .tsdial .minute.hand {
	height: 1.5em;
	-webkit-animation: clockHandRotate 1s linear infinite;
	animation: clockHandRotate 1s linear infinite;
}
.tsspinner.tsclock .tsshadow {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 0.5em;
	margin-bottom: -0.25em;
	background: radial-gradient(#03a9f4, transparent 60%);
	-webkit-animation: clockShadowFade 1.5s linear infinite;
	animation: clockShadowFade 1.5s linear infinite;
}




                              /*Preloader Demo 39*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute40 {
	position: absolute;
	left: 67%;
	top: 74%;
	height: 200px;
	width: 200px;
	margin-top: -100px;
	margin-left: -100px;
}



@-webkit-keyframes sphereSpin {
 0% {
-webkit-transform: rotateX(360deg) rotateY(0deg);
}
 100% {
-webkit-transform: rotateX(0deg) rotateY(360deg);
}
}
@keyframes sphereSpin {
 0% {
transform: rotateX(360deg) rotateY(0deg);
}
 100% {
transform: rotateX(0deg) rotateY(360deg);
}
}
.tsspinner.sphere {
	width: 4em;
	height: 4em;
	-webkit-transform: perspective(20em) rotateX(-24deg) rotateY(20deg) rotateZ(30deg);
	transform: perspective(20em) rotateX(-24deg) rotateY(20deg) rotateZ(30deg);
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d;
}
.tsspinner .tsinner {
	width: 100%;
	height: 100%;
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d;
	-webkit-animation: sphereSpin 6s linear infinite;
	animation: sphereSpin 6s linear infinite;
}
.tsspinner.sphere .tsdisc {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 100%;
	border: 0.3em dotted #006DF0;
}
 @-webkit-keyframes rotateDisc2 {
 from {
-webkit-transform: rotateX(90deg) rotateZ(0deg);
}
to {
	-webkit-transform: rotateX(90deg) rotateZ(360deg);
}
}
@keyframes rotateDisc2 {
 from {
transform: rotateX(90deg) rotateZ(0deg);
}
to {
	transform: rotateX(90deg) rotateZ(360deg);
}
}
 @-webkit-keyframes rotateDisc3 {
 from {
-webkit-transform: rotateY(90deg) rotateZ(0deg);
}
to {
	-webkit-transform: rotateY(90deg) rotateZ(360deg);
}
}
@keyframes rotateDisc3 {
 from {
transform: rotateY(90deg) rotateZ(0deg);
}
to {
	transform: rotateY(90deg) rotateZ(360deg);
}
}
 .tsspinner.sphere .tsdisc:nth-child(1) {
 -webkit-animation: rotate 12s linear infinite;
 animation: rotate 12s linear infinite;
}
.tsspinner.sphere .tsdisc:nth-child(2) {
 -webkit-animation: rotateDisc2 12s linear infinite;
 animation: rotateDisc2 12s linear infinite;
}
.tsspinner.sphere .tsdisc:nth-child(3) {
 -webkit-animation: rotateDisc3 12s linear infinite;
 animation: rotateDisc3 12s linear infinite;
}





                              /*Preloader Demo 40*/

/******************************************************************************/
/******************************************************************************/









#ts-preloader-absolute41 {
	height: 200px;
	left: 67%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 72%;
	width: 200px;
}

.tsspinner.colorwheel {
	position: relative;
	display: inline-block;
	width: 4em;
	height: 4em;
	overflow: hidden;
	border-radius: 100%;
	z-index: 0;
}
 .tsspinner.colorwheel::before, .tsspinner.colorwheel::after {
 content: '';
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
}
 .tsspinner.colorwheel::before {
 background: linear-gradient(to right, green, yellow);
 -webkit-animation: rotate 2.5s linear infinite;
 animation: rotate 2.5s linear infinite;
}
 .tsspinner.colorwheel::after {
 background: linear-gradient(to bottom, red, blue);
 -webkit-animation: fade 2s infinite alternate, rotate 2.5s linear reverse infinite;
 animation: fade 2s infinite alternate, rotate 2.5s linear reverse infinite;
}
.tsspinner .centerpiece {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 1;
	border-radius: 100%;
	box-sizing: border-box;
	border-left: 0.5em solid transparent;
	border-right: 0.5em solid transparent;
	border-bottom: 0.5em solid #006DF0;
	border-top: 0.5em solid #006DF0;
	-webkit-animation: rotate 0.8s linear infinite;
	animation: rotate 0.8s linear infinite;
}




                              /*Preloader Demo 41*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute42 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 81%;
	width: 200px;
}


.tsspinner.infinity {
	-webkit-transform: perspective(10em) rotateZ(90deg) rotateY(30deg);
	transform: perspective(10em) rotateZ(90deg) rotateY(30deg);
}
.tsspinner.infinity .tshalf {
	position: relative;
	width: 4em;
	height: 4em;
}
.tsspinner.infinity .tsmarker {
	position: absolute;
	width: 100%;
	height: 100%;
}
 @-webkit-keyframes rotateHide {
 0% {
-webkit-transform: rotate(0deg);
opacity: 0;
}
 25% {
opacity: 1;
}
 50%, 100% {
-webkit-transform: rotate(360deg);
opacity: 0;
}
}
@keyframes rotateHide {
 0% {
transform: rotate(0deg);
opacity: 0;
}
 25% {
opacity: 1;
}
 50%, 100% {
transform: rotate(360deg);
opacity: 0;
}
}
 .tsspinner.infinity .tsmarker::after {
 opacity: 0;
 content: '\2022';
 width: 100%;
 height: 100%;
 display: block;
 -webkit-animation: rotateHide 3.5s cubic-bezier(0.4, 0.1, 0.6, 0.9) infinite;
 animation: rotateHide 3.5s cubic-bezier(0.4, 0.1, 0.6, 0.9) infinite;
}
.tsspinner.infinity .tshalf:first-child {
	-webkit-transform: translateY(1em) rotateX(180deg);
	transform: translateY(1em) rotateX(180deg);
}
 .tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(1)::after {
-webkit-animation-delay: 0s;
animation-delay: 0s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(2)::after {
-webkit-animation-delay: 0.25s;
animation-delay: 0.25s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(3)::after {
-webkit-animation-delay: 0.5s;
animation-delay: 0.5s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(4)::after {
-webkit-animation-delay: 0.75s;
animation-delay: 0.75s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(5)::after {
-webkit-animation-delay: 1s;
animation-delay: 1s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(6)::after {
-webkit-animation-delay: 1.25s;
animation-delay: 1.25s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(7)::after {
-webkit-animation-delay: 1.5s;
animation-delay: 1.5s;
}
.tsspinner.infinity .tshalf:first-child .tsmarker:nth-child(8)::after {
-webkit-animation-delay: 1.75s;
animation-delay: 1.75s;
}
 .tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(1)::after {
-webkit-animation-delay: 1.75s;
animation-delay: 1.75s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(2)::after {
-webkit-animation-delay: 2s;
animation-delay: 2s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(3)::after {
-webkit-animation-delay: 2.25s;
animation-delay: 2.25s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(4)::after {
-webkit-animation-delay: 2.5s;
animation-delay: 2.5s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(5)::after {
-webkit-animation-delay: 2.75s;
animation-delay: 2.75s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(6)::after {
-webkit-animation-delay: 3s;
animation-delay: 3s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(7)::after {
-webkit-animation-delay: 3.25s;
animation-delay: 3.25s;
}
.tsspinner.infinity .tshalf:last-child .tsmarker:nth-child(8)::after {
-webkit-animation-delay: 3.5s;
animation-delay: 3.5s;
}



                              /*Preloader Demo 42*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute43 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 81%;
	width: 200px;
}
.tscssload-wrap {
	position: absolute;
	margin: 0 auto 0;
	left: 50%;
	margin-left: -218px;
	transform: rotateX(75deg);
}
.tscssload-circle {
	position: absolute;
	float: left;
	border: 1px solid white;
	animation: bounce 1.73s infinite ease-in-out alternate;
	-o-animation: bounce 1.73s infinite ease-in-out alternate;
	-ms-animation: bounce 1.73s infinite ease-in-out alternate;
	-webkit-animation: bounce 1.73s infinite ease-in-out alternate;
	-moz-animation: bounce 1.73s infinite ease-in-out alternate;
	border-radius: 100%;
	background: transparent;
	top: -73px;
	left: -73px;
}
.tscssload-circle:nth-child(1) {
 margin: 0 288px;
 width: 10px;
 height: 10px;
 animation-delay: 115ms;
 -o-animation-delay: 115ms;
 -ms-animation-delay: 115ms;
 -webkit-animation-delay: 115ms;
 -moz-animation-delay: 115ms;
 z-index: -1;
 border: 1px solid rgba(255, 43, 0, 0.7);
}
.tscssload-circle:nth-child(2) {
 margin: 0 283px;
 width: 19px;
 height: 19px;
 animation-delay: 230ms;
 -o-animation-delay: 230ms;
 -ms-animation-delay: 230ms;
 -webkit-animation-delay: 230ms;
 -moz-animation-delay: 230ms;
 z-index: -2;
 border: 1px solid rgba(255, 85, 0, 0.7);
}
.tscssload-circle:nth-child(3) {
 margin: 0 278px;
 width: 29px;
 height: 29px;
 animation-delay: 345ms;
 -o-animation-delay: 345ms;
 -ms-animation-delay: 345ms;
 -webkit-animation-delay: 345ms;
 -moz-animation-delay: 345ms;
 z-index: -3;
 border: 1px solid rgba(255, 128, 0, 0.7);
}
.tscssload-circle:nth-child(4) {
 margin: 0 273px;
 width: 39px;
 height: 39px;
 animation-delay: 460ms;
 -o-animation-delay: 460ms;
 -ms-animation-delay: 460ms;
 -webkit-animation-delay: 460ms;
 -moz-animation-delay: 460ms;
 z-index: -4;
 border: 1px solid rgba(255, 170, 0, 0.7);
}
.tscssload-circle:nth-child(5) {
 margin: 0 268px;
 width: 49px;
 height: 49px;
 animation-delay: 575ms;
 -o-animation-delay: 575ms;
 -ms-animation-delay: 575ms;
 -webkit-animation-delay: 575ms;
 -moz-animation-delay: 575ms;
 z-index: -5;
 border: 1px solid rgba(255, 213, 0, 0.7);
}
.tscssload-circle:nth-child(6) {
 margin: 0 263px;
 width: 58px;
 height: 58px;
 animation-delay: 690ms;
 -o-animation-delay: 690ms;
 -ms-animation-delay: 690ms;
 -webkit-animation-delay: 690ms;
 -moz-animation-delay: 690ms;
 z-index: -6;
 border: 1px solid rgba(255, 255, 0, 0.7);
}
.tscssload-circle:nth-child(7) {
 margin: 0 258px;
 width: 68px;
 height: 68px;
 animation-delay: 805ms;
 -o-animation-delay: 805ms;
 -ms-animation-delay: 805ms;
 -webkit-animation-delay: 805ms;
 -moz-animation-delay: 805ms;
 z-index: -7;
 border: 1px solid rgba(212, 255, 0, 0.7);
}
.tscssload-circle:nth-child(8) {
 margin: 0 253px;
 width: 78px;
 height: 78px;
 animation-delay: 920ms;
 -o-animation-delay: 920ms;
 -ms-animation-delay: 920ms;
 -webkit-animation-delay: 920ms;
 -moz-animation-delay: 920ms;
 z-index: -8;
 border: 1px solid rgba(170, 255, 0, 0.7);
}
.tscssload-circle:nth-child(9) {
 margin: 0 249px;
 width: 88px;
 height: 88px;
 animation-delay: 1035ms;
 -o-animation-delay: 1035ms;
 -ms-animation-delay: 1035ms;
 -webkit-animation-delay: 1035ms;
 -moz-animation-delay: 1035ms;
 z-index: -9;
 border: 1px solid rgba(128, 255, 0, 0.7);
}
.tscssload-circle:nth-child(10) {
 margin: 0 244px;
 width: 97px;
 height: 97px;
 animation-delay: 1150ms;
 -o-animation-delay: 1150ms;
 -ms-animation-delay: 1150ms;
 -webkit-animation-delay: 1150ms;
 -moz-animation-delay: 1150ms;
 z-index: -10;
 border: 1px solid rgba(85, 255, 0, 0.7);
}
.tscssload-circle:nth-child(11) {
 margin: 0 239px;
 width: 107px;
 height: 107px;
 animation-delay: 1265ms;
 -o-animation-delay: 1265ms;
 -ms-animation-delay: 1265ms;
 -webkit-animation-delay: 1265ms;
 -moz-animation-delay: 1265ms;
 z-index: -11;
 border: 1px solid rgba(43, 255, 0, 0.7);
}
.tscssload-circle:nth-child(12) {
 margin: 0 234px;
 width: 117px;
 height: 117px;
 animation-delay: 1380ms;
 -o-animation-delay: 1380ms;
 -ms-animation-delay: 1380ms;
 -webkit-animation-delay: 1380ms;
 -moz-animation-delay: 1380ms;
 z-index: -12;
 border: 1px solid rgba(0, 255, 0, 0.7);
}
.tscssload-circle:nth-child(13) {
 margin: 0 229px;
 width: 127px;
 height: 127px;
 animation-delay: 1495ms;
 -o-animation-delay: 1495ms;
 -ms-animation-delay: 1495ms;
 -webkit-animation-delay: 1495ms;
 -moz-animation-delay: 1495ms;
 z-index: -13;
 border: 1px solid rgba(0, 255, 43, 0.7);
}
.tscssload-circle:nth-child(14) {
 margin: 0 224px;
 width: 136px;
 height: 136px;
 animation-delay: 1610ms;
 -o-animation-delay: 1610ms;
 -ms-animation-delay: 1610ms;
 -webkit-animation-delay: 1610ms;
 -moz-animation-delay: 1610ms;
 z-index: -14;
 border: 1px solid rgba(0, 255, 85, 0.7);
}
.tscssload-circle:nth-child(15) {
 margin: 0 219px;
 width: 146px;
 height: 146px;
 animation-delay: 1725ms;
 -o-animation-delay: 1725ms;
 -ms-animation-delay: 1725ms;
 -webkit-animation-delay: 1725ms;
 -moz-animation-delay: 1725ms;
 z-index: -15;
 border: 1px solid rgba(0, 255, 128, 0.7);
}
.tscssload-circle:nth-child(16) {
 margin: 0 214px;
 width: 156px;
 height: 156px;
 animation-delay: 1840ms;
 -o-animation-delay: 1840ms;
 -ms-animation-delay: 1840ms;
 -webkit-animation-delay: 1840ms;
 -moz-animation-delay: 1840ms;
 z-index: -16;
 border: 1px solid rgba(0, 255, 170, 0.7);
}
.tscssload-circle:nth-child(17) {
 margin: 0 210px;
 width: 166px;
 height: 166px;
 animation-delay: 1955ms;
 -o-animation-delay: 1955ms;
 -ms-animation-delay: 1955ms;
 -webkit-animation-delay: 1955ms;
 -moz-animation-delay: 1955ms;
 z-index: -17;
 border: 1px solid rgba(0, 255, 213, 0.7);
}
.tscssload-circle:nth-child(18) {
 margin: 0 205px;
 width: 175px;
 height: 175px;
 animation-delay: 2070ms;
 -o-animation-delay: 2070ms;
 -ms-animation-delay: 2070ms;
 -webkit-animation-delay: 2070ms;
 -moz-animation-delay: 2070ms;
 z-index: -18;
 border: 1px solid rgba(0, 255, 255, 0.7);
}
.tscssload-circle:nth-child(19) {
 margin: 0 200px;
 width: 185px;
 height: 185px;
 animation-delay: 2185ms;
 -o-animation-delay: 2185ms;
 -ms-animation-delay: 2185ms;
 -webkit-animation-delay: 2185ms;
 -moz-animation-delay: 2185ms;
 z-index: -19;
 border: 1px solid rgba(0, 212, 255, 0.7);
}
.tscssload-circle:nth-child(20) {
 margin: 0 195px;
 width: 195px;
 height: 195px;
 animation-delay: 2300ms;
 -o-animation-delay: 2300ms;
 -ms-animation-delay: 2300ms;
 -webkit-animation-delay: 2300ms;
 -moz-animation-delay: 2300ms;
 z-index: -20;
 border: 1px solid rgba(0, 170, 255, 0.7);
}
.tscssload-circle:nth-child(21) {
 margin: 0 190px;
 width: 205px;
 height: 205px;
 animation-delay: 2415ms;
 -o-animation-delay: 2415ms;
 -ms-animation-delay: 2415ms;
 -webkit-animation-delay: 2415ms;
 -moz-animation-delay: 2415ms;
 z-index: -21;
 border: 1px solid rgba(0, 127, 255, 0.7);
}
.tscssload-circle:nth-child(22) {
 margin: 0 185px;
 width: 214px;
 height: 214px;
 animation-delay: 2530ms;
 -o-animation-delay: 2530ms;
 -ms-animation-delay: 2530ms;
 -webkit-animation-delay: 2530ms;
 -moz-animation-delay: 2530ms;
 z-index: -22;
 border: 1px solid rgba(0, 85, 255, 0.7);
}
.tscssload-circle:nth-child(23) {
 margin: 0 180px;
 width: 224px;
 height: 224px;
 animation-delay: 2645ms;
 -o-animation-delay: 2645ms;
 -ms-animation-delay: 2645ms;
 -webkit-animation-delay: 2645ms;
 -moz-animation-delay: 2645ms;
 z-index: -23;
 border: 1px solid rgba(0, 43, 255, 0.7);
}
.tscssload-circle:nth-child(24) {
 margin: 0 175px;
 width: 234px;
 height: 234px;
 animation-delay: 2760ms;
 -o-animation-delay: 2760ms;
 -ms-animation-delay: 2760ms;
 -webkit-animation-delay: 2760ms;
 -moz-animation-delay: 2760ms;
 z-index: -24;
 border: 1px solid rgba(0, 0, 255, 0.7);
}
.tscssload-circle:nth-child(25) {
 margin: 0 171px;
 width: 244px;
 height: 244px;
 animation-delay: 2875ms;
 -o-animation-delay: 2875ms;
 -ms-animation-delay: 2875ms;
 -webkit-animation-delay: 2875ms;
 -moz-animation-delay: 2875ms;
 z-index: -25;
 border: 1px solid rgba(42, 0, 255, 0.7);
}
.tscssload-circle:nth-child(26) {
 margin: 0 166px;
 width: 253px;
 height: 253px;
 animation-delay: 2990ms;
 -o-animation-delay: 2990ms;
 -ms-animation-delay: 2990ms;
 -webkit-animation-delay: 2990ms;
 -moz-animation-delay: 2990ms;
 z-index: -26;
 border: 1px solid rgba(85, 0, 255, 0.7);
}
.tscssload-circle:nth-child(27) {
 margin: 0 161px;
 width: 263px;
 height: 263px;
 animation-delay: 3105ms;
 -o-animation-delay: 3105ms;
 -ms-animation-delay: 3105ms;
 -webkit-animation-delay: 3105ms;
 -moz-animation-delay: 3105ms;
 z-index: -27;
 border: 1px solid rgba(127, 0, 255, 0.7);
}
.tscssload-circle:nth-child(28) {
 margin: 0 156px;
 width: 273px;
 height: 273px;
 animation-delay: 3220ms;
 -o-animation-delay: 3220ms;
 -ms-animation-delay: 3220ms;
 -webkit-animation-delay: 3220ms;
 -moz-animation-delay: 3220ms;
 z-index: -28;
 border: 1px solid rgba(170, 0, 255, 0.7);
}
.tscssload-circle:nth-child(29) {
 margin: 0 151px;
 width: 283px;
 height: 283px;
 animation-delay: 3335ms;
 -o-animation-delay: 3335ms;
 -ms-animation-delay: 3335ms;
 -webkit-animation-delay: 3335ms;
 -moz-animation-delay: 3335ms;
 z-index: -29;
 border: 1px solid rgba(212, 0, 255, 0.7);
}
.tscssload-circle:nth-child(30) {
 margin: 0 146px;
 width: 292px;
 height: 292px;
 animation-delay: 3450ms;
 -o-animation-delay: 3450ms;
 -ms-animation-delay: 3450ms;
 -webkit-animation-delay: 3450ms;
 -moz-animation-delay: 3450ms;
 z-index: -30;
 border: 1px solid rgba(255, 0, 255, 0.7);
}
 @keyframes bounce {
 0% {
 transform: translateY(0px);
}
 100% {
 transform: translateY(97px);
}
}
 @-o-keyframes bounce {
 0% {
 -o-transform: translateY(0px);
}
 100% {
 -o-transform: translateY(97px);
}
}
 @-ms-keyframes bounce {
 0% {
 -ms-transform: translateY(0px);
}
 100% {
 -ms-transform: translateY(97px);
}
}
 @-webkit-keyframes bounce {
 0% {
 -webkit-transform: translateY(0px);
}
 100% {
 -webkit-transform: translateY(97px);
}
}
 @-moz-keyframes bounce {
 0% {
 -moz-transform: translateY(0px);
}
 100% {
 -moz-transform: translateY(97px);
}
}





                              /*Preloader Demo 43*/

/******************************************************************************/
/******************************************************************************/






#ts-preloader-absolute44 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
#circularG {
	position:relative;
	width:58px;
	height:58px;
	margin: auto;
}
.circularG {
	position:absolute;
	background-color:#03a9f4;
	width:14px;
	height:14px;
	border-radius:9px;
	-o-border-radius:9px;
	-ms-border-radius:9px;
	-webkit-border-radius:9px;
	-moz-border-radius:9px;
	animation-name:bounce_circularG;
	-o-animation-name:bounce_circularG;
	-ms-animation-name:bounce_circularG;
	-webkit-animation-name:bounce_circularG;
	-moz-animation-name:bounce_circularG;
	animation-duration:1.1s;
	-o-animation-duration:1.1s;
	-ms-animation-duration:1.1s;
	-webkit-animation-duration:1.1s;
	-moz-animation-duration:1.1s;
	animation-iteration-count:infinite;
	-o-animation-iteration-count:infinite;
	-ms-animation-iteration-count:infinite;
	-webkit-animation-iteration-count:infinite;
	-moz-animation-iteration-count:infinite;
	animation-direction:normal;
	-o-animation-direction:normal;
	-ms-animation-direction:normal;
	-webkit-animation-direction:normal;
	-moz-animation-direction:normal;
}
#circularG_1 {
	left:0;
	top:23px;
	animation-delay:0.41s;
	-o-animation-delay:0.41s;
	-ms-animation-delay:0.41s;
	-webkit-animation-delay:0.41s;
	-moz-animation-delay:0.41s;
}
#circularG_2 {
	left:6px;
	top:6px;
	animation-delay:0.55s;
	-o-animation-delay:0.55s;
	-ms-animation-delay:0.55s;
	-webkit-animation-delay:0.55s;
	-moz-animation-delay:0.55s;
}
#circularG_3 {
	top:0;
	left:23px;
	animation-delay:0.69s;
	-o-animation-delay:0.69s;
	-ms-animation-delay:0.69s;
	-webkit-animation-delay:0.69s;
	-moz-animation-delay:0.69s;
}
#circularG_4 {
	right:6px;
	top:6px;
	animation-delay:0.83s;
	-o-animation-delay:0.83s;
	-ms-animation-delay:0.83s;
	-webkit-animation-delay:0.83s;
	-moz-animation-delay:0.83s;
}
#circularG_5 {
	right:0;
	top:23px;
	animation-delay:0.97s;
	-o-animation-delay:0.97s;
	-ms-animation-delay:0.97s;
	-webkit-animation-delay:0.97s;
	-moz-animation-delay:0.97s;
}
#circularG_6 {
	right:6px;
	bottom:6px;
	animation-delay:1.1s;
	-o-animation-delay:1.1s;
	-ms-animation-delay:1.1s;
	-webkit-animation-delay:1.1s;
	-moz-animation-delay:1.1s;
}
#circularG_7 {
	left:23px;
	bottom:0;
	animation-delay:1.24s;
	-o-animation-delay:1.24s;
	-ms-animation-delay:1.24s;
	-webkit-animation-delay:1.24s;
	-moz-animation-delay:1.24s;
}
#circularG_8 {
	left:6px;
	bottom:6px;
	animation-delay:1.38s;
	-o-animation-delay:1.38s;
	-ms-animation-delay:1.38s;
	-webkit-animation-delay:1.38s;
	-moz-animation-delay:1.38s;
}
 @keyframes bounce_circularG {
 0% {
 transform:scale(1);
}
 100% {
 transform:scale(.3);
}
}
 @-o-keyframes bounce_circularG {
 0% {
 -o-transform:scale(1);
}
 100% {
 -o-transform:scale(.3);
}
}
 @-ms-keyframes bounce_circularG {
 0% {
 -ms-transform:scale(1);
}
 100% {
 -ms-transform:scale(.3);
}
}
 @-webkit-keyframes bounce_circularG {
 0% {
 -webkit-transform:scale(1);
}
 100% {
 -webkit-transform:scale(.3);
}
}
 @-moz-keyframes bounce_circularG {
 0% {
 -moz-transform:scale(1);
}
 100% {
 -moz-transform:scale(.3);
}
}








                              /*Preloader Demo 44*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute45 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
#fountainTextG {
	width:234px;
	margin:auto;
}
.fountainTextG {
	color:#03a9f4;
	font-family:Arial;
	font-size:24px;
	text-decoration:none;
	font-weight:normal;
	font-style:normal;
	float:left;
	animation-name:bounce_fountainTextG;
	-o-animation-name:bounce_fountainTextG;
	-ms-animation-name:bounce_fountainTextG;
	-webkit-animation-name:bounce_fountainTextG;
	-moz-animation-name:bounce_fountainTextG;
	animation-duration:2.09s;
	-o-animation-duration:2.09s;
	-ms-animation-duration:2.09s;
	-webkit-animation-duration:2.09s;
	-moz-animation-duration:2.09s;
	animation-iteration-count:infinite;
	-o-animation-iteration-count:infinite;
	-ms-animation-iteration-count:infinite;
	-webkit-animation-iteration-count:infinite;
	-moz-animation-iteration-count:infinite;
	animation-direction:normal;
	-o-animation-direction:normal;
	-ms-animation-direction:normal;
	-webkit-animation-direction:normal;
	-moz-animation-direction:normal;
	transform:scale(.5);
	-o-transform:scale(.5);
	-ms-transform:scale(.5);
	-webkit-transform:scale(.5);
	-moz-transform:scale(.5);
}
#fountainTextG_1 {
	animation-delay:0.75s;
	-o-animation-delay:0.75s;
	-ms-animation-delay:0.75s;
	-webkit-animation-delay:0.75s;
	-moz-animation-delay:0.75s;
}
#fountainTextG_2 {
	animation-delay:0.9s;
	-o-animation-delay:0.9s;
	-ms-animation-delay:0.9s;
	-webkit-animation-delay:0.9s;
	-moz-animation-delay:0.9s;
}
#fountainTextG_3 {
	animation-delay:1.05s;
	-o-animation-delay:1.05s;
	-ms-animation-delay:1.05s;
	-webkit-animation-delay:1.05s;
	-moz-animation-delay:1.05s;
}
#fountainTextG_4 {
	animation-delay:1.2s;
	-o-animation-delay:1.2s;
	-ms-animation-delay:1.2s;
	-webkit-animation-delay:1.2s;
	-moz-animation-delay:1.2s;
}
#fountainTextG_5 {
	animation-delay:1.35s;
	-o-animation-delay:1.35s;
	-ms-animation-delay:1.35s;
	-webkit-animation-delay:1.35s;
	-moz-animation-delay:1.35s;
}
#fountainTextG_6 {
	animation-delay:1.5s;
	-o-animation-delay:1.5s;
	-ms-animation-delay:1.5s;
	-webkit-animation-delay:1.5s;
	-moz-animation-delay:1.5s;
}
#fountainTextG_7 {
	animation-delay:1.64s;
	-o-animation-delay:1.64s;
	-ms-animation-delay:1.64s;
	-webkit-animation-delay:1.64s;
	-moz-animation-delay:1.64s;
}
#fountainTextG_8 {
	animation-delay:1.79s;
	-o-animation-delay:1.79s;
	-ms-animation-delay:1.79s;
	-webkit-animation-delay:1.79s;
	-moz-animation-delay:1.79s;
}
#fountainTextG_9 {
	animation-delay:1.94s;
	-o-animation-delay:1.94s;
	-ms-animation-delay:1.94s;
	-webkit-animation-delay:1.94s;
	-moz-animation-delay:1.94s;
}
#fountainTextG_10 {
	animation-delay:2.09s;
	-o-animation-delay:2.09s;
	-ms-animation-delay:2.09s;
	-webkit-animation-delay:2.09s;
	-moz-animation-delay:2.09s;
}
 @keyframes bounce_fountainTextG {
 0% {
 transform:scale(1);
 color:#03a9f4;
}
 100% {
 transform:scale(.5);
 color:#03a9f4;
}
}
 @-o-keyframes bounce_fountainTextG {
 0% {
 -o-transform:scale(1);
 color:rgb(0,0,0);
}
 100% {
 -o-transform:scale(.5);
 color:#03a9f4;
}
}
 @-ms-keyframes bounce_fountainTextG {
 0% {
 -ms-transform:scale(1);
 color:#03a9f4;
}
 100% {
 -ms-transform:scale(.5);
 color:#03a9f4;
}
}
 @-webkit-keyframes bounce_fountainTextG {
 0% {
 -webkit-transform:scale(1);
 color:rgb(0,0,0);
}
 100% {
 -webkit-transform:scale(.5);
 color:rgb(255,255,255);
}
}
 @-moz-keyframes bounce_fountainTextG {
 0% {
 -moz-transform:scale(1);
 color:rgb(0,0,0);
}
 100% {
 -moz-transform:scale(.5);
 color:rgb(255,255,255);
}
}




                              /*Preloader Demo 45*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute46 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
.bubblingG {
	text-align: center;
	width:78px;
	height:49px;
	margin: auto;
}
.bubblingG span {
	display: inline-block;
	vertical-align: middle;
	width: 10px;
	height: 10px;
	margin: 24px auto;
	background: #03a9f4;
	border-radius: 49px;
	-o-border-radius: 49px;
	-ms-border-radius: 49px;
	-webkit-border-radius: 49px;
	-moz-border-radius: 49px;
	animation: bubblingG 1.5s infinite alternate;
	-o-animation: bubblingG 1.5s infinite alternate;
	-ms-animation: bubblingG 1.5s infinite alternate;
	-webkit-animation: bubblingG 1.5s infinite alternate;
	-moz-animation: bubblingG 1.5s infinite alternate;
}
#bubblingG_1 {
	animation-delay: 0s;
	-o-animation-delay: 0s;
	-ms-animation-delay: 0s;
	-webkit-animation-delay: 0s;
	-moz-animation-delay: 0s;
}
#bubblingG_2 {
	animation-delay: 0.45s;
	-o-animation-delay: 0.45s;
	-ms-animation-delay: 0.45s;
	-webkit-animation-delay: 0.45s;
	-moz-animation-delay: 0.45s;
}
#bubblingG_3 {
	animation-delay: 0.9s;
	-o-animation-delay: 0.9s;
	-ms-animation-delay: 0.9s;
	-webkit-animation-delay: 0.9s;
	-moz-animation-delay: 0.9s;
}
 @keyframes bubblingG {
 0% {
 width: 10px;
 height: 10px;
 background-color:#03a9f4;
 transform: translateY(0);
}
 100% {
 width: 23px;
 height: 23px;
 background-color:#03a9f4;
 transform: translateY(-20px);
}
}
 @-o-keyframes bubblingG {
 0% {
 width: 10px;
 height: 10px;
 background-color:rgb(0,0,0);
 -o-transform: translateY(0);
}
 100% {
 width: 23px;
 height: 23px;
 background-color:rgb(255,255,255);
 -o-transform: translateY(-20px);
}
}
 @-ms-keyframes bubblingG {
 0% {
 width: 10px;
 height: 10px;
 background-color:rgb(0,0,0);
 -ms-transform: translateY(0);
}
 100% {
 width: 23px;
 height: 23px;
 background-color:rgb(255,255,255);
 -ms-transform: translateY(-20px);
}
}
 @-webkit-keyframes bubblingG {
 0% {
 width: 10px;
 height: 10px;
 background-color:rgb(0,0,0);
 -webkit-transform: translateY(0);
}
 100% {
 width: 23px;
 height: 23px;
 background-color:rgb(255,255,255);
 -webkit-transform: translateY(-20px);
}
}
 @-moz-keyframes bubblingG {
 0% {
 width: 10px;
 height: 10px;
 background-color:rgb(0,0,0);
 -moz-transform: translateY(0);
}
 100% {
 width: 23px;
 height: 23px;
 background-color:rgb(255,255,255);
 -moz-transform: translateY(-20px);
}
}



                              /*Preloader Demo 46*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute47 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
#fountainG {
	position:relative;
	width:234px;
	height:28px;
	margin:auto;
}
.fountainG {
	position:absolute;
	top:0;
	background-color:#03a9f4;
	width:28px;
	height:28px;
	animation-name:bounce_fountainG;
	-o-animation-name:bounce_fountainG;
	-ms-animation-name:bounce_fountainG;
	-webkit-animation-name:bounce_fountainG;
	-moz-animation-name:bounce_fountainG;
	animation-duration:1.5s;
	-o-animation-duration:1.5s;
	-ms-animation-duration:1.5s;
	-webkit-animation-duration:1.5s;
	-moz-animation-duration:1.5s;
	animation-iteration-count:infinite;
	-o-animation-iteration-count:infinite;
	-ms-animation-iteration-count:infinite;
	-webkit-animation-iteration-count:infinite;
	-moz-animation-iteration-count:infinite;
	animation-direction:normal;
	-o-animation-direction:normal;
	-ms-animation-direction:normal;
	-webkit-animation-direction:normal;
	-moz-animation-direction:normal;
	transform:scale(.3);
	-o-transform:scale(.3);
	-ms-transform:scale(.3);
	-webkit-transform:scale(.3);
	-moz-transform:scale(.3);
	border-radius:19px;
	-o-border-radius:19px;
	-ms-border-radius:19px;
	-webkit-border-radius:19px;
	-moz-border-radius:19px;
}
#fountainG_1 {
	left:0;
	animation-delay:0.6s;
	-o-animation-delay:0.6s;
	-ms-animation-delay:0.6s;
	-webkit-animation-delay:0.6s;
	-moz-animation-delay:0.6s;
}
#fountainG_2 {
	left:29px;
	animation-delay:0.75s;
	-o-animation-delay:0.75s;
	-ms-animation-delay:0.75s;
	-webkit-animation-delay:0.75s;
	-moz-animation-delay:0.75s;
}
#fountainG_3 {
	left:58px;
	animation-delay:0.9s;
	-o-animation-delay:0.9s;
	-ms-animation-delay:0.9s;
	-webkit-animation-delay:0.9s;
	-moz-animation-delay:0.9s;
}
#fountainG_4 {
	left:88px;
	animation-delay:1.05s;
	-o-animation-delay:1.05s;
	-ms-animation-delay:1.05s;
	-webkit-animation-delay:1.05s;
	-moz-animation-delay:1.05s;
}
#fountainG_5 {
	left:117px;
	animation-delay:1.2s;
	-o-animation-delay:1.2s;
	-ms-animation-delay:1.2s;
	-webkit-animation-delay:1.2s;
	-moz-animation-delay:1.2s;
}
#fountainG_6 {
	left:146px;
	animation-delay:1.35s;
	-o-animation-delay:1.35s;
	-ms-animation-delay:1.35s;
	-webkit-animation-delay:1.35s;
	-moz-animation-delay:1.35s;
}
#fountainG_7 {
	left:175px;
	animation-delay:1.5s;
	-o-animation-delay:1.5s;
	-ms-animation-delay:1.5s;
	-webkit-animation-delay:1.5s;
	-moz-animation-delay:1.5s;
}
#fountainG_8 {
	left:205px;
	animation-delay:1.64s;
	-o-animation-delay:1.64s;
	-ms-animation-delay:1.64s;
	-webkit-animation-delay:1.64s;
	-moz-animation-delay:1.64s;
}
 @keyframes bounce_fountainG {
 0% {
 transform:scale(1);
 background-color:#03a9f4;
}
 100% {
 transform:scale(.3);
 background-color:#03a9f4;
}
}
 @-o-keyframes bounce_fountainG {
 0% {
 -o-transform:scale(1);
 background-color:rgb(0,0,0);
}
 100% {
 -o-transform:scale(.3);
 background-color:rgb(255,255,255);
}
}
 @-ms-keyframes bounce_fountainG {
 0% {
 -ms-transform:scale(1);
 background-color:rgb(0,0,0);
}
 100% {
 -ms-transform:scale(.3);
 background-color:rgb(255,255,255);
}
}
 @-webkit-keyframes bounce_fountainG {
 0% {
 -webkit-transform:scale(1);
 background-color:rgb(0,0,0);
}
 100% {
 -webkit-transform:scale(.3);
 background-color:rgb(255,255,255);
}
}
 @-moz-keyframes bounce_fountainG {
 0% {
 -moz-transform:scale(1);
 background-color:rgb(0,0,0);
}
 100% {
 -moz-transform:scale(.3);
 background-color:rgb(255,255,255);
}
}



                              /*Preloader Demo 47*/

/******************************************************************************/
/******************************************************************************/





#ts-preloader-absolute48 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
.tscssload-main {
	position: absolute;
	content: '';
	left: 50%;
	transform: translate(-100%, -240%);
	-o-transform: translate(-100%, -240%);
	-ms-transform: translate(-100%, -240%);
	-webkit-transform: translate(-100%, -240%);
	-moz-transform: translate(-100%, -240%);
}
.tscssload-main * {
	font-size:62px;
}
.tscssload-heart {
	animation: cssload-heart 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-o-animation: cssload-heart 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-ms-animation: cssload-heart 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-webkit-animation: cssload-heart 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-moz-animation: cssload-heart 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	top: 50%;
	content: '';
	left: 50%;
	position: absolute;
}
.tscssload-heartL {
	width: 1em;
	height: 1em;
	border: 1px solid rgb(63,193,242);
	background-color: rgb(63,193,242);
	content: '';
	position: absolute;
	display: block;
	border-radius: 100%;
	animation: cssload-heartL 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-o-animation: cssload-heartL 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-ms-animation: cssload-heartL 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-webkit-animation: cssload-heartL 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-moz-animation: cssload-heartL 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	transform: translate(-28px, -27px);
	-o-transform: translate(-28px, -27px);
	-ms-transform: translate(-28px, -27px);
	-webkit-transform: translate(-28px, -27px);
	-moz-transform: translate(-28px, -27px);
}
.tscssload-heartR {
	width: 1em;
	height: 1em;
	border: 1px solid rgb(63,193,242);
	background-color: rgb(63,193,242);
	content: '';
	position: absolute;
	display: block;
	border-radius: 100%;
	transform: translate(28px, -27px);
	-o-transform: translate(28px, -27px);
	-ms-transform: translate(28px, -27px);
	-webkit-transform: translate(28px, -27px);
	-moz-transform: translate(28px, -27px);
	animation: cssload-heartR 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-o-animation: cssload-heartR 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-ms-animation: cssload-heartR 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-webkit-animation: cssload-heartR 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-moz-animation: cssload-heartR 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
}
.tscssload-square {
	width: 1em;
	height: 1em;
	border: 1px solid rgb(63,193,242);
	background-color: rgb(63,193,242);
	position: relative;
	display: block;
	content: '';
	transform: scale(1) rotate(-45deg);
	-o-transform: scale(1) rotate(-45deg);
	-ms-transform: scale(1) rotate(-45deg);
	-webkit-transform: scale(1) rotate(-45deg);
	-moz-transform: scale(1) rotate(-45deg);
	animation: cssload-square 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-o-animation: cssload-square 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-ms-animation: cssload-square 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-webkit-animation: cssload-square 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-moz-animation: cssload-square 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
}
.tscssload-shadow {
	top: 97px;
	left: 50%;
	content: '';
	position: relative;
	display: block;
	bottom: -.5em;
	width: 1em;
	height: .24em;
	background-color: rgb(215,215,215);
	border: 1px solid rgb(215,215,215);
	border-radius: 50%;
	animation: cssload-shadow 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-o-animation: cssload-shadow 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-ms-animation: cssload-shadow 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-webkit-animation: cssload-shadow 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
	-moz-animation: cssload-shadow 2.88s cubic-bezier(0.75, 0, 0.5, 1) infinite normal;
}
 @keyframes cssload-square {
 50% {
 border-radius: 100%;
 transform: scale(0.5) rotate(-45deg);
}
 100% {
 transform: scale(1) rotate(-45deg);
}
}
 @-o-keyframes cssload-square {
 50% {
 border-radius: 100%;
 -o-transform: scale(0.5) rotate(-45deg);
}
 100% {
 -o-transform: scale(1) rotate(-45deg);
}
}
 @-ms-keyframes cssload-square {
 50% {
 border-radius: 100%;
 -ms-transform: scale(0.5) rotate(-45deg);
}
 100% {
 -ms-transform: scale(1) rotate(-45deg);
}
}
 @-webkit-keyframes cssload-square {
 50% {
 border-radius: 100%;
 -webkit-transform: scale(0.5) rotate(-45deg);
}
 100% {
 -webkit-transform: scale(1) rotate(-45deg);
}
}
 @-moz-keyframes cssload-square {
 50% {
 border-radius: 100%;
 -moz-transform: scale(0.5) rotate(-45deg);
}
 100% {
 -moz-transform: scale(1) rotate(-45deg);
}
}
 @keyframes cssload-heart {
 50% {
 transform: rotate(360deg);
}
 100% {
 transform: rotate(720deg);
}
}
 @-o-keyframes cssload-heart {
 50% {
 -o-transform: rotate(360deg);
}
 100% {
 -o-transform: rotate(720deg);
}
}
 @-ms-keyframes cssload-heart {
 50% {
 -ms-transform: rotate(360deg);
}
 100% {
 -ms-transform: rotate(720deg);
}
}
 @-webkit-keyframes cssload-heart {
 50% {
 -webkit-transform: rotate(360deg);
}
 100% {
 -webkit-transform: rotate(720deg);
}
}
 @-moz-keyframes cssload-heart {
 50% {
 -moz-transform: rotate(360deg);
}
 100% {
 -moz-transform: rotate(720deg);
}
}
 @keyframes cssload-heartL {
 60% {
 transform: scale(0.4);
}
}
 @-o-keyframes cssload-heartL {
 60% {
 -o-transform: scale(0.4);
}
}
 @-ms-keyframes cssload-heartL {
 60% {
 -ms-transform: scale(0.4);
}
}
 @-webkit-keyframes cssload-heartL {
 60% {
 -webkit-transform: scale(0.4);
}
}
 @-moz-keyframes cssload-heartL {
 60% {
 -moz-transform: scale(0.4);
}
}
 @keyframes cssload-heartR {
 40% {
 transform: scale(0.4);
}
}
 @-o-keyframes cssload-heartR {
 40% {
 -o-transform: scale(0.4);
}
}
 @-ms-keyframes cssload-heartR {
 40% {
 -ms-transform: scale(0.4);
}
}
 @-webkit-keyframes cssload-heartR {
 40% {
 -webkit-transform: scale(0.4);
}
}
 @-moz-keyframes cssload-heartR {
 40% {
 -moz-transform: scale(0.4);
}
}
 @keyframes cssload-shadow {
 50% {
 transform: scale(0.5);
 border-color: rgb(228,228,228);
}
}
 @-o-keyframes cssload-shadow {
 50% {
 -o-transform: scale(0.5);
 border-color: rgb(228,228,228);
}
}
 @-ms-keyframes cssload-shadow {
 50% {
 -ms-transform: scale(0.5);
 border-color: rgb(228,228,228);
}
}
 @-webkit-keyframes cssload-shadow {
 50% {
 -webkit-transform: scale(0.5);
 border-color: rgb(228,228,228);
}
}
 @-moz-keyframes cssload-shadow {
 50% {
 -moz-transform: scale(0.5);
 border-color: rgb(228,228,228);
}
}



                              /*Preloader Demo 48*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute49 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 55%;
	width: 200px;
}
.tscssload-container {
	width: 72px;
	margin: 58px auto;
	font-size: 0;
	position: relative;
	transform-origin: 50% 50%;
	-o-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	animation: cssload-clockwise 6.9s linear infinite;
	-o-animation: cssload-clockwise 6.9s linear infinite;
	-ms-animation: cssload-clockwise 6.9s linear infinite;
	-webkit-animation: cssload-clockwise 6.9s linear infinite;
	-moz-animation: cssload-clockwise 6.9s linear infinite;
}
.tscssload-container:before {
	position: absolute;
	content: '';
	top: 0;
	left: 0;
	width: 39px;
	height: 39px;
	border: 6px solid rgb(229,229,229);
	border-radius: 100%;
	-o-border-radius: 100%;
	-ms-border-radius: 100%;
	-webkit-border-radius: 100%;
	-moz-border-radius: 100%;
	box-sizing: border-box;
	-o-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
}
.tscssload-container:after {
	position: absolute;
	content: '';
	z-index: -1;
	top: 0;
	right: 0;
	width: 39px;
	height: 39px;
	border: 6px solid rgb(229,229,229);
	border-radius: 100%;
	-o-border-radius: 100%;
	-ms-border-radius: 100%;
	-webkit-border-radius: 100%;
	-moz-border-radius: 100%;
	box-sizing: border-box;
	-o-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
}
.tscssload-lt, .tscssload-rt, .tscssload-lb, .tscssload-rb {
	position: relative;
	display: inline-block;
	overflow: hidden;
	width: 39px;
	height: 19px;
	opacity: 1;
}
.tscssload-lt:before, .tscssload-rt:before, .tscssload-lb:before, .tscssload-rb:before {
	position: absolute;
	content: '';
	width: 39px;
	height: 39px;
	border-top: 6px solid #03a9f4;
	border-right: 6px solid transparent;
	border-bottom: 6px solid transparent;
	border-left: 6px solid transparent;
	border-radius: 100%;
	-o-border-radius: 100%;
	-ms-border-radius: 100%;
	-webkit-border-radius: 100%;
	-moz-border-radius: 100%;
	box-sizing: border-box;
	-o-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-moz-box-sizing: border-box;
}
.tscssload-lt {
	margin-right: -6px;
	animation: cssload-lt 2.3s linear -2300ms infinite;
	-o-animation: cssload-lt 2.3s linear -2300ms infinite;
	-ms-animation: cssload-lt 2.3s linear -2300ms infinite;
	-webkit-animation: cssload-lt 2.3s linear -2300ms infinite;
	-moz-animation: cssload-lt 2.3s linear -2300ms infinite;
}
.tscssload-lt:before {
	top: 0;
	left: 0;
	animation: cssload-not-clockwise 1.15s linear infinite;
	-o-animation: cssload-not-clockwise 1.15s linear infinite;
	-ms-animation: cssload-not-clockwise 1.15s linear infinite;
	-webkit-animation: cssload-not-clockwise 1.15s linear infinite;
	-moz-animation: cssload-not-clockwise 1.15s linear infinite;
}
.tscssload-rt {
	animation: cssload-lt 2.3s linear -1150ms infinite;
	-o-animation: cssload-lt 2.3s linear -1150ms infinite;
	-ms-animation: cssload-lt 2.3s linear -1150ms infinite;
	-webkit-animation: cssload-lt 2.3s linear -1150ms infinite;
	-moz-animation: cssload-lt 2.3s linear -1150ms infinite;
}
.tscssload-rt:before {
	top: 0;
	right: 0;
	animation: cssload-clockwise 1.15s linear infinite;
	-o-animation: cssload-clockwise 1.15s linear infinite;
	-ms-animation: cssload-clockwise 1.15s linear infinite;
	-webkit-animation: cssload-clockwise 1.15s linear infinite;
	-moz-animation: cssload-clockwise 1.15s linear infinite;
}
.tscssload-lb {
	margin-right: -6px;
	animation: cssload-lt 2.3s linear -1725ms infinite;
	-o-animation: cssload-lt 2.3s linear -1725ms infinite;
	-ms-animation: cssload-lt 2.3s linear -1725ms infinite;
	-webkit-animation: cssload-lt 2.3s linear -1725ms infinite;
	-moz-animation: cssload-lt 2.3s linear -1725ms infinite;
}
.tscssload-lb:before {
	bottom: 0;
	left: 0;
	animation: cssload-not-clockwise 1.15s linear infinite;
	-o-animation: cssload-not-clockwise 1.15s linear infinite;
	-ms-animation: cssload-not-clockwise 1.15s linear infinite;
	-webkit-animation: cssload-not-clockwise 1.15s linear infinite;
	-moz-animation: cssload-not-clockwise 1.15s linear infinite;
}
.tscssload-rb {
	animation: cssload-lt 2.3s linear -575ms infinite;
	-o-animation: cssload-lt 2.3s linear -575ms infinite;
	-ms-animation: cssload-lt 2.3s linear -575ms infinite;
	-webkit-animation: cssload-lt 2.3s linear -575ms infinite;
	-moz-animation: cssload-lt 2.3s linear -575ms infinite;
}
.tscssload-rb:before {
	bottom: 0;
	right: 0;
	animation: cssload-clockwise 1.15s linear infinite;
	-o-animation: cssload-clockwise 1.15s linear infinite;
	-ms-animation: cssload-clockwise 1.15s linear infinite;
	-webkit-animation: cssload-clockwise 1.15s linear infinite;
	-moz-animation: cssload-clockwise 1.15s linear infinite;
}
 @keyframes cssload-clockwise {
 0% {
 transform: rotate(-45deg);
}
 100% {
 transform: rotate(315deg);
}
}
 @-o-keyframes cssload-clockwise {
 0% {
 -o-transform: rotate(-45deg);
}
 100% {
 -o-transform: rotate(315deg);
}
}
 @-ms-keyframes cssload-clockwise {
 0% {
 -ms-transform: rotate(-45deg);
}
 100% {
 -ms-transform: rotate(315deg);
}
}
 @-webkit-keyframes cssload-clockwise {
 0% {
 -webkit-transform: rotate(-45deg);
}
 100% {
 -webkit-transform: rotate(315deg);
}
}
 @-moz-keyframes cssload-clockwise {
 0% {
 -moz-transform: rotate(-45deg);
}
 100% {
 -moz-transform: rotate(315deg);
}
}
 @keyframes cssload-not-clockwise {
 0% {
 transform: rotate(45deg);
}
 100% {
 transform: rotate(-315deg);
}
}
 @-o-keyframes cssload-not-clockwise {
 0% {
 -o-transform: rotate(45deg);
}
 100% {
 -o-transform: rotate(-315deg);
}
}
 @-ms-keyframes cssload-not-clockwise {
 0% {
 -ms-transform: rotate(45deg);
}
 100% {
 -ms-transform: rotate(-315deg);
}
}
 @-webkit-keyframes cssload-not-clockwise {
 0% {
 -webkit-transform: rotate(45deg);
}
 100% {
 -webkit-transform: rotate(-315deg);
}
}
 @-moz-keyframes cssload-not-clockwise {
 0% {
 -moz-transform: rotate(45deg);
}
 100% {
 -moz-transform: rotate(-315deg);
}
}
 @keyframes cssload-lt {
 0% {
 opacity: 1;
}
 25% {
 opacity: 1;
}
 26% {
 opacity: 0;
}
 75% {
 opacity: 0;
}
 76% {
 opacity: 1;
}
 100% {
 opacity: 1;
}
}
 @-o-keyframes cssload-lt {
 0% {
 opacity: 1;
}
 25% {
 opacity: 1;
}
 26% {
 opacity: 0;
}
 75% {
 opacity: 0;
}
 76% {
 opacity: 1;
}
 100% {
 opacity: 1;
}
}
 @-ms-keyframes cssload-lt {
 0% {
 opacity: 1;
}
 25% {
 opacity: 1;
}
 26% {
 opacity: 0;
}
 75% {
 opacity: 0;
}
 76% {
 opacity: 1;
}
 100% {
 opacity: 1;
}
}
 @-webkit-keyframes cssload-lt {
 0% {
 opacity: 1;
}
 25% {
 opacity: 1;
}
 26% {
 opacity: 0;
}
 75% {
 opacity: 0;
}
 76% {
 opacity: 1;
}
 100% {
 opacity: 1;
}
}
 @-moz-keyframes cssload-lt {
 0% {
 opacity: 1;
}
 25% {
 opacity: 1;
}
 26% {
 opacity: 0;
}
 75% {
 opacity: 0;
}
 76% {
 opacity: 1;
}
 100% {
 opacity: 1;
}
}



                              /*Preloader Demo 49*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute50 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 52%;
	width: 200px;
}
#tscssload-loader {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 171px;
	height: 171px;
	margin: auto;
}
#tscssload-loader .tscssload-dot {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 85.5px;
	height: 100%;
	margin: auto;
}
#tscssload-loader .tscssload-dot:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	width: 85.5px;
	height: 85.5px;
	border-radius: 100%;
	transform: scale(0);
	-o-transform: scale(0);
	-ms-transform: scale(0);
	-webkit-transform: scale(0);
	-moz-transform: scale(0);
}
#tscssload-loader .tscssload-dot:nth-child(7n+1) {
 transform: rotate(45deg);
 -o-transform: rotate(45deg);
 -ms-transform: rotate(45deg);
 -webkit-transform: rotate(45deg);
 -moz-transform: rotate(45deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+1):before {
 background: #03a9f4;
 animation: cssload-load 0.92s linear 0.12s infinite;
 -o-animation: cssload-load 0.92s linear 0.12s infinite;
 -ms-animation: cssload-load 0.92s linear 0.12s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.12s infinite;
 -moz-animation: cssload-load 0.92s linear 0.12s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+2) {
 transform: rotate(90deg);
 -o-transform: rotate(90deg);
 -ms-transform: rotate(90deg);
 -webkit-transform: rotate(90deg);
 -moz-transform: rotate(90deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+2):before {
 background: #03a9f4;
 animation: cssload-load 0.92s linear 0.23s infinite;
 -o-animation: cssload-load 0.92s linear 0.23s infinite;
 -ms-animation: cssload-load 0.92s linear 0.23s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.23s infinite;
 -moz-animation: cssload-load 0.92s linear 0.23s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+3) {
 transform: rotate(135deg);
 -o-transform: rotate(135deg);
 -ms-transform: rotate(135deg);
 -webkit-transform: rotate(135deg);
 -moz-transform: rotate(135deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+3):before {
 background: rgb(0,170,255);
 animation: cssload-load 0.92s linear 0.35s infinite;
 -o-animation: cssload-load 0.92s linear 0.35s infinite;
 -ms-animation: cssload-load 0.92s linear 0.35s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.35s infinite;
 -moz-animation: cssload-load 0.92s linear 0.35s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+4) {
 transform: rotate(180deg);
 -o-transform: rotate(180deg);
 -ms-transform: rotate(180deg);
 -webkit-transform: rotate(180deg);
 -moz-transform: rotate(180deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+4):before {
 background: rgb(0,64,255);
 animation: cssload-load 0.92s linear 0.46s infinite;
 -o-animation: cssload-load 0.92s linear 0.46s infinite;
 -ms-animation: cssload-load 0.92s linear 0.46s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.46s infinite;
 -moz-animation: cssload-load 0.92s linear 0.46s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+5) {
 transform: rotate(225deg);
 -o-transform: rotate(225deg);
 -ms-transform: rotate(225deg);
 -webkit-transform: rotate(225deg);
 -moz-transform: rotate(225deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+5):before {
 background: rgb(42,0,255);
 animation: cssload-load 0.92s linear 0.58s infinite;
 -o-animation: cssload-load 0.92s linear 0.58s infinite;
 -ms-animation: cssload-load 0.92s linear 0.58s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.58s infinite;
 -moz-animation: cssload-load 0.92s linear 0.58s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+6) {
 transform: rotate(270deg);
 -o-transform: rotate(270deg);
 -ms-transform: rotate(270deg);
 -webkit-transform: rotate(270deg);
 -moz-transform: rotate(270deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+6):before {
 background: rgb(149,0,255);
 animation: cssload-load 0.92s linear 0.69s infinite;
 -o-animation: cssload-load 0.92s linear 0.69s infinite;
 -ms-animation: cssload-load 0.92s linear 0.69s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.69s infinite;
 -moz-animation: cssload-load 0.92s linear 0.69s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+7) {
 transform: rotate(315deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+7):before {
 background: magenta;
 animation: cssload-load 0.92s linear 0.81s infinite;
 -o-animation: cssload-load 0.92s linear 0.81s infinite;
 -ms-animation: cssload-load 0.92s linear 0.81s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.81s infinite;
 -moz-animation: cssload-load 0.92s linear 0.81s infinite;
}
#tscssload-loader .tscssload-dot:nth-child(7n+8) {
 transform: rotate(360deg);
 -o-transform: rotate(360deg);
 -ms-transform: rotate(360deg);
 -webkit-transform: rotate(360deg);
 -moz-transform: rotate(360deg);
}
#tscssload-loader .tscssload-dot:nth-child(7n+8):before {
 background: rgb(255,0,149);
 animation: cssload-load 0.92s linear 0.92s infinite;
 -o-animation: cssload-load 0.92s linear 0.92s infinite;
 -ms-animation: cssload-load 0.92s linear 0.92s infinite;
 -webkit-animation: cssload-load 0.92s linear 0.92s infinite;
 -moz-animation: cssload-load 0.92s linear 0.92s infinite;
}
 @keyframes cssload-load {
 100% {
 opacity: 0;
 transform: scale(1);
}
}
 @-o-keyframes cssload-load {
 100% {
 opacity: 0;
 -o-transform: scale(1);
}
}
 @-ms-keyframes cssload-load {
 100% {
 opacity: 0;
 -ms-transform: scale(1);
}
}
 @-webkit-keyframes cssload-load {
 100% {
 opacity: 0;
 -webkit-transform: scale(1);
}
}
 @-moz-keyframes cssload-load {
 100% {
 opacity: 0;
 -moz-transform: scale(1);
}
}



                              /*Preloader Demo 50*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute51 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 70%;
	width: 200px;
}
.tscssload-aim {
	position: relative;
	width: 80px;
	height: 80px;
	left: 35%;
	left: calc(50% - 43px);
	left: -o-calc(50% - 43px);
	left: -ms-calc(50% - 43px);
	left: -webkit-calc(50% - 43px);
	left: -moz-calc(50% - 43px);
	left: calc(50% - 43px);
	border-radius: 50px;
	background-color: rgb(255,255,255);
	border-width: 40px;
	border-style: double;
	border-color:transparent #03a9f4;
	box-sizing:border-box;
	-o-box-sizing:border-box;
	-ms-box-sizing:border-box;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	transform-origin:	50% 50%;
	-o-transform-origin:	50% 50%;
	-ms-transform-origin:	50% 50%;
	-webkit-transform-origin:	50% 50%;
	-moz-transform-origin:	50% 50%;
	animation: cssload-aim 2.3s linear infinite;
	-o-animation: cssload-aim 2.3s linear infinite;
	-ms-animation: cssload-aim 2.3s linear infinite;
	-webkit-animation: cssload-aim 2.3s linear infinite;
	-moz-animation: cssload-aim 2.3s linear infinite;
}
 @keyframes cssload-aim {
 0% {
transform:rotate(0deg);
}
 100% {
transform:rotate(360deg);
}
}
 @-o-keyframes cssload-aim {
 0% {
-o-transform:rotate(0deg);
}
 100% {
-o-transform:rotate(360deg);
}
}
 @-ms-keyframes cssload-aim {
 0% {
-ms-transform:rotate(0deg);
}
 100% {
-ms-transform:rotate(360deg);
}
}
 @-webkit-keyframes cssload-aim {
 0% {
-webkit-transform:rotate(0deg);
}
 100% {
-webkit-transform:rotate(360deg);
}
}
 @-moz-keyframes cssload-aim {
 0% {
-moz-transform:rotate(0deg);
}
 100% {
-moz-transform:rotate(360deg);
}
}


                              /*Preloader Demo 51*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute52 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 76%;
	width: 200px;
}
.tscssload-triangles {
	transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	height: 79px;
	width: 88px;
	position: absolute;
	left: 50%;
}
.tscssload-tri {
	position: absolute;
	animation: cssload-pulse 862.5ms ease-in infinite;
	-o-animation: cssload-pulse 862.5ms ease-in infinite;
	-ms-animation: cssload-pulse 862.5ms ease-in infinite;
	-webkit-animation: cssload-pulse 862.5ms ease-in infinite;
	-moz-animation: cssload-pulse 862.5ms ease-in infinite;
	border-top: 26px solid #03a9f4;
	border-left: 15px solid transparent;
	border-right: 15px solid transparent;
	border-bottom: 0px;
}
.tscssload-tri.tscssload-invert {
	border-top: 0px;
	border-bottom: 26px solid #03a9f4;
	border-left: 15px solid transparent;
	border-right: 15px solid transparent;
}
.tscssload-tri:nth-child(1) {
 left: 29px;
}
.tscssload-tri:nth-child(2) {
 left: 15px;
 top: 26px;
 animation-delay: -143.75ms;
 -o-animation-delay: -143.75ms;
 -ms-animation-delay: -143.75ms;
 -webkit-animation-delay: -143.75ms;
 -moz-animation-delay: -143.75ms;
}
.tscssload-tri:nth-child(3) {
 left: 29px;
 top: 26px;
}
.tscssload-tri:nth-child(4) {
 left: 44px;
 top: 26px;
 animation-delay: -718.75ms;
 -o-animation-delay: -718.75ms;
 -ms-animation-delay: -718.75ms;
 -webkit-animation-delay: -718.75ms;
 -moz-animation-delay: -718.75ms;
}
.tscssload-tri:nth-child(5) {
 top: 53px;
 animation-delay: -287.5ms;
 -o-animation-delay: -287.5ms;
 -ms-animation-delay: -287.5ms;
 -webkit-animation-delay: -287.5ms;
 -moz-animation-delay: -287.5ms;
}
.tscssload-tri:nth-child(6) {
 top: 53px;
 left: 15px;
 animation-delay: -287.5ms;
 -o-animation-delay: -287.5ms;
 -ms-animation-delay: -287.5ms;
 -webkit-animation-delay: -287.5ms;
 -moz-animation-delay: -287.5ms;
}
.tscssload-tri:nth-child(7) {
 top: 53px;
 left: 29px;
 animation-delay: -431.25ms;
 -o-animation-delay: -431.25ms;
 -ms-animation-delay: -431.25ms;
 -webkit-animation-delay: -431.25ms;
 -moz-animation-delay: -431.25ms;
}
.tscssload-tri:nth-child(8) {
 top: 53px;
 left: 44px;
 animation-delay: -575ms;
 -o-animation-delay: -575ms;
 -ms-animation-delay: -575ms;
 -webkit-animation-delay: -575ms;
 -moz-animation-delay: -575ms;
}
.tscssload-tri:nth-child(9) {
 top: 53px;
 left: 58px;
 animation-delay: -575ms;
 -o-animation-delay: -575ms;
 -ms-animation-delay: -575ms;
 -webkit-animation-delay: -575ms;
 -moz-animation-delay: -575ms;
}
 @keyframes cssload-pulse {
 0% {
 opacity: 1;
}
 16.666% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
 @-o-keyframes cssload-pulse {
 0% {
 opacity: 1;
}
 16.666% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
 @-ms-keyframes cssload-pulse {
 0% {
 opacity: 1;
}
 16.666% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
 @-webkit-keyframes cssload-pulse {
 0% {
 opacity: 1;
}
 16.666% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
 @-moz-keyframes cssload-pulse {
 0% {
 opacity: 1;
}
 16.666% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}



                              /*Preloader Demo 52*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute53 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 60%;
	width: 200px;
}
#tscssload-global {
	width: 68px;
	margin: auto;
	margin-top: 49px;
	position: relative;
	cursor: pointer;
	height: 58px;
}
.tscssload-mask {
	position: absolute;
	border-radius: 2px;
	overflow: hidden;
	perspective: 1000;
	-o-perspective: 1000;
	-ms-perspective: 1000;
	-webkit-perspective: 1000;
	-moz-perspective: 1000;
	backface-visibility: hidden;
	-o-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
}
.tscssload-plane {
	background: #006DF0;
	width: 400%;
	height: 100%;
	position: absolute;
	z-index: 100;
	transform: translate3d(0px, 0, 0);
	-o-transform: translate3d(0px, 0, 0);
	-ms-transform: translate3d(0px, 0, 0);
	-webkit-transform: translate3d(0px, 0, 0);
	-moz-transform: translate3d(0px, 0, 0);
	perspective: 1000;
	-o-perspective: 1000;
	-ms-perspective: 1000;
	-webkit-perspective: 1000;
	-moz-perspective: 1000;
	backface-visibility: hidden;
	-o-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
}
#tscssload-top .tscssload-plane {
	z-index: 2000;
	animation: cssload-trans1 1.5s ease-in infinite 0s backwards;
	-o-animation: cssload-trans1 1.5s ease-in infinite 0s backwards;
	-ms-animation: cssload-trans1 1.5s ease-in infinite 0s backwards;
	-webkit-animation: cssload-trans1 1.5s ease-in infinite 0s backwards;
	-moz-animation: cssload-trans1 1.5s ease-in infinite 0s backwards;
}
#tscssload-middle .tscssload-plane {
	background: rgb(0,0,0);
	transform: translate3d(0px, 0, 0);
	-o-transform: translate3d(0px, 0, 0);
	-ms-transform: translate3d(0px, 0, 0);
	-webkit-transform: translate3d(0px, 0, 0);
	-moz-transform: translate3d(0px, 0, 0);
	animation: cssload-trans2 1.5s linear infinite 0.35s backwards;
	-o-animation: cssload-trans2 1.5s linear infinite 0.35s backwards;
	-ms-animation: cssload-trans2 1.5s linear infinite 0.35s backwards;
	-webkit-animation: cssload-trans2 1.5s linear infinite 0.35s backwards;
	-moz-animation: cssload-trans2 1.5s linear infinite 0.35s backwards;
}
#tscssload-bottom .tscssload-plane {
	z-index: 2000;
	animation: cssload-trans3 1.5s ease-out infinite 0.81s backwards;
	-o-animation: cssload-trans3 1.5s ease-out infinite 0.81s backwards;
	-ms-animation: cssload-trans3 1.5s ease-out infinite 0.81s backwards;
	-webkit-animation: cssload-trans3 1.5s ease-out infinite 0.81s backwards;
	-moz-animation: cssload-trans3 1.5s ease-out infinite 0.81s backwards;
}
#tscssload-top {
	width: 52px;
	height: 19px;
	left: 19px;
	transform: skew(-15deg, 0);
	-o-transform: skew(-15deg, 0);
	-ms-transform: skew(-15deg, 0);
	-webkit-transform: skew(-15deg, 0);
	-moz-transform: skew(-15deg, 0);
	z-index: 100;
}
#tscssload-middle {
	width: 32px;
	height: 19px;
	left: 19px;
	top: 15px;
	transform: skew(-15deg, 40deg);
	-o-transform: skew(-15deg, 40deg);
	-ms-transform: skew(-15deg, 40deg);
	-webkit-transform: skew(-15deg, 40deg);
	-moz-transform: skew(-15deg, 40deg);
}
#tscssload-bottom {
	width: 52px;
	height: 19px;
	top: 29px;
	transform: skew(-15deg, 0);
	-o-transform: skew(-15deg, 0);
	-ms-transform: skew(-15deg, 0);
	-webkit-transform: skew(-15deg, 0);
	-moz-transform: skew(-15deg, 0);
}
 @keyframes cssload-trans1 {
 from {
 transform: translate3d(52px, 0, 0);
}
to {
	transform: translate3d(-244px, 0, 0);
}
}
 @-o-keyframes cssload-trans1 {
 from {
 -o-transform: translate3d(52px, 0, 0);
}
to {
	-o-transform: translate3d(-244px, 0, 0);
}
}
 @-ms-keyframes cssload-trans1 {
 from {
 -ms-transform: translate3d(52px, 0, 0);
}
to {
	-ms-transform: translate3d(-244px, 0, 0);
}
}
 @-webkit-keyframes cssload-trans1 {
 from {
 -webkit-transform: translate3d(52px, 0, 0);
}
to {
	-webkit-transform: translate3d(-244px, 0, 0);
}
}
 @-moz-keyframes cssload-trans1 {
 from {
 -moz-transform: translate3d(52px, 0, 0);
}
to {
	-moz-transform: translate3d(-244px, 0, 0);
}
}
 @keyframes cssload-trans2 {
 from {
 transform: translate3d(-156px, 0, 0);
}
to {
	transform: translate3d(52px, 0, 0);
}
}
 @-o-keyframes cssload-trans2 {
 from {
 -o-transform: translate3d(-156px, 0, 0);
}
to {
	-o-transform: translate3d(52px, 0, 0);
}
}
 @-ms-keyframes cssload-trans2 {
 from {
 -ms-transform: translate3d(-156px, 0, 0);
}
to {
	-ms-transform: translate3d(52px, 0, 0);
}
}
 @-webkit-keyframes cssload-trans2 {
 from {
 -webkit-transform: translate3d(-156px, 0, 0);
}
to {
	-webkit-transform: translate3d(52px, 0, 0);
}
}
 @-moz-keyframes cssload-trans2 {
 from {
 -moz-transform: translate3d(-156px, 0, 0);
}
to {
	-moz-transform: translate3d(52px, 0, 0);
}
}
 @keyframes cssload-trans3 {
 from {
 transform: translate3d(52px, 0, 0);
}
to {
	transform: translate3d(-214px, 0, 0);
}
}
 @-o-keyframes cssload-trans3 {
 from {
 -o-transform: translate3d(52px, 0, 0);
}
to {
	-o-transform: translate3d(-214px, 0, 0);
}
}
 @-ms-keyframes cssload-trans3 {
 from {
 -ms-transform: translate3d(52px, 0, 0);
}
to {
	-ms-transform: translate3d(-214px, 0, 0);
}
}
 @-webkit-keyframes cssload-trans3 {
 from {
 -webkit-transform: translate3d(52px, 0, 0);
}
to {
	-webkit-transform: translate3d(-214px, 0, 0);
}
}
 @-moz-keyframes cssload-trans3 {
 from {
 -moz-transform: translate3d(52px, 0, 0);
}
to {
	-moz-transform: translate3d(-214px, 0, 0);
}
}
 @keyframes cssload-animColor {
 from {
 background: red;
}
 25% {
 background: yellow;
}
 50% {
 background: green;
}
 75% {
 background: brown;
}
to {
	background: blue;
}
}
 @-o-keyframes cssload-animColor {
 from {
 background: red;
}
 25% {
 background: yellow;
}
 50% {
 background: green;
}
 75% {
 background: brown;
}
to {
	background: blue;
}
}
 @-ms-keyframes cssload-animColor {
 from {
 background: red;
}
 25% {
 background: yellow;
}
 50% {
 background: green;
}
 75% {
 background: brown;
}
to {
	background: blue;
}
}
 @-webkit-keyframes cssload-animColor {
 from {
 background: red;
}
 25% {
 background: yellow;
}
 50% {
 background: green;
}
 75% {
 background: brown;
}
to {
	background: blue;
}
}
 @-moz-keyframes cssload-animColor {
 from {
 background: red;
}
 25% {
 background: yellow;
}
 50% {
 background: green;
}
 75% {
 background: brown;
}
to {
	background: blue;
}
}



                              /*Preloader Demo 53*/

/******************************************************************************/
/******************************************************************************/




#ts-preloader-absolute54 {
	height: 200px;
	left: 50%;
	margin-left: -100px;
	margin-top: -100px;
	position: absolute;
	top: 52%;
	width: 200px;
}
.csstscssload-load-frame {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 49px;
	height: 49px;
	margin: auto;
	display: box;
	display: -o-box;
	display: -ms-box;
	display: -webkit-box;
	display: -moz-box;
	display: flex;
	display: -o-flex;
	display: -ms-flex;
	display: -webkit-flex;
	display: -moz-flex;
	flex-flow: row wrap;
	-o-flex-flow: row wrap;
	-ms-flex-flow: row wrap;
	-webkit-flex-flow: row wrap;
	-moz-flex-flow: row wrap;
}
.csstscssload-load-frame .tscssload-dot {
	width: 10px;
	height: 10px;
}
.csstscssload-load-frame .tscssload-dot:nth-child(1) {
 background: rgb(32,223,214);
 animation: cssload-load 0.35s linear -0.12s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.12s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.12s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.12s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.12s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(2) {
 background: rgb(32,223,220);
 animation: cssload-load 0.35s linear -0.23s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.23s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.23s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.23s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.23s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(3) {
 background: rgb(32,220,223);
 animation: cssload-load 0.35s linear -0.35s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.35s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.35s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.35s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.35s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(4) {
 background: rgb(32,214,223);
 animation: cssload-load 0.35s linear -0.46s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.46s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.46s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.46s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.46s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(5) {
 background: rgb(32,207,223);
 animation: cssload-load 0.35s linear -0.58s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.58s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.58s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.58s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.58s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(6) {
 background: rgb(32,201,223);
 animation: cssload-load 0.35s linear -0.69s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.69s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.69s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.69s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.69s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(7) {
 background: rgb(32,194,223);
 animation: cssload-load 0.35s linear -0.81s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.81s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.81s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.81s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.81s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(8) {
 background: rgb(32,188,223);
 animation: cssload-load 0.35s linear -0.92s infinite alternate;
 -o-animation: cssload-load 0.35s linear -0.92s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -0.92s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -0.92s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -0.92s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(9) {
 background: rgb(32,182,223);
 animation: cssload-load 0.35s linear -1.04s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.04s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.04s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.04s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.04s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(10) {
 background: rgb(32,175,223);
 animation: cssload-load 0.35s linear -1.15s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.15s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.15s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.15s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.15s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(11) {
 background: rgb(32,169,223);
 animation: cssload-load 0.35s linear -1.27s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.27s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.27s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.27s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.27s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(12) {
 background: rgb(32,163,223);
 animation: cssload-load 0.35s linear -1.38s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.38s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.38s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.38s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.38s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(13) {
 background: rgb(32,156,223);
 animation: cssload-load 0.35s linear -1.5s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.5s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.5s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.5s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.5s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(14) {
 background: rgb(32,150,223);
 animation: cssload-load 0.35s linear -1.61s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.61s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.61s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.61s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.61s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(15) {
 background: rgb(32,143,223);
 animation: cssload-load 0.35s linear -1.73s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.73s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.73s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.73s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.73s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(16) {
 background: rgb(32,137,223);
 animation: cssload-load 0.35s linear -1.84s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.84s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.84s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.84s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.84s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(17) {
 background: #2083df;
 animation: cssload-load 0.35s linear -1.96s infinite alternate;
 -o-animation: cssload-load 0.35s linear -1.96s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -1.96s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -1.96s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -1.96s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(18) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.07s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.07s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.07s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.07s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.07s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(19) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.19s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.19s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.19s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.19s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.19s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(20) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.3s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.3s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.3s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.3s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.3s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(21) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.42s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.42s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.42s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.42s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.42s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(22) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.53s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.53s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.53s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.53s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.53s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(23) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.65s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.65s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.65s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.65s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.65s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(24) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.76s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.76s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.76s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.76s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.76s infinite alternate;
}
.csstscssload-load-frame .tscssload-dot:nth-child(25) {
 background: #006DF0;
 animation: cssload-load 0.35s linear -2.88s infinite alternate;
 -o-animation: cssload-load 0.35s linear -2.88s infinite alternate;
 -ms-animation: cssload-load 0.35s linear -2.88s infinite alternate;
 -webkit-animation: cssload-load 0.35s linear -2.88s infinite alternate;
 -moz-animation: cssload-load 0.35s linear -2.88s infinite alternate;
}
 @keyframes cssload-load {
 100% {
 opacity: 0;
 transform: scale(0.5);
}
}
 @-o-keyframes cssload-load {
 100% {
 opacity: 0;
 -o-transform: scale(0.5);
}
}
 @-ms-keyframes cssload-load {
 100% {
 opacity: 0;
 -ms-transform: scale(0.5);
}
}
 @-webkit-keyframes cssload-load {
 100% {
 opacity: 0;
 -webkit-transform: scale(0.5);
}
}
 @-moz-keyframes cssload-load {
 100% {
 opacity: 0;
 -moz-transform: scale(0.5);
}
}