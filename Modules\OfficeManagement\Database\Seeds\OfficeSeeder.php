<?php

namespace Modules\OfficeManagement\Database\Seeds;

use CodeIgniter\Database\Seeder;

class OfficeSeeder extends Seeder
{
    public function run()
    {
        // Sample agency data
        $offices = [
            [
                'name' => 'Department of Information Technology',
                'description' => 'Government agency responsible for information technology policies and digital transformation initiatives.',
                'address' => '123 Technology Street, Metro Manila, Philippines',
                'email' => '<EMAIL>',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Department of Education',
                'description' => 'Government agency responsible for education policies and programs in the Philippines.',
                'address' => '456 Education Avenue, Pasig City, Philippines',
                'email' => '<EMAIL>',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Department of Health',
                'description' => 'Government agency responsible for health policies and public health programs.',
                'address' => '789 Health Boulevard, Quezon City, Philippines',
                'email' => '<EMAIL>',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Local Government Unit - Manila',
                'description' => 'City government of Manila responsible for local governance and public services.',
                'address' => 'Manila City Hall, Manila, Philippines',
                'email' => '<EMAIL>',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Private Consulting Firm',
                'description' => 'Private consulting firm specializing in government projects and digital solutions.',
                'address' => '321 Business District, Makati City, Philippines',
                'email' => '<EMAIL>',
                'is_active' => 0, // Inactive for testing
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert the offices
        $this->db->table('offices')->insertBatch($offices);
    }
}
