<?php

namespace Modules\AgencyManagement\Controllers;

use App\Controllers\AdminController;
use Mo<PERSON>les\AgencyManagement\Models\AgencyModel;
use Modules\AgencyManagement\Config\Validation;
use \Hermawan\DataTables\DataTable;

class AgencyController extends AdminController
{
    protected $agencyModel;

    public function __construct()
    {
        $this->agencyModel = new AgencyModel();
    }

    public function index()
    {
        if (!hasPermission('agency.manage')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $data['title'] = 'Agency Management';
        $data['page_title'] = 'Agency Management';
        if(hasPermission('agency.dashboard')) {
            $data['statistics'] = $this->agencyModel->getStatistics();
        }
        return view('Modules\AgencyManagement\Views\index', $data);
    }

    public function create()
    {
        if (!hasPermission('agency.manage') && !hasPermission('agency.create')) {
            return redirect()->to(base_url('dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $data['title'] = 'Create Agency';
        $data['page_title'] = 'Create New Agency';
        
        return view('Modules\AgencyManagement\Views\create', $data);
    }

    public function store()
    {
        if (!hasPermission('agency.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $data = $this->request->getPost();
        
        // Validate the input
        $validation = new Validation();
        if(!$this->validate($validation->create_agency, $validation->create_agency_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        

        // Set default values
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;

        if ($this->agencyModel->insert($data)) {
            return redirect()->route('agencies.index')->with('success', 'Agency created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create agency.');
        }
    }

    public function edit($id = null)
    {
        if (!hasPermission('agency.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $agency = $this->agencyModel->find($id);
        
        if (!$agency) {
            return redirect()->route('agencies.index')->with('error', 'Agency not found.');
        }

        $data['agency'] = $agency;
        $data['title'] = 'Edit Agency';
        $data['page_title'] = 'Edit Agency: ' . $agency['name'];
        
        return view('Modules\AgencyManagement\Views\edit', $data);
    }

    public function update($id = null)
    {
        if (!hasPermission('agency.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $agency = $this->agencyModel->find($id);
        
        if (!$agency) {
            return redirect()->route('agencies.index')->with('error', 'Agency not found.');
        }

        $data = $this->request->getPost();
        $data['id'] = $id;

        // Load validation rules and add the ID for unique validation
        $validation = new Validation();
        $rules = $validation->update_agency;

        // Replace {id} placeholder in the unique rule
        $rules['name'] = str_replace('{id}', $id, $rules['name']);
        $rules['email'] = str_replace('{id}', $id, $rules['email']);
        $rules['code'] = str_replace('{id}', $id, $rules['code']);
        if(!$this->validate($rules, $validation->update_agency_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Set default values
        $data['is_active'] = isset($data['is_active']) ? 1 : 0;

        if ($this->agencyModel->update($id, $data)) {
            return redirect()->route('agencies.index')->with('success', 'Agency updated successfully.');
        } else {
            // return redirect()->back()->withInput()->with('error', 'Failed to update agency.');
            return view('Modules\AgencyManagement\Views\edit', ['agency' => $agency,
            'title' => 'Edit Agency',
            'page_title' => 'Edit Agency: ' . $agency['name'],
            'error' => $this->agencyModel->errors(),
            'oldInput' => $data]);
        }
    }

    public function delete($id = null)
    {
        if (!hasPermission('agency.manage')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $agency = $this->agencyModel->find($id);
        
        if (!$agency) {
            return redirect()->route('agencies.index')->with('error', 'Agency not found.');
        }

        // Check if agency can be deleted
        if (!$this->agencyModel->canDelete($id)) {
            return redirect()->route('agencies.index')->with('error', 'Cannot delete agency. There are users assigned to this agency.');
        }

        if ($this->agencyModel->delete($id)) {
            return redirect()->route('agencies.index')->with('success', 'Agency deleted successfully.');
        } else {
            return redirect()->route('agencies.index')->with('error', 'Failed to delete agency.');
        }
    }

    public function show($id = null)
    {
        // if (!hasPermission('agency.view')) {
        //     return $this->response->setJSON(['error' => 'Permission denied']);
        // }

        $agency = $this->agencyModel->getAgencyWithUserCount($id);
        $html = view('Modules\AgencyManagement\Views\show', ['agency' => $agency]);
        
        if (!$agency) {
            return $this->response->setJSON(['error' => 'Agency not found']);
        }

        return $this->response->setJSON(['success' => true, 'data' => $agency, 'html' => $html]);
    }

    /**
     * DataTables AJAX endpoint using Hermawan DataTables
     */
    public function datatable()
    {
        if (!hasPermission('agency.view')) {
            return $this->response->setJSON(['error' => 'Permission denied']);
        }

        $builder = $this->agencyModel->db->table('agencies');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex') // Add row numbering
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                if (hasPermission('agency.manage')) {
                    $actions .= '<button type="button" class="btn btn-info btn-sm me-1" onclick="viewAgency(' . $row->id . ')" title="View">
                        <i class="fas fa-eye"></i>
                    </button>';
                    $actions .= '<a href="' . site_url('admin/agencies/edit/' . $row->id) . '" class="btn btn-warning btn-sm me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    $actions .= '<button id="deleteAgency" type="button" class="btn btn-danger btn-sm" title="Delete" data-id="'. $row->id.'">
                        <i class="fas fa-trash"></i>
                    </button>';
                }
                return $actions;
            })
            ->toJson(true); // Allow HTML in columns
    }

    /**
     * AJAX delete endpoint
     */
    public function ajaxDelete()
    {
        if (!hasPermission('agency.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $id = $this->request->getPost('id');
        $agency = $this->agencyModel->find($id);
        
        if (!$agency) {
            return $this->response->setJSON(['success' => false, 'message' => 'Agency not found']);
        }

        // Check if agency can be deleted
        if (!$this->agencyModel->canDelete($id)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete agency. There are users assigned to this agency.']);
        }

        if ($this->agencyModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Agency deleted successfully']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete agency']);
        }
    }

    /**
     * Bulk delete agencies
     */
    public function bulkDelete()
    {
        if (!hasPermission('agency.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No agencies selected']);
        }

        $result = $this->agencyModel->bulkDelete($ids);
        
        if (isset($result['error'])) {
            return $this->response->setJSON(['success' => false, 'message' => $result['error']]);
        }

        return $this->response->setJSON(['success' => true, 'message' => 'Selected agencies deleted successfully']);
    }

    /**
     * Bulk activate agencies
     */
    public function bulkActivate()
    {
        if (!hasPermission('agency.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No agencies selected']);
        }

        if ($this->agencyModel->bulkUpdateStatus($ids, 1)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected agencies activated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to activate agencies']);
    }

    /**
     * Bulk deactivate agencies
     */
    public function bulkDeactivate()
    {
        if (!hasPermission('agency.manage')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No agencies selected']);
        }

        if ($this->agencyModel->bulkUpdateStatus($ids, 0)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Selected agencies deactivated successfully']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to deactivate agencies']);
    }
}
