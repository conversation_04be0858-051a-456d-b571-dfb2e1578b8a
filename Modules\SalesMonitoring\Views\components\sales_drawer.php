<!-- Sales Drawer Overlay -->
<div class="sales-drawer-overlay" id="salesDrawerOverlay" onclick="closeSalesDrawer()"></div>

<!-- Sales Input Drawer -->
<div class="sales-drawer" id="salesDrawer">
    <div class="sales-drawer-header">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h3 class="mb-1">Add New Sale</h3>
                <p class="text-muted mb-0">Record your trade expo sales transaction</p>
            </div>
            <button type="button" class="btn-close" onclick="closeSalesDrawer()"></button>
        </div>
    </div>
    
    <div class="sales-drawer-body">
        <form id="salesForm" novalidate>
            <!-- Sale Type Selection -->
            <div class="form-section" data-aos="fade-up">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/><path d="M17.657 16.657l-4.243 4.243a2 2 0 0 1 -2.827 0l-4.244 -4.243a8 8 0 1 1 11.314 0z"/></svg>
                    Sale Type
                </div>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="sale-type-card card text-center p-3" onclick="selectSaleType('export')">
                            <div class="sale-type-icon text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                            </div>
                            <h4 class="mb-1">Export Sales</h4>
                            <p class="text-muted small mb-0">International buyers</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="sale-type-card card text-center p-3" onclick="selectSaleType('domestic')">
                            <div class="sale-type-icon text-success">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="5 12 3 12 12 3 21 12 19 12"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>
                            </div>
                            <h4 class="mb-1">Domestic Sales</h4>
                            <p class="text-muted small mb-0">Local market</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="sale-type-card card text-center p-3" onclick="selectSaleType('retail')">
                            <div class="sale-type-icon text-info">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="6" cy="19" r="2"/><circle cx="17" cy="19" r="2"/><path d="M17 17h-11v-14h-2"/><path d="M6 5l14 1l-1 7h-13"/></svg>
                            </div>
                            <h4 class="mb-1">Retail Sales</h4>
                            <p class="text-muted small mb-0">Direct to consumer</p>
                        </div>
                    </div>
                </div>
                <input type="hidden" id="saleType" name="sale_type" required>
            </div>

            <!-- Basic Information -->
            <div class="form-section" id="basicInfoSection" style="display: none;" data-aos="fade-up" data-aos-delay="100">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/><line x1="9" y1="9" x2="10" y2="9"/><line x1="9" y1="13" x2="15" y2="13"/><line x1="9" y1="17" x2="15" y2="17"/></svg>
                    Basic Information
                </div>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label required">Product Category</label>
                        <select class="form-select" id="productCategory" name="product_category" required>
                            <option value="">Select category...</option>
                            <option value="textiles">Textiles & Clothing</option>
                            <option value="furniture">Furniture & Home Decor</option>
                            <option value="handicrafts">Handicrafts</option>
                            <option value="jewelry">Jewelry & Accessories</option>
                            <option value="food">Food & Beverages</option>
                            <option value="electronics">Electronics</option>
                            <option value="others">Others</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Date of Sale</label>
                        <input type="date" class="form-control" id="saleDate" name="sale_date" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Buyer Information -->
            <div class="form-section" id="buyerInfoSection" style="display: none;" data-aos="fade-up" data-aos-delay="200">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
                    Buyer Information
                </div>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label required">Buyer Name</label>
                        <input type="text" class="form-control" id="buyerName" name="buyer_name" placeholder="Enter buyer name" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6" id="buyerTypeField">
                        <label class="form-label required">Type of Buyer</label>
                        <input type="text" class="form-control" id="buyerType" name="buyer_type" placeholder="Enter buyer type" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Export-specific fields -->
            <div class="form-section" id="exportFields" style="display: none;" data-aos="fade-up" data-aos-delay="300">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"/></svg>
                    Export Details
                </div>
                <div class="row g-3">
                    <div class="col-md-12">
                        <label class="form-label required">Country to Export</label>
                        <select class="form-select" id="countryExport" name="country_export">
                            <option value="">Select country...</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country->id ?>"><?= $country->country ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Sale Details -->
            <div class="form-section" id="saleDetailsSection" style="display: none;" data-aos="fade-up" data-aos-delay="400">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M14.8 9a2 2 0 0 0 -1.8 -1h-2a2 2 0 0 0 0 4h2a2 2 0 0 1 0 4h-2a2 2 0 0 1 -1.8 -1"/><path d="M12 6v2m0 8v2"/></svg>
                    Sale Details
                </div>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label required">Status</label>
                        <div>
                            <div id="standardStatusOptions">
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" value="booked" required>
                                    <span class="form-check-label">Booked</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" value="under negotiation" required>
                                    <span class="form-check-label">Under Negotiation</span>
                                </label>
                            </div>
                            <div id="consideredOption" style="display: none;">
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" value="booked" required id="consideredBookedRadio">
                                    <span class="form-check-label">Considered Booked</span>
                                </label>
                            </div>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required">Cost</label>
                        <div class="currency-input">
                            <span class="currency-symbol" id="currencySymbol">$</span>
                            <input type="number" class="form-control" id="saleCost" name="cost" placeholder="0.00" step="0.01" min="0" required>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Additional Notes -->
            <div class="form-section" id="notesSection" style="display: none;" data-aos="fade-up" data-aos-delay="500">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                    Additional Notes (Optional)
                </div>
                <div class="row g-3">
                    <div class="col-12">
                        <textarea class="form-control" id="saleNotes" name="notes" rows="3" placeholder="Add any additional notes about this sale..."></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div class="sales-drawer-footer">
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary" onclick="closeSalesDrawer()">
                Cancel
            </button>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                    Save as Draft
                </button>
                <button type="button" class="btn btn-primary" onclick="submitSale()">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/></svg>
                    Save Sale
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Sales Drawer Functionality
let currentSaleType = '';

function openSalesDrawer() {
    const drawer = document.getElementById('salesDrawer');
    const overlay = document.getElementById('salesDrawerOverlay');
    
    drawer.classList.add('show');
    overlay.classList.add('show');
    
    // Reset form
    resetSalesForm();
    
    // Set today's date as default
    document.getElementById('saleDate').value = new Date().toISOString().split('T')[0];
    
    // Initialize AOS for drawer content
    AOS.refresh();
}

function closeSalesDrawer() {
    const drawer = document.getElementById('salesDrawer');
    const overlay = document.getElementById('salesDrawerOverlay');
    
    drawer.classList.remove('show');
    overlay.classList.remove('show');
}

function selectSaleType(type) {
    // Remove active class from all cards
    document.querySelectorAll('.sale-type-card').forEach(card => {
        card.classList.remove('active');
    });
    
    // Add active class to selected card
    event.currentTarget.classList.add('active');
    
    currentSaleType = type;
    document.getElementById('saleType').value = type;
    
    // Show/hide fields based on sale type
    updateFormFields(type);
    
    // Animate form sections
    setTimeout(() => {
        AOS.refresh();
    }, 100);

    const basicInfoSection = document.getElementById('basicInfoSection');
    if (basicInfoSection) {
        basicInfoSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function updateFormFields(type) {
    const exportFields = document.getElementById('exportFields');
    const buyerTypeField = document.getElementById('buyerTypeField');
    const currencySymbol = document.getElementById('currencySymbol');
    const consideredOption = document.getElementById('consideredOption');
    const consideredBookedRadio = document.getElementById('consideredBookedRadio');
    const standardStatusOptions = document.getElementById('standardStatusOptions');
    const buyerType = document.getElementById('buyerType');

    // Show all form sections when a type is selected
    const formSections = ['basicInfoSection', 'buyerInfoSection', 'saleDetailsSection', 'notesSection'];
    formSections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
            // Add fade-in animation
            section.classList.add('fade-in');
        }
    });

    // Reset visibility for type-specific fields
    exportFields.style.display = 'none';
    buyerTypeField.style.display = 'block';

    // Default: show standard statuses, hide considered booked
    if (standardStatusOptions) standardStatusOptions.style.display = 'block';
    if (consideredOption) consideredOption.style.display = 'none';




    // Remove all status selections
    const statusRadios = document.querySelectorAll('input[name="status"]');
    statusRadios.forEach(radio => radio.checked = false);

    // Update based on sale type
    switch(type) {
        case 'export':
            exportFields.style.display = 'block';
            currencySymbol.textContent = '$';
            buyerType.required = false;
            break;

        case 'domestic':
            currencySymbol.textContent = '₱';
            buyerType.required = true;
            break;

        case 'retail':
            currencySymbol.textContent = '₱';
            buyerType.required = true;
            // SHOW the entire considered booked radio button
            // Hide standard options
            if (standardStatusOptions) standardStatusOptions.style.display = 'none';

            // Show "Considered Booked"
            if (consideredOption) consideredOption.style.display = 'inline-block';

            // Set it as default selected
            if (consideredBookedRadio) consideredBookedRadio.checked = true;
            break;
    }
}

function resetSalesForm() {
    const form = document.getElementById('salesForm');
    form.reset();

    // Clear active sale type cards
    document.querySelectorAll('.sale-type-card').forEach(card => {
        card.classList.remove('active');
    });

    // Hide all form sections
    const formSections = [
        'basicInfoSection',
        'buyerInfoSection',
        'exportFields',
        'saleDetailsSection',
        'notesSection'
    ];
    formSections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    });

    // Reset currentSaleType
    currentSaleType = '';
    document.getElementById('saleType').value = '';

    // Uncheck all status radio buttons
    const statusRadios = document.querySelectorAll('input[name="status"]');
    statusRadios.forEach(radio => {
        radio.checked = false;
    });

    // Reset visibility of status options
    const standardStatusOptions = document.getElementById('standardStatusOptions');
    const consideredOption = document.getElementById('consideredOption');
    if (standardStatusOptions) standardStatusOptions.style.display = 'block';
    if (consideredOption) consideredOption.style.display = 'none';

    // Clear validation states
    const allInputs = form.querySelectorAll('input, select, textarea');
    allInputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}


function validateForm() {
    const form = document.getElementById('salesForm');
    // We now check for radio buttons with the name 'status'
    const statusChecked = form.querySelector('input[name="status"]:checked');
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    // Check if a status radio button is selected
    if (!statusChecked) {
        const statusField = form.querySelector('input[name="status"]');
        if (statusField) {
            statusField.parentNode.querySelector('.invalid-feedback').textContent = 'Please select a status.';
            statusField.classList.add('is-invalid');
        }
        isValid = false;
    } else {
        const statusField = form.querySelector('input[name="status"]');
        if (statusField) {
            statusField.classList.remove('is-invalid');
        }
    }
 
    if (!currentSaleType) {
        showDrawerAlert('Please select a sale type', 'warning');
        isValid = false;
    }
    
    return isValid;
}

function submitSale() {
    if (!validateForm()) {
        return;
    }
    
    const submitBtn = event.target;
    submitBtn.classList.add('btn-loading');
    submitBtn.disabled = true;
    
    const formData = new FormData(document.getElementById('salesForm'));
    
    // Simulate API call
    // setTimeout(() => {
    //     submitBtn.classList.remove('btn-loading');
    //     submitBtn.disabled = false;
        
    //     showAlert('Sale recorded successfully!', 'success');
    //     closeSalesDrawer();
        
    //     // Refresh the page or update the table
    //     // location.reload();
    // }, 2000);

    submitBtn.classList.remove('btn-loading');
        submitBtn.disabled = false;
    //use sweet alert
    // Swal.fire({
    //     title: 'Success!',
    //     text: 'Sale recorded successfully!',
    //     icon: 'success',
    //     timer: 2000,
    //     showConfirmButton: false
    // });
    showDrawerAlert('Sale recorded successfully!', 'success');
        closeSalesDrawer();
}

function saveDraft() {
    const formData = new FormData(document.getElementById('salesForm'));
    
    showDrawerAlert('Draft saved successfully!', 'info');
}

function showDrawerAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible alert-floating fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Close drawer on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeSalesDrawer();
    }
});
</script>
