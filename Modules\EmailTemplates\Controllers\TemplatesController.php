<?php

namespace Modules\EmailTemplates\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

use Modules\EmailTemplates\Models\EmailTemplatesModel;
use Modules\EmailTemplates\Config\EmailTemplates as EmailTemplatesConfig;
use Modules\EmailTemplates\Config\Validation;
use Modules\Authentication\Services\UserService;

use \Hermawan\DataTables\DataTable;

class TemplatesController extends BaseController
{
    protected $emailTemplatesModel;

    public function __construct()
    {
        $this->emailTemplatesModel = new EmailTemplatesModel();
    }

    public function index()
    {
        $config = new EmailTemplatesConfig();

        $data = [
            'title' => 'Create Email Template',
            'page_title' => 'New Email Template',
            'categories' => $config->template_categories,
        ];
        $config = new EmailTemplatesConfig();
        return view('Modules\EmailTemplates\Views\index', $data);

    }


    public function create()
    {

        $config = new EmailTemplatesConfig();

        $data = [
            'categories' => $config->template_categories,
            'title' => 'Create Email Template',
            'page_title' => 'New Email Template',
        ];

        return view('Modules\EmailTemplates\Views\create', $data);
    }

    public function store()
    {
        $user_id = session()->get('user_id');
        $validation = new Validation();
        if(!$this->validate($validation->save_template, $validation->save_template_errors)){
            return $this->response->setJSON(['success' => false, 'message' => 'Validation failed']);
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'slug' => $this->request->getPost('slug'),
            'description' => $this->request->getPost('description'),
            'category_name' => $this->request->getPost('category_name'),
            'subject' => $this->request->getPost('subject'),
            'html_content' => $this->request->getPost('html_content'),
            'design_json' => $this->request->getPost('design_json'),
            'created_by' => $user_id,
            'created_at' => date('Y-m-d H:i:s'),
            'is_active' => $this->request->getPost('is_active') ?? 0,
            'created_by' => $user_id,
        ];

        $this->emailTemplatesModel->insert($data);

        if($this->emailTemplatesModel->affectedRows() > 0){
            return $this->response->setJSON(['success' => true, 'message' => 'Template created successfully']);
        }else{
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to create template']);
        }
        
    }


    public function datatable()
    {
        $request = $this->request->getPost();

        $category = $request['category'] ?? '';
        $status = $request['status'] ?? '';

        $builder = $this->emailTemplatesModel->getTemplates($category, $status);

        // print_r($builder->get()->getResultArray());exit();

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex') // Add row numbering
            ->add('name', function($row) {
                return '<strong>' . esc($row->name) . '</strong><br><small class="text-muted">' . esc($row->description) . '</small>';
            })
            ->add('checkbox', function($row) {
                return '<input type="checkbox" class="select-item" value="' . $row->id . '">';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $editUrl    = base_url('admin/email-templates/edit/' . $row->id);
                $previewUrl = base_url('admin/email-templates/preview/' . $row->id);

                return '<div class="btn-group" role="group">
                    <a href="' . $editUrl . '" class="btn btn-sm btn-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="' . $previewUrl . '" class="btn btn-sm btn-info" title="Preview" target="_blank">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn btn-sm btn-warning" onclick="duplicateTemplate(' . $row->id . ')" title="Duplicate">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteTemplate(' . $row->id . ')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                    </div>
                ';
            })
            ->toJson(true);
    }

    public function edit($id)
    {
        $template = $this->emailTemplatesModel->find($id);

        if (!$template) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Template not found');
        }

        $config = new EmailTemplatesConfig();
        $data = [
            'categories' => $config->template_categories,
            'title' => 'Edit Email Template',
            'page_title' => 'Edit Email Template',
            'template' => $template,
        ];

        return view('Modules\EmailTemplates\Views\edit', $data);
    }

    /**
     * Update email template
     */
    public function update($id)
    {
        // if (!hasPermission('email_templates.manage')) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Permission denied']);
        // }

        $template = $this->emailTemplatesModel->find($id);
        // if (!$template) {
        //     return $this->response->setJSON(['success' => false, 'message' => 'Template not found']);
        // }

        // $validation = new Validation();
        // if (!$this->validate($validation->save_template, $validation->save_template_errors)) {
        //     $errors = $this->validator->getErrors();
        //     return $this->response->setJSON([
        //         'success' => false,
        //         'message' => 'Validation failed',
        //         'errors' => $errors
        //     ]);
        // }

        $userId = session()->get('user_id');
        $data = [
            'name' => $this->request->getPost('name'),
            'slug' => $this->request->getPost('slug'),
            'description' => $this->request->getPost('description'),
            'category_name' => $this->request->getPost('category_name'),
            'subject' => $this->request->getPost('subject'),
            'html_content' => $this->request->getPost('html_content'),
            'design_json' => $this->request->getPost('design_json'),
            'is_active' => $this->request->getPost('is_active') ?? 0,
            'updated_by' => $userId,
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        try {
            $result = $this->emailTemplatesModel->update($id, $data);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Template updated successfully',
                    'template_id' => $id
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update template'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Template update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while updating the template'
            ]);
        }
    }

    public function duplicate($id)
    {
        $template = $this->emailTemplatesModel->find($id);
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'message' => 'Template not found']);
        }

        $newSlug = $template['slug'] . '-copy-' . time();
        $data = [
            'name' => $template['name'] . ' (Copy)',
            'slug' => $newSlug,
            'description' => $template['description'],
            'category_name' => $template['category_name'],
            'subject' => $template['subject'],
            'html_content' => $template['html_content'],
            'design_json' => $template['design_json'],
            'created_by' => session()->get('user_id'),
            'is_active' => 0,
        ];

        $this->emailTemplatesModel->insert($data);

        if($this->emailTemplatesModel->affectedRows() > 0){
            return $this->response->setJSON(['success' => true, 'message' => 'Template duplicated successfully']);
        }else{
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to duplicate template']);
        }
    }

    public function delete($id)
    {
        $template = $this->emailTemplatesModel->find($id);
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'message' => 'Template not found']);
        }

        $this->emailTemplatesModel->delete($id);

        if($this->emailTemplatesModel->affectedRows() > 0){
            return $this->response->setJSON(['success' => true, 'message' => 'Template deleted successfully']);
        }else{
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete template']);
        }
    }

    public function preview($id)
    {
        $template = $this->emailTemplatesModel->find($id);
        if (!$template) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Template not found');
        }

        $data = [
            'template' => $template,
            'html_content' => $template['html_content'],
            'subject' => $template['subject']
        ];

        return view('Modules\EmailTemplates\Views\preview', $data);
    }




 
}
