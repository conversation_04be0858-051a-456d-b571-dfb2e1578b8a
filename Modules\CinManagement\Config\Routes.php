<?php

namespace Modules\CinManagement\Config;

$routes->group('cin', ['namespace' => 'Modules\CinManagement\Controllers', 'filter' => 'login'], function ($routes) {
    $routes->get('/', 'CinController::index', ['as' => 'cin.index']);
    $routes->get('create', 'CinController::create', ['as' => 'cin.create']);
    $routes->post('store', 'CinController::store', ['as' => 'cin.store']);
    $routes->get('edit/(:num)', 'CinController::edit/$1', ['as' => 'cin.edit']);
    $routes->post('update/(:num)', 'CinController::update/$1', ['as' => 'cin.update']);
    $routes->get('delete/(:num)', 'CinController::delete/$1', ['as' => 'cin.delete']);
});
