<?php

namespace Modules\EmailTemplates\Config;

$routes->group('admin', function($routes) {
    $routes->group('email-templates', ['namespace' => 'Modules\EmailTemplates\Controllers'], function ($routes) {

        // Main CRUD routes
        $routes->GET('/', 'TemplatesController::index', ['as' => '  emailTemplates.index']);
        $routes->GET('create', 'TemplatesController::create', ['as' => 'emailTemplate.create']);
        $routes->POST('store', 'TemplatesController::store', ['as' => 'emailTemplate.store']);
        $routes->GET('edit/(:segment)', 'TemplatesController::edit/$1', ['as' => 'emailTemplate.edit']);
        $routes->POST('update/(:segment)', 'TemplatesController::update/$1', ['as' => 'emailTemplate.update']);
        $routes->DELETE('delete/(:segment)', 'TemplatesController::delete/$1', ['as' => 'emailTemplate.delete']);
        $routes->POST('duplicate/(:num)', 'TemplatesController::duplicate/$1');
        $routes->GET('preview/(:num)', 'TemplatesController::preview/$1', ['as' => 'emailTemplate.preview']);


        // AJAX endpoints for DataTables
        $routes->POST('datatable', 'TemplatesController::datatable', ['as' => 'emailTemplate.datatable']);
    });
});
