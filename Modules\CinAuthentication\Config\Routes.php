<?php

namespace Modules\CinAuthentication\Config;

$routes->group('cinauth', ['namespace' => 'Modules\CinAuthentication\Controllers'], function ($routes) {
    $routes->get('/', 'AuthController::login', ['as' => 'cinauth.login']);
    $routes->post('/', 'AuthController::attemptLogin');
    $routes->get('register', 'AuthController::register', ['as' => 'cinauth.register','filter'=>'login']);
    $routes->post('register', 'AuthController::attemptRegister');
    $routes->get('logout', 'AuthController::logout', ['as' => 'cinauth.logout']);

    $routes->get('forgot-password', 'AuthController::forgotPassword', ['as' => 'cinauth.forgotPassword']);
    $routes->post('forgot-password', 'AuthController::sendResetLink');
    $routes->get('reset-password/(:hash)', 'AuthController::resetPassword/$1', ['as' => 'cinauth.resetPassword']);
    $routes->post('reset-password/(:hash)', 'AuthController::attemptResetPassword/$1');

});

// $routes->group('admin/roles', ['namespace' => 'Modules\Authentication\Controllers', 'filter' => 'auth'], function ($routes) {
//     $routes->get('/', 'RoleController::index', ['as' => 'roles.index']);
//     $routes->get('create', 'RoleController::create', ['as' => 'roles.create']);
//     $routes->post('store', 'RoleController::store', ['as' => 'roles.store']);
//     $routes->get('edit/(:num)', 'RoleController::edit/$1', ['as' => 'roles.edit']);
//     $routes->post('update/(:num)', 'RoleController::update/$1', ['as' => 'roles.update']);
//     $routes->get('delete/(:num)', 'RoleController::delete/$1', ['as' => 'roles.delete']);
// });
