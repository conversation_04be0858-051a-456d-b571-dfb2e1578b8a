<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/permissions') ?>">Permissions</a></li>
<li class="breadcrumb-item active">List</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<?php if (hasPermission('permission.dashboard')) : ?>
    <!-- Statistics Cards -->
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><?= $stats['total'] ?? 0 ?></h3>
                    <p>Total Permissions</p>
                </div>
                <div class="icon">
                    <i class="fas fa-key"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><?= $stats['categories'] ?? 0 ?></h3>
                    <p>Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3><?= $stats['recent'] ?? 0 ?></h3>
                    <p>Added This Month</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3><?= $stats['unused'] ?? 0 ?></h3>
                    <p>Unused Permissions</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">All Permissions</h3>
        <div class="card-tools">
            <a href="<?= route_to('permission.create') ?>" class="btn btn-primary btn-sm rounded-md shadow-sm">
                <i class="fas fa-plus"></i> Add New Permission
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Bulk Actions -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                        <i class="fas fa-check"></i> Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                        <i class="fas fa-times"></i> Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <span class="ml-2 text-muted" id="selected-count">0 selected</span>
            </div>
        </div>

        <div class="table-responsive">
            <table id="permissionsTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <?php if (hasPermission('permission.manage')) : ?>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <?php endif; ?>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Permission View Modal -->
<div class="modal fade" id="permissionViewModal" tabindex="-1" role="dialog" aria-labelledby="permissionViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="permissionViewModalLabel">Permission Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="permissionViewContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables  & Plugins -->
<script src="<?= base_url("assets/plugins/datatables/jquery.dataTables.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/jszip/jszip.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/pdfmake.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/vfs_fonts.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js") ?>"></script>
<script>
$(document).ready(function() {
    // CSRF token
    var csrfName = '<?= csrf_token() ?>';
    var csrfHash = '<?= csrf_hash() ?>';

    // Initialize DataTable
    var permissionTable = $('#permissionsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/permissions/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[2, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('permission.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'name' }
            ,{ data: 'description' }
            ,{ data: 'category', orderable: false, searchable: false }
            ,{ data: 'status_badge', orderable: false, searchable: false }
            ,{ data: 'created_date' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // Select all checkbox functionality
    $('#select-all').on('click', function() {
        var isChecked = $(this).prop('checked');
        $('.select-item').prop('checked', isChecked);
        updateBulkButtons();
    });

    // Individual checkbox functionality
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select-all checkbox
        var totalCheckboxes = $('.select-item').length;
        var checkedCheckboxes = $('.select-item:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedCount = $('.select-item:checked').length;
        $('#selected-count').text(selectedCount + ' selected');

        if (selectedCount > 0) {
            $('#bulk-delete, #bulk-activate, #bulk-deactivate').prop('disabled', false);
        } else {
            $('#bulk-delete, #bulk-activate, #bulk-deactivate').prop('disabled', true);
        }
    }

    // Bulk delete
    $('#bulk-delete').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select permissions to delete.');
            return;
        }

        if (confirm('Are you sure you want to delete the selected permissions?')) {
            $.ajax({
                url: '<?= site_url('admin/permissions/bulk-delete') ?>',
                type: 'POST',
                data: {
                    ids: selectedIds,
                    [csrfName]: csrfHash
                },
                success: function(response) {
                    if (response.success) {
                        permissionTable.ajax.reload();
                        $('#select-all').prop('checked', false);
                        updateBulkButtons();
                        alert(response.message);
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while deleting permissions.');
                }
            });
        }
    });

    // Bulk activate
    $('#bulk-activate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select permissions to activate.');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/permissions/bulk-activate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    permissionTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    alert(response.message);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('An error occurred while activating permissions.');
            }
        });
    });

    // Bulk deactivate
    $('#bulk-deactivate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select permissions to deactivate.');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/permissions/bulk-deactivate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    permissionTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    alert(response.message);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('An error occurred while deactivating permissions.');
            }
        });
    });

    // Individual delete
    $(document).on('click', '#deletePermission', function() {
        var permissionId = $(this).data('id');

        if (confirm('Are you sure you want to delete this permission?')) {
            $.ajax({
                url: '<?= site_url('admin/permissions/ajax-delete') ?>',
                type: 'POST',
                data: {
                    id: permissionId,
                    [csrfName]: csrfHash
                },
                success: function(response) {
                    if (response.success) {
                        permissionTable.ajax.reload();
                        alert(response.message);
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while deleting the permission.');
                }
            });
        }
    });
});

// View permission function
function viewPermission(permissionId) {
    $.ajax({
        url: '<?= site_url('admin/permissions/show') ?>/' + permissionId,
        type: 'GET',
        success: function(response) {
            $('#permissionViewContent').html(response);
            $('#permissionViewModal').modal('show');
        },
        error: function() {
            alert('An error occurred while loading permission details.');
        }
    });
}
</script>
<?= $this->endSection() ?>