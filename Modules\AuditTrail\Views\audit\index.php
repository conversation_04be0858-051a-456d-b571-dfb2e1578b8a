<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
<li class="breadcrumb-item active">Audit Trail</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<?php if (hasPermission('audit.view_dashboard')) : ?>
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="total-logs"><?= $stats['total'] ?? 0 ?></h3>
                    <p>Total Audit Entries</p>
                </div>
                <div class="icon">
                    <i class="fas fa-list"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="today-logs"><?= $stats['today'] ?? 0 ?></h3>
                    <p>Today's Activities</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="month-logs"><?= $stats['this_month'] ?? 0 ?></h3>
                    <p>This Month</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="event-types"><?= count($stats['by_event'] ?? []) ?></h3>
                    <p>Event Types</p>
                </div>
                <div class="icon">
                    <i class="fas fa-layer-group"></i>
                </div>
            </div>
        </div>
    </div>
    <!-- Event Types Chart -->
    <?php if (!empty($stats['by_event'])): ?>
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Events Distribution</h3>
                </div>
                <div class="card-body">
                    <div class="chart-responsive">
                        <canvas id="eventChart" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Entity Types</h3>
                </div>
                <div class="card-body">
                    <div class="chart-responsive">
                        <canvas id="entityChart" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Audit Trail Logs</h3>
        <div class="card-tools">
            <?php if (hasPermission('audit.export')) : ?>
                <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm" onclick="showExportModal()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            <?php endif; ?>
            <?php if (hasPermission('audit.purge')) : ?>
                <button type="button" class="btn btn-warning btn-sm ml-2" onclick="showCleanupModal()">
                    <i class="fas fa-broom"></i> Purge Old Logs
                </button>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-check"></i> <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-ban"></i> <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table id="auditTrailTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <th>Date/Time</th>
                        <th>User</th>
                        <th>Event</th>
                        <th>Entity</th>
                        <th>IP Address</th>
                        <th width="80">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Audit Trail Details Modal -->
<div class="modal fade" id="auditDetailsModal" tabindex="-1" role="dialog" aria-labelledby="auditDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditDetailsModalLabel">Audit Trail Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="auditDetailsModalBody">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">Export Audit Trail</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="exportForm" action="<?= site_url('admin/audit/export') ?>" method="GET">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="export_format">Format</label>
                        <select class="form-control" id="export_format" name="format">
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="date_from">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="form-group">
                        <label for="date_to">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Export</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog" aria-labelledby="cleanupModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cleanupModalLabel">Cleanup Old Audit Logs</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="days_to_keep">Keep logs for (days)</label>
                    <input type="number" class="form-control" id="days_to_keep" value="365" min="30" max="3650">
                    <small class="form-text text-muted">Logs older than this will be permanently deleted.</small>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. Old audit logs will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="cleanupOldLogs()">Cleanup</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables JS -->
<script src="<?= base_url('assets/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>

<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/chart.js/Chart.min.js') ?>"></script>

<script>
let auditTable;
let csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');

$(document).ready(function() {
    // Initialize DataTable
    auditTable = $('#auditTrailTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/audit/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d.<?= csrf_token() ?> = csrfHash;
            },
            error: function(xhr, error, thrown) {
                console.error('DataTable Error:', error);
                Swal.fire('Error!', 'Failed to load data. Please refresh the page.', 'error');
            }
        },
        order: [[1, 'desc']], // Sort by date/time
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'created_at_formatted', name: 'a.created_at' },
            { data: 'user_info', name: 'u.username' },
            { data: 'event_formatted', name: 'a.event' },
            { data: 'entity_info', name: 'a.auditable_type' },
            { data: 'ip_address', name: 'a.ip_address' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // View details button click
    $(document).on('click', '.view-details', function() {
        var logId = $(this).data('id');
        viewAuditDetails(logId);
    });

    // Initialize charts
    initializeCharts();
});

// View audit trail details
function viewAuditDetails(logId) {
    $.ajax({
        url: '<?= site_url('admin/audit/show') ?>/' + logId,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                var log = response.data;
                var html = buildDetailsHtml(log);
                $('#auditDetailsModalBody').html(html);
                $('#auditDetailsModal').modal('show');
            } else {
                Swal.fire('Error!', response.error || 'Failed to load audit trail details.', 'error');
            }
        },
        error: function() {
            Swal.fire('Error!', 'Failed to load audit trail details.', 'error');
        }
    });
}

// Build details HTML
function buildDetailsHtml(log) {
    var user = log.username || 'System';
    if (log.first_name || log.last_name) {
        user += ' (' + (log.first_name + ' ' + log.last_name).trim() + ')';
    }

    var html = '<div class="row">';
    html += '<div class="col-md-6"><strong>ID:</strong></div><div class="col-md-6">' + log.id + '</div>';
    html += '<div class="col-md-6"><strong>Date/Time:</strong></div><div class="col-md-6">' + log.created_at + '</div>';
    html += '<div class="col-md-6"><strong>User:</strong></div><div class="col-md-6">' + user + '</div>';
    html += '<div class="col-md-6"><strong>Event:</strong></div><div class="col-md-6"><span class="badge badge-primary">' + log.event + '</span></div>';
    html += '<div class="col-md-6"><strong>Entity Type:</strong></div><div class="col-md-6">' + (log.auditable_type || 'N/A') + '</div>';
    html += '<div class="col-md-6"><strong>Entity ID:</strong></div><div class="col-md-6">' + (log.auditable_id || 'N/A') + '</div>';
    html += '<div class="col-md-6"><strong>IP Address:</strong></div><div class="col-md-6">' + (log.ip_address || 'N/A') + '</div>';
    html += '<div class="col-md-6"><strong>URL:</strong></div><div class="col-md-6"><small>' + (log.url || 'N/A') + '</small></div>';
    html += '<div class="col-md-6"><strong>Method:</strong></div><div class="col-md-6">' + (log.method || 'N/A') + '</div>';
    html += '<div class="col-md-6"><strong>User Agent:</strong></div><div class="col-md-6"><small>' + (log.user_agent || 'N/A') + '</small></div>';
    html += '</div>';

    if (log.old_values_parsed || log.new_values_parsed) {
        html += '<hr><h6>Data Changes:</h6>';
        if (log.old_values_parsed) {
            html += '<div class="mb-3"><strong>Old Values:</strong><pre class="bg-light p-2">' + JSON.stringify(log.old_values_parsed, null, 2) + '</pre></div>';
        }
        if (log.new_values_parsed) {
            html += '<div class="mb-3"><strong>New Values:</strong><pre class="bg-light p-2">' + JSON.stringify(log.new_values_parsed, null, 2) + '</pre></div>';
        }
    }

    return html;
}

// Show export modal
function showExportModal() {
    $('#exportModal').modal('show');
}

// Show cleanup modal
function showCleanupModal() {
    $('#cleanupModal').modal('show');
}

// Cleanup old logs
function cleanupOldLogs() {
    var days = $('#days_to_keep').val();

    Swal.fire({
        title: 'Are you sure?',
        text: 'This will permanently delete audit logs older than ' + days + ' days.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, cleanup!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= site_url('admin/audit/clean-old-logs') ?>',
                type: 'POST',
                data: {
                    <?= csrf_token() ?>: csrfHash,
                    days: days
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Success!', response.message, 'success');
                        $('#cleanupModal').modal('hide');
                        auditTable.ajax.reload();
                        updateStats();
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error!', 'Failed to cleanup old logs.', 'error');
                }
            });
        }
    });
}

// Update statistics
function updateStats() {
    $.ajax({
        url: '<?= site_url('admin/audit/stats') ?>',
        type: 'POST',
        data: {
            <?= csrf_token() ?>: csrfHash
        },
        success: function(response) {
            if (response.success) {
                $('#total-logs').text(response.data.total);
                $('#today-logs').text(response.data.today);
                $('#month-logs').text(response.data.this_month);
                $('#event-types').text(response.data.by_event.length);
            }
        }
    });
}

// Initialize charts
function initializeCharts() {
    <?php if (!empty($stats['by_event'])): ?>
    // Event Chart
    var eventCtx = document.getElementById('eventChart').getContext('2d');
    var eventChart = new Chart(eventCtx, {
        type: 'doughnut',
        data: {
            labels: [<?php foreach($stats['by_event'] as $event): ?>'<?= esc($event['event']) ?>',<?php endforeach; ?>],
            datasets: [{
                data: [<?php foreach($stats['by_event'] as $event): ?><?= $event['count'] ?>,<?php endforeach; ?>],
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8', '#fd7e14'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
                position: 'bottom'
            }
        }
    });
    <?php endif; ?>

    <?php if (!empty($stats['by_entity'])): ?>
    // Entity Chart
    var entityCtx = document.getElementById('entityChart').getContext('2d');
    var entityChart = new Chart(entityCtx, {
        type: 'bar',
        data: {
            labels: [<?php foreach($stats['by_entity'] as $entity): ?>'<?= esc($entity['auditable_type']) ?>',<?php endforeach; ?>],
            datasets: [{
                label: 'Count',
                data: [<?php foreach($stats['by_entity'] as $entity): ?><?= $entity['count'] ?>,<?php endforeach; ?>],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            },
            legend: {
                display: false
            }
        }
    });
    <?php endif; ?>
}

// CSRF token variables
var csrfName = '<?= csrf_token() ?>';
</script>
<?= $this->endSection() ?>