<?= $this->extend('layout/auth'); ?>

<?= $this->section('title'); ?><?= $title??'no title set';?><?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<div class="login-box">
    <div class="login-logo">
        <!-- System Logo -->
        <div class="text-center mb-2">
            <img src="<?= base_url('assets/dist/img/citem_logo_2023.png') ?>" alt="System Logo" class="img-fluid" style="max-height: 50px;">
        </div>
        <a href="<?= base_url() ?>"><b><?= $system_name??'no title set';?></b></a>
    </div>
    <div class="card">
        <div class="card-body login-card-body">
            <p class="login-box-msg">Sign in to start your session</p>

            <!-- display flash data message -->
            <?php if(session()->has('warning')): ?>
              <div class="alert alert-warning alert-dismissible"><?php echo session()->get('warning'); ?></div>
            <?php endif; ?>

            <?php
                if(session()->getFlashdata('success')) { ?>
                    <div class="alert alert-success alert-dismissible">                  
                        <?php echo session()->getFlashdata('success') ?>
                    </div>
                <?php } elseif(session()->getFlashdata('failed')) { ?>
                    <div class="alert alert-danger alert-dismissible">
                        <?php echo session()->getFlashdata('failed') ?>
                    </div>
            <?php } ?>

            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger">
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <?= form_open(route_to('auth.login'));?>
                <?= csrf_field() ?>
              <div class="input-group mb-3 has-validation">
                  <input type="text" class="form-control <?php echo isInvalid($loginField) ?>" name="<?= $loginField ?>" placeholder="<?= $loginLabel ?>" value="<?php echo old($loginField) ?>"/>          
                  <div class="input-group-append">
                    <div class="input-group-text">
                      <?php if($loginField === 'email'): ?>
                      <span class="fas fa-envelope"></span>
                      <?php else: ?>
                      <span class="fas fa-user"></span>
                      <?php endif; ?>
                    </div>
                  </div>
                      <div class="invalid-feedback">
                          <?php echo show_validation_error($loginField,session("errors"));?>
                      </div>                                
                </div>
                <div class="input-group mb-3">
                  <input id="password" type="password" class="form-control <?php echo isInvalid('password') ?>" name="password" placeholder="Password" value=""/>
                  <div class="input-group-append">
                    <div class="input-group-text">
                      <span id="eye" class="fas fa-eye"></span>
                    </div>
                  </div>
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('password',session("errors"));?>
                        </div>                                
                </div>


<!-- 
                <div class="row">
                    <div class="col-8">
                        <div class="icheck-primary">
                            <input type="checkbox" id="remember">
                            <label for="remember">
                                Remember Me
                            </label>
                        </div>
                    </div>
                    <div class="col-4">
                        <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                    </div>
                </div> -->

                <div class="row">
                  <!-- /.col -->
                  <div class="col-4">
                    <button type="submit" name="submit" value="1" class="btn btn-primary btn-block">Login</button>
                  </div>
                  <!-- /.col -->
                </div>
            </form>

            <br>
            <!-- <p class="mb-1">
              <a href="<?//= site_url().'auth/forgot_password' ?>">I forgot my password</a>
            </p> -->
            
            <!-- <p class="mb-0">
              <a href="register.html" class="text-center">Register a new membership</a>
            </p> -->
        </div>
        </div>
</div>
<!-- /.login-box -->
<?= $this->endSection(); ?>


<?= $this->section('script'); ?>

<script>
const passwordInput = document.querySelector("#password")
const eye = document.querySelector("#eye")

eye.addEventListener("click", function(){
  this.classList.toggle("fa-eye-slash")
  const type = passwordInput.getAttribute("type") === "password" ? "text" : "password"
  passwordInput.setAttribute("type", type)
})

$(document).ready(function(){
  $('#eventSector').change(function(){ 
     if (document.getElementById('eventSector').value === "") {
      //alert(myVar1);
      $('#eventProj').prop('disabled', true);
     }
     else {
      $('#eventProj').prop('disabled', false);
     }
  });
});

</script>

<?= $this->endSection(); ?>