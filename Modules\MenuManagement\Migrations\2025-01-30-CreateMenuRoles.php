<?php

namespace Modules\MenuManagement\Migrations;

use CodeIgniter\Database\Migration;

class CreateMenuRoles extends Migration
{
    public function up()
    {
        // Create menu_roles junction table for many-to-many relationship
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'menu_id' => [
                'type'     => 'INT',
                'unsigned' => true,
            ],
            'role_id' => [
                'type'     => 'INT',
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['menu_id', 'role_id'], false, true); // Unique constraint
        $this->forge->addKey('menu_id');
        $this->forge->addKey('role_id');
        
        // Create table with MyISAM engine (no foreign key constraints)
        $this->forge->createTable('menu_roles', true, ['ENGINE' => 'MyISAM']);

        // Migrate existing permission-based data to role-based data
        $this->migrateExistingData();
    }

    public function down()
    {
        $this->forge->dropTable('menu_roles');
    }

    /**
     * Migrate existing permission-based menu data to role-based system
     */
    private function migrateExistingData()
    {
        // Get all menus that have permission_id set
        $menusWithPermissions = $this->db->table('menus')
                                        ->select('id, permission_id')
                                        ->where('permission_id IS NOT NULL')
                                        ->where('permission_id >', 0)
                                        ->get()
                                        ->getResultArray();

        if (!empty($menusWithPermissions)) {
            $menuRolesData = [];
            
            foreach ($menusWithPermissions as $menu) {
                // Find all roles that have this permission
                $rolesWithPermission = $this->db->table('role_permissions')
                                                ->select('role_id')
                                                ->where('permission_id', $menu['permission_id'])
                                                ->get()
                                                ->getResultArray();

                // Create menu_role entries for each role that has the permission
                foreach ($rolesWithPermission as $rolePermission) {
                    $menuRolesData[] = [
                        'menu_id'    => $menu['id'],
                        'role_id'    => $rolePermission['role_id'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }
            }

            // Insert migrated data if any exists
            if (!empty($menuRolesData)) {
                $this->db->table('menu_roles')->insertBatch($menuRolesData);
            }
        }

        // Add backup column for permission_id before we potentially remove it later
        $this->forge->addColumn('menus', [
            'old_permission_id' => [
                'type'     => 'INT',
                'unsigned' => true,
                'null'     => true,
                'after'    => 'permission_id',
            ],
        ]);

        // Copy current permission_id to backup column
        $this->db->query('UPDATE menus SET old_permission_id = permission_id WHERE permission_id IS NOT NULL');
    }
}
