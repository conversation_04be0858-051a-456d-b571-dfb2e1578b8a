<?php

namespace Modules\RoleManagement\Models;

use CodeIgniter\Model;

class RoleModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false; // Roles are usually not soft deleted
    protected $protectFields    = true;
    protected $allowedFields = ['name', 'description', 'is_active'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // protected $validationRules    = [
    //     'name' => 'required|min_length[3]|max_length[50]|is_unique[roles.name,id,{id}]',
    // ];
    // protected $validationMessages = [];
    // protected $skipValidation     = false;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all permissions assigned to a specific role.
     * @param int $roleId
     * @return array
     */
    public function getPermissions(int $roleId)
    {
        return $this->db->table('role_permissions rp')
                        ->select('p.id, p.name, p.description, p.category') // Include category from permissions table
                        ->join('permissions p', 'p.id = rp.permission_id')
                        ->where('rp.role_id', $roleId)
                        ->get()
                        ->getResultArray();
    }

    /**
     * Assign permissions to a role.
     * @param int $roleId
     * @param array $permissionIds
     * @return bool
     */
    public function assignPermissions(int $roleId, array $permissionIds)
    {
        $rolePermissionModel = new RolePermissionModel();
        
        // First, remove all existing permissions for this role
        // This ensures that if permissions are unchecked, they are removed.
        $rolePermissionModel->where('role_id', $roleId)->delete();

        // Then, insert new permissions
        $data = [];
        foreach ($permissionIds as $permissionId) {
            $data[] = [
                'role_id'       => $roleId,
                'permission_id' => $permissionId,
            ];
        }

// print_r($data);exit();

        if (!empty($data)) {
            // Use insertBatch for efficiency when inserting multiple records
            return $rolePermissionModel->insertBatch($data);
        }
        return true; // No permissions to assign/remove, still a success
    }

    /**
     * Checks if a role can be deleted.
     * For MyISAM, this means checking for related records in pivot tables.
     * @param int $roleId
     * @return bool True if the role can be deleted, false otherwise.
     */
    public function canDelete(int $roleId): bool
    {
        // Check for associated user_roles
        $userRolesCount = $this->db->table('user_roles')
                                   ->where('role_id', $roleId)
                                   ->countAllResults();
        if ($userRolesCount > 0) {
            return false; // Role is assigned to users
        }

        // Check for associated role_permissions
        $rolePermissionsCount = $this->db->table('role_permissions')
                                         ->where('role_id', $roleId)
                                         ->countAllResults();
        if ($rolePermissionsCount > 0) {
            return false; // Role has assigned permissions
        }

        return true; // No associated records found, safe to delete
    }



    // public function getRolePermissions($roleId)
    // {
    //     return $this->db->table('role_permissions')
    //                     ->select('permission_id')
    //                     ->where('role_id', $roleId)
    //                     ->get()
    //                     ->getResultArray();
    // }

    // public function setRolePermissions($roleId, array $permissionIds)
    // {
    //     $this->db->table('role_permissions')->where('role_id', $roleId)->delete();
    //     if (!empty($permissionIds)) {
    //         $data = [];
    //         foreach ($permissionIds as $permissionId) {
    //             $data[] = ['role_id' => $roleId, 'permission_id' => $permissionId];
    //         }
    //         //insert batch but return true if successful
    //         $this->db->table('role_permissions')->insertBatch($data);
    //     }
    // }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics()
    {
        $total = $this->countAll();

        // Count roles that have at least one permission
        $withPermissions = $this->db->table('roles r')
                                   ->select('r.id')
                                   ->join('role_permissions rp', 'rp.role_id = r.id')
                                   ->distinct()
                                   ->countAllResults();

        $withoutPermissions = $total - $withPermissions;

        return [
            'total' => $total,
            'with_permissions' => $withPermissions,
            'without_permissions' => $withoutPermissions,
        ];
    }

    // /**
    //  * Get role with permissions
    //  */
    // public function getRoleWithPermissions($id)
    // {
    //     $role = $this->find($id);
    //     if ($role) {
    //         $permissions = $this->db->table('role_permissions rp')
    //                                ->select('p.name, p.description')
    //                                ->join('permissions p', 'p.id = rp.permission_id')
    //                                ->where('rp.role_id', $id)
    //                                ->get()
    //                                ->getResultArray();
    //         $role['permissions'] = $permissions;
    //     }
    //     return $role;
    // }

    // /**
    //  * Get user count by role
    //  */
    public function getUserCountByRole($roleId)
    {
        return $this->db->table('user_roles')
                       ->where('role_id', $roleId)
                       ->where('is_active', 1)
                       ->countAllResults();
    }

    // /**
    //  * Get users assigned to a role
    //  */
    // public function getUsersByRole($roleId)
    // {
    //     return $this->db->table('user_roles ur')
    //                    ->select('u.*, ur.assigned_at, ur.expires_at')
    //                    ->join('users u', 'u.id = ur.user_id')
    //                    ->where('ur.role_id', $roleId)
    //                    ->where('ur.is_active', 1)
    //                    ->orderBy('ur.assigned_at', 'DESC')
    //                    ->get()
    //                    ->getResultArray();
    // }

    /**
     * Bulk delete roles
     */
    public function bulkDelete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return ['error' => 'No roles selected'];
        }

        // Check if any roles are being used by users
        $userCount = $this->db->table('user_roles')
                             ->whereIn('role_id', $ids)
                             ->where('is_active', 1)
                             ->countAllResults();

        if ($userCount > 0) {
            return ['error' => "Cannot delete roles. They are assigned to $userCount user(s)."];
        }

        try {
            // Delete role permissions first
            $this->db->table('role_permissions')->whereIn('role_id', $ids)->delete();
            // Delete user role assignments
            $this->db->table('user_roles')->whereIn('role_id', $ids)->delete();
            // Delete roles
            $this->whereIn('id', $ids)->delete();
            return ['success' => true];
        } catch (\Exception $e) {
            return ['error' => 'Failed to delete roles: ' . $e->getMessage()];
        }
    }

    /**
     * Bulk update status for roles
     */
    public function bulkUpdateStatus($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        try {
            $this->whereIn('id', $ids)->set(['is_active' => $status])->update();
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Error updating role status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get active roles for dropdowns
     */
    public function getActiveRoles()
    {
        return $this->where('is_active', 1)->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Get user roles for session
     */
    public function getUserRoleNames($userId)
    {
        return $this->db->table('user_roles ur')
                       ->select('r.name')
                       ->join('roles r', 'r.id = ur.role_id')
                       ->where('ur.user_id', $userId)
                       ->where('ur.is_active', 1)
                       ->where('(ur.expires_at IS NULL OR ur.expires_at > NOW())')
                       ->get()
                       ->getResultArray();
    }


}
