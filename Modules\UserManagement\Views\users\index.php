<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">User Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-4 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?= $statistics['total'] ?></h3>
                <p>Total Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?= $statistics['active'] ?></h3>
                <p>Active Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?= $statistics['inactive'] ?></h3>
                <p>Inactive Users</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-times"></i>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">User Management</h3>
        <div class="card-tools">
            <!-- <?php if (hasPermission('user.manage')) : ?> -->
                <a href="<?= route_to('users.create') ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New User
                </a>
            <!-- <?php endif; ?> -->
        </div>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <!-- <?php if (hasPermission('user.manage')) : ?> -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('activate', 'This will activate all selected users.')">
                        <i class="fas fa-check"></i> Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('deactivate', 'This will deactivate all selected users.')">
                        <i class="fas fa-times"></i> Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete', 'This will permanently delete all selected users.')">
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
        </div>
        <!-- <?php endif; ?> -->

        <div class="table-responsive">
            <table id="usersTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <!-- <?php if (hasPermission('user.manage')) : ?> -->
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <!-- <?php endif; ?> -->
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Dept/Div.</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">User Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables  & Plugins -->
<script src="<?= base_url("assets/plugins/datatables/jquery.dataTables.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/jszip/jszip.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/pdfmake.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/vfs_fonts.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js") ?>"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var userTable = $('#usersTable').DataTable({
        processing: true,
        searching: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        // buttons: ["copy", "csv", "excel", "pdf", "print"],
        ajax: {
            url: '<?= site_url('admin/users/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[2, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            <?php if (hasPermission('user.manage')) : ?>
            { data: 'checkbox', orderable: false, searchable: false },
            <?php endif; ?>
            { data: 'username' },
            { data: 'email' },
            { data: 'role_names', createdCell: function(td) {
                $(td).addClass('wrap-text');
            }},
            // ,{ data: 'role_badges', orderable: false, searchable: false }
            { data: 'office_code' },
            { data: 'status_badge', orderable: false, searchable: false },
            { data: 'created_date' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // Add buttons to DataTable
    userTable.buttons().container().appendTo('#usersTable_wrapper .col-md-6:eq(0)');

    // Select all checkbox functionality
    $('#select-all').on('click', function() {
        var rows = userTable.rows({ 'search': 'applied' }).nodes();
        $('input[type="checkbox"]', rows).prop('checked', this.checked);
    });

    // Handle individual checkbox clicks
    $('#usersTable tbody').on('change', 'input[type="checkbox"]', function() {
        if (!this.checked) {
            var el = $('#select-all').get(0);
            if (el && el.checked && ('indeterminate' in el)) {
                el.indeterminate = true;
            }
        }
    });

    // Delete user functionality
    $(document).on('click', '#deleteUser', function() {
        var userId = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= route_to('users.ajax_delete') ?>',
                    type: 'POST',
                    data: {
                        id: userId,
                        [csrfName]: csrfHash
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Deleted!', response.message, 'success');
                            userTable.ajax.reload();
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                        // Update CSRF token
                        csrfHash = response.csrfHash || csrfHash;
                    },
                    error: function() {
                        Swal.fire('Error!', 'Something went wrong!', 'error');
                    }
                });
            }
        });
    });

    // Auto-dismiss alerts after 5 seconds
    // setTimeout(function() {
    //     $('.alert').fadeOut('slow');
    // }, 5000);
    window.setTimeout(function() {
        $('.alert').fadeTo(500, 0).slideUp(500, function() {
            $(this).remove();
        });
    }, 5000);
});

// View user details
function viewUser(id) {
    $.ajax({
        url: '<?= site_url('admin/users/show') ?>/' + id,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var user = response.user;
                var modalBody = `
                    <div class="row">
                        <div class="col-md-6"><strong>Username:</strong></div>
                        <div class="col-md-6">${user.username}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Email:</strong></div>
                        <div class="col-md-6">${user.email}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Name:</strong></div>
                        <div class="col-md-6">${user.first_name || 'N/A'} ${user.last_name || 'N/A'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Role:</strong></div>
                        <div class="col-md-6">${user.role_name || 'N/A'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Office:</strong></div>
                        <div class="col-md-6">${user.office_name || 'N/A'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Status:</strong></div>
                        <div class="col-md-6">
                            ${user.is_active == 1 ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-danger">Inactive</span>'}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Created:</strong></div>
                        <div class="col-md-6">${new Date(user.created_at).toLocaleDateString()}</div>
                    </div>
                `;
                $('#userModalBody').html(modalBody);
                $('#userModal').modal('show');
            } else {
                Swal.fire('Error!', response.error, 'error');
            }
        },
        error: function() {
            Swal.fire('Error!', 'Failed to load user details!', 'error');
        }
    });
}

function bulkAction(action, message) {
    var selectedIds = [];
    $('.select-item:checked').each(function() {
        selectedIds.push($(this).val());
    });

    if (selectedIds.length === 0) {
        alert('Please select at least one user.');
        return;
    }

    Swal.fire({
        title: 'Are you sure?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, proceed!'
    }).then((result) => {
        if (result.isConfirmed) {
            var url = '';
            switch(action) {
                case 'activate':
                    url = '<?= route_to('users.bulk_activate') ?>';
                    break;
                case 'deactivate':
                    url = '<?= route_to('users.bulk_deactivate') ?>';
                    break;
                case 'delete':
                    url = '<?= route_to('users.bulk_delete') ?>';
                    break;
            }

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    ids: selectedIds,
                    [csrfName]: csrfHash
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Success!', response.message, 'success');
                        $('#usersTable').DataTable().ajax.reload();
                        $('#select-all').prop('checked', false);
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                    // Update CSRF token
                    csrfHash = response.csrfHash || csrfHash;
                },
                error: function() {
                    Swal.fire('Error!', 'Something went wrong!', 'error');
                }
            });
        }
    });
}
// CSRF token variables
var csrfName = '<?= csrf_token() ?>';
var csrfHash = '<?= csrf_hash() ?>';
</script>
<?= $this->endSection() ?>