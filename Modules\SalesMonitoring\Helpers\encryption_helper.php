<?php

use Config\Services;

if (!function_exists('encrypt_data')){
    function encrypt_data($data){
        $key = env('myencryption.key'); // Retrieve the key from the .env file
        $method = "AES-256-CBC";
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method));
        $encrypted = openssl_encrypt($data, $method, $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
}

if (!function_exists('decrypt_data')){
    function decrypt_data($data){
        $key = env('myencryption.key'); // Retrieve the key from the .env file
        $method = "AES-256-CBC";
        $data = base64_decode($data);
        $iv_length = openssl_cipher_iv_length($method);
        $iv = substr($data, 0, $iv_length);
        $encrypted = substr($data, $iv_length);
        return openssl_decrypt($encrypted, $method, $key, 0, $iv);
    }
}


// Example Usage:
//$key = generate_secure_key(32); // Generates a 64-character hex string
if (!function_exists('generate_secure_key')) {
    function generate_secure_key($length = 32){
        // Generate a random secure key
        return bin2hex(random_bytes($length));
    }
}