<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;
use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class SaleUpdatesModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'e_sales_updates';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = \App\Entities\SaleUpdate::class;
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = ['sales_id','updated_cost','updated_status'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'date_added';
    protected $updatedField  = 'date_modified';
    protected $deletedField  = 'deleted_at';


    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];


    protected $updatesFields    = [
        'a.sales_id','b.minor_prod_desc', 'b.buyer_name', 'b.cost','b.type', 'a.updated_cost', 'b.status', 'a.updated_status', 'b.sale_date AS original_date', 'a.date_modified'
    ];

    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }


    public function getUpdateDetailsByExh($ff_code){
        try{
            $builder = $this->db->table($this->table.' a')->select($this->updatesFields)
                ->join('e_sales as b', 'a.sales_id = b.sales_id', 'left')->orderBy('b.sale_date')
                ->where('ff_code',$ff_code);
            $query = $builder->get();
            $result = $query->getResultArray();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $sql = str_replace("\n", " ", $sql);
            // echo $sql;exit();
            return $result;
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }


    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }





}
