<?php

namespace Modules\CinAuthentication\Migrations;

use CodeIgniter\Database\Migration;

class CreateUsers extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'username' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
            ],
            'email' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'unique' => true,
            ],
            'password' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            // 'role' => [
            //     'type' => 'VARCHAR',
            //     'constraint' => '50',
            //     'default' => 'user',
            // ],
            'role_id' => [
                'type' => 'TINYINT',
                'constraint' => '1',
                'default' => 2,
            ],
            'sector' => [
                'type' => 'VARCHAR',
                'constraint' => '20',
                'null' => true,
            ],
            'rights' => [
                'type' => 'TINYINT',
                'constraint' => '1',
                'default' => 0,
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'designation' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'division' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            // 'status' => [
            //     'type' => 'ENUM',
            //     'constraint' => ['active', 'inactive', 'suspended'],
            //     'default' => 'active',
            // ],
            'status' => [
                'type' => 'TINYINT',
                'constraint' => '1',
                'default' => 1,
            ],
            'default_type' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => true,
            ],
            'appid' => [
                'type' => 'VARCHAR',
                'constraint' => '10',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'reset_token' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->createTable('users');
    }

    public function down()
    {
        $this->forge->dropTable('users');
    }
}


// php spark migrate --to 2025-01-01-CreateUsers --group default