<?php

namespace Modules\RoleManagement\Config;


$routes->group('admin', function($routes) {
    $routes->group('roles', [
        'namespace' => 'Modules\RoleManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Debug route
        $routes->GET('debug', 'RoleController::debug', ['as' => 'roles.debug']);

        // Main CRUD routes
        $routes->GET('/', 'RoleController::index', ['as' => 'roles.index']);
        $routes->GET('create', 'RoleController::create', ['as' => 'roles.create']);
        $routes->POST('store', 'RoleController::store', ['as' => 'roles.store']);
        $routes->GET('edit/(:segment)', 'RoleController::edit/$1', ['as' => 'roles.edit']);
        $routes->POST('update/(:segment)', 'RoleController::update/$1', ['as' => 'roles.update']);
        $routes->GET('delete/(:segment)', 'RoleController::delete/$1', ['as' => 'roles.delete']);
        $routes->GET('assign-permissions/(:segment)', 'RoleController::assignPermissions/$1', ['as' => 'roles.assign_permissions']);
        $routes->POST('save-permissions/(:segment)', 'RoleController::savePermissions/$1', ['as' => 'roles.save_permissions']);

        // AJAX endpoints for DataTables
        $routes->POST('datatable', 'RoleController::datatable', ['as' => 'roles.datatable']);
        $routes->POST('ajax-delete', 'RoleController::ajaxDelete', ['as' => 'roles.ajax_delete']);
        $routes->GET('show/(:segment)', 'RoleController::show/$1', ['as' => 'roles.show']);

        // Bulk operations
        $routes->POST('bulk-delete', 'RoleController::bulkDelete', ['as' => 'roles.bulk_delete']);
        $routes->POST('bulk-activate', 'RoleController::bulkActivate', ['as' => 'roles.bulk_activate']);
        $routes->POST('bulk-deactivate', 'RoleController::bulkDeactivate', ['as' => 'roles.bulk_deactivate']);
    });

    $routes->group('permissions', [
        'namespace' => 'Modules\RoleManagement\Controllers', 
        // 'filter' => 'permission:permission.manage',
    ], function ($routes) {
        $routes->GET('/', 'PermissionController::index', ['as' => 'permission.index']);
        $routes->GET('create', 'PermissionController::create', ['as' => 'permission.create']);
        $routes->POST('store', 'PermissionController::store', ['as' => 'permission.store']);
        $routes->GET('edit/(:num)', 'PermissionController::edit/$1', ['as' => 'permission.edit']);
        $routes->POST('update/(:num)', 'PermissionController::update/$1', ['as' => 'permission.update']);
        $routes->GET('delete/(:num)', 'PermissionController::delete/$1', ['as' => 'permission.delete']);
        $routes->GET('stats', 'PermissionController::stats', ['as' => 'permission.stats']);

        // AJAX endpoints for DataTables
        $routes->POST('datatable', 'PermissionController::datatable', ['as' => 'permission.datatable']);
        $routes->POST('ajax-delete', 'PermissionController::ajaxDelete', ['as' => 'permission.ajax_delete']);
        $routes->GET('show/(:segment)', 'PermissionController::show/$1', ['as' => 'permission.show']);

        // Bulk operations
        $routes->POST('bulk-delete', 'PermissionController::bulkDelete', ['as' => 'permission.bulk_delete']);
        $routes->POST('bulk-activate', 'PermissionController::bulkActivate', ['as' => 'permission.bulk_activate']);
        $routes->POST('bulk-deactivate', 'PermissionController::bulkDeactivate', ['as' => 'permission.bulk_deactivate']);
    });

    // Role Management Dashboard
    $routes->GET('role-dashboard', 'UserRoleController::dashboard', ['as' => 'role.dashboard']);

    // User Role Assignment Routes (with expiration functionality)
    $routes->group('users', [
        'namespace' => 'Modules\RoleManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Role assignment routes
        $routes->GET('assign-roles/(:num)', 'UserRoleController::assignRoles/$1', ['as' => 'users.assign_roles']);
        $routes->POST('assign-roles/(:num)/save', 'UserRoleController::saveRoles/$1', ['as' => 'users.save_roles']);
        $routes->POST('save-roles/(:num)', 'UserRoleController::saveRoles/$1', ['as' => 'users.save_roles_alt']); // Alternative route

        // Role expiration management routes
        $routes->GET('expired-roles/(:num)', 'UserRoleController::viewExpiredRoles/$1', ['as' => 'users.expired_roles']);
        $routes->GET('extend-role-expiration/(:num)/(:num)', 'UserRoleController::extendRoleExpiration/$1/$2', ['as' => 'users.extend_role_expiration']);
        $routes->POST('extend-role-expiration/(:num)/(:num)', 'UserRoleController::extendRoleExpiration/$1/$2', ['as' => 'users.extend_role_expiration_save']);

        // Additional utility routes
        $routes->POST('cleanup-expired-roles/(:num)', 'UserRoleController::cleanupExpiredRoles/$1', ['as' => 'users.cleanup_expired_roles']);
        $routes->GET('role-history/(:num)/(:num)', 'UserRoleController::roleHistory/$1/$2', ['as' => 'users.role_history']);
    });
});
