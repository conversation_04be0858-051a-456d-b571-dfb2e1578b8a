<?php

/**
 * Tabler Helper Functions
 * Provides helper functions for Tabler.io UI components
 */

if (!function_exists('tabler_page_header')) {
    /**
     * Generate a Tabler page header
     *
     * @param string $title
     * @param array $options
     * @return string
     */
    function tabler_page_header(string $title, array $options = []): string
    {
        $pretitle = $options['pretitle'] ?? '';
        $actions = $options['actions'] ?? '';
        $breadcrumb = $options['breadcrumb'] ?? [];

        $html = '<div class="page-header d-print-none">';
        $html .= '<div class="container-xl">';
        $html .= '<div class="row g-2 align-items-center">';
        $html .= '<div class="col">';
        
        // Breadcrumb
        if (!empty($breadcrumb)) {
            $html .= '<div class="page-pretitle">';
            $html .= '<nav aria-label="breadcrumb">';
            $html .= '<ol class="breadcrumb">';
            foreach ($breadcrumb as $item) {
                if (isset($item['url'])) {
                    $html .= '<li class="breadcrumb-item"><a href="' . esc($item['url']) . '">' . esc($item['text']) . '</a></li>';
                } else {
                    $html .= '<li class="breadcrumb-item active" aria-current="page">' . esc($item['text']) . '</li>';
                }
            }
            $html .= '</ol>';
            $html .= '</nav>';
            $html .= '</div>';
        } elseif ($pretitle) {
            $html .= '<div class="page-pretitle">' . esc($pretitle) . '</div>';
        }
        
        $html .= '<h2 class="page-title">' . esc($title) . '</h2>';
        $html .= '</div>';
        
        // Actions
        if ($actions) {
            $html .= '<div class="col-auto ms-auto d-print-none">';
            $html .= '<div class="btn-list">';
            $html .= $actions;
            $html .= '</div>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}

if (!function_exists('tabler_button')) {
    /**
     * Generate a Tabler button
     *
     * @param string $text
     * @param string $variant
     * @param array $options
     * @return string
     */
    function tabler_button(string $text, string $variant = 'primary', array $options = []): string
    {
        $icon = $options['icon'] ?? '';
        $href = $options['href'] ?? '';
        $size = $options['size'] ?? '';
        $class = $options['class'] ?? '';
        $id = $options['id'] ?? '';
        $onclick = $options['onclick'] ?? '';
        $disabled = $options['disabled'] ?? false;

        $classes = ['btn', 'btn-' . $variant];
        
        if ($size) {
            $classes[] = 'btn-' . $size;
        }
        
        if ($class) {
            $classes[] = $class;
        }
        
        if ($disabled) {
            $classes[] = 'disabled';
        }

        $attributes = [];
        $attributes[] = 'class="' . implode(' ', $classes) . '"';
        
        if ($id) {
            $attributes[] = 'id="' . esc($id) . '"';
        }
        
        if ($onclick) {
            $attributes[] = 'onclick="' . esc($onclick) . '"';
        }
        
        if ($disabled) {
            $attributes[] = 'disabled';
        }

        $iconHtml = '';
        if ($icon) {
            $iconHtml = '<i class="' . esc($icon) . ' me-1"></i>';
        }

        if ($href) {
            $attributes[] = 'href="' . esc($href) . '"';
            return '<a ' . implode(' ', $attributes) . '>' . $iconHtml . esc($text) . '</a>';
        } else {
            return '<button ' . implode(' ', $attributes) . '>' . $iconHtml . esc($text) . '</button>';
        }
    }
}

if (!function_exists('tabler_flash_messages')) {
    /**
     * Display flash messages in Tabler format
     *
     * @return string
     */
    function tabler_flash_messages(): string
    {
        $html = '';
        $session = session();

        // Success messages
        if ($session->getFlashdata('success')) {
            $html .= '<div class="alert alert-success alert-dismissible" role="alert">';
            $html .= '<div class="d-flex">';
            $html .= '<div><i class="ti ti-check icon alert-icon"></i></div>';
            $html .= '<div>' . esc($session->getFlashdata('success')) . '</div>';
            $html .= '</div>';
            $html .= '<a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>';
            $html .= '</div>';
        }

        // Error messages
        if ($session->getFlashdata('error')) {
            $html .= '<div class="alert alert-danger alert-dismissible" role="alert">';
            $html .= '<div class="d-flex">';
            $html .= '<div><i class="ti ti-alert-circle icon alert-icon"></i></div>';
            $html .= '<div>' . esc($session->getFlashdata('error')) . '</div>';
            $html .= '</div>';
            $html .= '<a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>';
            $html .= '</div>';
        }

        // Warning messages
        if ($session->getFlashdata('warning')) {
            $html .= '<div class="alert alert-warning alert-dismissible" role="alert">';
            $html .= '<div class="d-flex">';
            $html .= '<div><i class="ti ti-alert-triangle icon alert-icon"></i></div>';
            $html .= '<div>' . esc($session->getFlashdata('warning')) . '</div>';
            $html .= '</div>';
            $html .= '<a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>';
            $html .= '</div>';
        }

        // Info messages
        if ($session->getFlashdata('info')) {
            $html .= '<div class="alert alert-info alert-dismissible" role="alert">';
            $html .= '<div class="d-flex">';
            $html .= '<div><i class="ti ti-info-circle icon alert-icon"></i></div>';
            $html .= '<div>' . esc($session->getFlashdata('info')) . '</div>';
            $html .= '</div>';
            $html .= '<a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>';
            $html .= '</div>';
        }

        return $html;
    }
}

if (!function_exists('tabler_modal')) {
    /**
     * Generate a Tabler modal
     *
     * @param string $id
     * @param string $title
     * @param string $content
     * @param array $options
     * @return string
     */
    function tabler_modal(string $id, string $title, string $content, array $options = []): string
    {
        $size = $options['size'] ?? '';
        $footer = $options['footer'] ?? '';

        $modalClass = 'modal-dialog modal-dialog-centered';
        if ($size) {
            $modalClass .= ' modal-' . $size;
        }

        $html = '<div class="modal modal-blur fade" id="' . esc($id) . '" tabindex="-1" role="dialog" aria-hidden="true">';
        $html .= '<div class="' . $modalClass . '" role="document">';
        $html .= '<div class="modal-content">';
        $html .= '<div class="modal-header">';
        $html .= '<h5 class="modal-title">' . esc($title) . '</h5>';
        $html .= '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>';
        $html .= '</div>';
        $html .= '<div class="modal-body">';
        $html .= $content;
        $html .= '</div>';
        
        if ($footer) {
            $html .= '<div class="modal-footer">';
            $html .= $footer;
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}

if (!function_exists('renderTablerSidebarMenu')) {
    /**
     * Render sidebar menu for Tabler layout
     */
    function renderTablerSidebarMenu($userId = null) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            $menus = $menuService->getUserMenus($userId);
            return buildTablerMenuHtml($menus);
        } catch (Exception $e) {
            // Log error and return empty string
            log_message('error', 'renderTablerSidebarMenu error: ' . $e->getMessage());
            return '';
        }
    }
}

if (!function_exists('buildTablerMenuHtml')) {
    /**
     * Build Tabler-style HTML for menu items
     */
    function buildTablerMenuHtml($menus, $level = 0) {
        if (empty($menus)) {
            return '';
        }

        $html = '';
        $currentUrl = uri_string();

        foreach ($menus as $menu) {
            $hasChildren = isset($menu['children']) && !empty($menu['children']);
            $isActive = isTablerMenuActive($menu['url'], $currentUrl);
            $hasActiveChild = $hasChildren ? hasTablerActiveChild($menu['children'], $currentUrl) : false;
            $shouldBeOpen = $isActive || $hasActiveChild;

            if ($hasChildren) {
                // Parent menu with children (dropdown)
                $html .= '<li class="nav-item dropdown' . ($shouldBeOpen ? ' active' : '') . '">';
                $html .= '<a class="nav-link dropdown-toggle" href="#navbar-' . $menu['id'] . '" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="' . ($shouldBeOpen ? 'true' : 'false') . '">';

                if ($menu['icon']) {
                    $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                    if (strpos($menu['icon'], 'ti ti-') === 0) {
                        // Convert Tabler icon class to SVG
                        $html .= getTablerIconSvg($menu['icon']);
                    } else {
                        // Use as-is for other icon formats
                        $html .= '<i class="' . $menu['icon'] . '"></i>';
                    }
                    $html .= '</span>';
                }

                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';

                $html .= '<div class="dropdown-menu' . ($shouldBeOpen ? ' show' : '') . '">';
                $html .= '<div class="dropdown-menu-columns">';
                $html .= '<div class="dropdown-menu-column">';

                foreach ($menu['children'] as $child) {
                    $childUrl = $child['url'] ? base_url($child['url']) : '#';
                    $childActive = isTablerMenuActive($child['url'], $currentUrl);
                    $target = !empty($child['target']) ? ' target="' . $child['target'] . '"' : '';
                    $hasGrandChildren = isset($child['children']) && !empty($child['children']);

                    if ($hasGrandChildren) {
                        // Child with grandchildren (3rd level) - use dropend
                        $html .= '<div class="dropend">';
                        $html .= '<a class="dropdown-item dropdown-toggle' . ($childActive ? ' active' : '') . '" href="#sidebar-' . $child['id'] . '" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">';
                        if ($child['icon']) {
                            if (strpos($child['icon'], 'ti ti-') === 0) {
                                $html .= str_replace('class="icon"', 'class="icon me-2"', getTablerIconSvg($child['icon']));
                            } else {
                                $html .= '<i class="' . $child['icon'] . ' me-2"></i>';
                            }
                        }
                        $html .= esc($child['label']);
                        $html .= '</a>';

                        // Grandchildren dropdown
                        $html .= '<div class="dropdown-menu">';
                        foreach ($child['children'] as $grandchild) {
                            $grandchildUrl = $grandchild['url'] ? base_url($grandchild['url']) : '#';
                            $grandchildActive = isTablerMenuActive($grandchild['url'], $currentUrl);
                            $grandchildTarget = !empty($grandchild['target']) ? ' target="' . $grandchild['target'] . '"' : '';

                            $html .= '<a class="dropdown-item' . ($grandchildActive ? ' active' : '') . '" href="' . $grandchildUrl . '"' . $grandchildTarget . '>';
                            if ($grandchild['icon']) {
                                if (strpos($grandchild['icon'], 'ti ti-') === 0) {
                                    $html .= str_replace('class="icon"', 'class="icon me-2"', getTablerIconSvg($grandchild['icon']));
                                } else {
                                    $html .= '<i class="' . $grandchild['icon'] . ' me-2"></i>';
                                }
                            }
                            $html .= esc($grandchild['label']);
                            $html .= '</a>';
                        }
                        $html .= '</div>';
                        $html .= '</div>';
                    } else {
                        // Regular child item (2nd level)
                        $html .= '<a class="dropdown-item' . ($childActive ? ' active' : '') . '" href="' . $childUrl . '"' . $target . '>';
                        if ($child['icon']) {
                            if (strpos($child['icon'], 'ti ti-') === 0) {
                                $html .= str_replace('class="icon"', 'class="icon me-2"', getTablerIconSvg($child['icon']));
                            } else {
                                $html .= '<i class="' . $child['icon'] . ' me-2"></i>';
                            }
                        }
                        $html .= esc($child['label']);
                        $html .= '</a>';
                    }
                }

                $html .= '</div>';
                $html .= '</div>';
                $html .= '</div>';
                $html .= '</li>';
            } else {
                // Single menu item
                $url = $menu['url'] ? base_url($menu['url']) : '#';
                $target = !empty($menu['target']) ? ' target="' . $menu['target'] . '"' : '';

                $html .= '<li class="nav-item">';
                $html .= '<a class="nav-link' . ($isActive ? ' active' : '') . '" href="' . $url . '"' . $target . '>';

                if ($menu['icon']) {
                    $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                    if (strpos($menu['icon'], 'ti ti-') === 0) {
                        $html .= getTablerIconSvg($menu['icon']);
                    } else {
                        $html .= '<i class="' . $menu['icon'] . '"></i>';
                    }
                    $html .= '</span>';
                }

                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';
                $html .= '</li>';
            }
        }

        return $html;
    }
}

if (!function_exists('renderTablerTopNavMenu')) {
    /**
     * Render top navigation menu for Tabler layout
     */
    function renderTablerTopNavMenu($userId = null) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            return $menuService->renderTopNavigationMenu($userId);
        } catch (Exception $e) {
            // Log error and return empty string
            log_message('error', 'renderTablerTopNavMenu error: ' . $e->getMessage());
            return '';
        }
    }
}

if (!function_exists('buildTablerTopNavHtml')) {
    /**
     * Build Tabler-style HTML for top navigation menu items
     */
    function buildTablerTopNavHtml($menus) {
        if (empty($menus)) {
            return '';
        }

        $html = '';
        $currentUrl = uri_string();

        foreach ($menus as $menu) {
            $hasChildren = isset($menu['children']) && !empty($menu['children']);
            $isActive = isTablerMenuActive($menu['url'], $currentUrl);
            $hasActiveChild = $hasChildren ? hasTablerActiveChild($menu['children'], $currentUrl) : false;
            $shouldBeActive = $isActive || $hasActiveChild;

            if ($hasChildren) {
                // Parent menu with dropdown
                $html .= '<li class="nav-item dropdown">';
                $html .= '<a class="nav-link dropdown-toggle' . ($shouldBeActive ? ' active' : '') . '" href="#" data-bs-toggle="dropdown" role="button" aria-expanded="false">';

                if ($menu['icon']) {
                    $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                    if (strpos($menu['icon'], 'ti ti-') === 0) {
                        $html .= getTablerIconSvg($menu['icon']);
                    } else {
                        $html .= '<i class="' . $menu['icon'] . '"></i>';
                    }
                    $html .= '</span>';
                }

                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';

                $html .= '<div class="dropdown-menu dropdown-menu-arrow">';
                foreach ($menu['children'] as $child) {
                    $childUrl = $child['url'] ? base_url($child['url']) : '#';
                    $childTarget = !empty($child['target']) ? ' target="' . $child['target'] . '"' : '';
                    $childActive = isTablerMenuActive($child['url'], $currentUrl) ? ' active' : '';

                    $html .= '<a class="dropdown-item' . $childActive . '" href="' . $childUrl . '"' . $childTarget . '>';
                    if ($child['icon']) {
                        if (strpos($child['icon'], 'ti ti-') === 0) {
                            $html .= getTablerIconSvg($child['icon']) . ' ';
                        } else {
                            $html .= '<i class="' . $child['icon'] . ' me-2"></i>';
                        }
                    }
                    $html .= esc($child['label']);
                    $html .= '</a>';
                }
                $html .= '</div>';
                $html .= '</li>';
            } else {
                // Single menu item
                $url = $menu['url'] ? base_url($menu['url']) : '#';
                $target = !empty($menu['target']) ? ' target="' . $menu['target'] . '"' : '';

                $html .= '<li class="nav-item">';
                $html .= '<a class="nav-link' . ($isActive ? ' active' : '') . '" href="' . $url . '"' . $target . '>';

                if ($menu['icon']) {
                    $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                    if (strpos($menu['icon'], 'ti ti-') === 0) {
                        $html .= getTablerIconSvg($menu['icon']);
                    } else {
                        $html .= '<i class="' . $menu['icon'] . '"></i>';
                    }
                    $html .= '</span>';
                }

                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';
                $html .= '</li>';
            }
        }

        return $html;
    }
}

if (!function_exists('renderTablerTopNavbar')) {
    /**
     * Render complete top navbar with navigation menu
     */
    function renderTablerTopNavbar($options = []) {
        $defaults = [
            'brand_text' => 'Tabler',
            'brand_url' => base_url(),
            'brand_image' => 'https://tabler.io/static/logo.svg',
            'user_name' => session()->get('username') ?? 'User',
            'user_role' => session()->get('user_role') ?? 'Member',
            'user_avatar' => session()->get('user_avatar') ?? 'https://ui-avatars.com/api/?name=' . urlencode(session()->get('username') ?? 'User'),
            'show_user_dropdown' => true,
            'container_class' => 'container-xl'
        ];

        $config = array_merge($defaults, $options);

        $html = '<header class="navbar navbar-expand-md navbar-dark d-print-none">';
        $html .= '<div class="' . $config['container_class'] . '">';

        // Mobile toggle button
        $html .= '<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">';
        $html .= '<span class="navbar-toggler-icon"></span>';
        $html .= '</button>';

        // Brand
        $html .= '<a href="' . $config['brand_url'] . '" class="navbar-brand navbar-brand-autodark me-3">';
        if ($config['brand_image']) {
            $html .= '<img src="' . $config['brand_image'] . '" width="110" height="32" alt="' . $config['brand_text'] . '" class="navbar-brand-image">';
        } else {
            $html .= $config['brand_text'];
        }
        $html .= '</a>';

        // Navigation menu
        $html .= '<div class="collapse navbar-collapse" id="navbar-menu">';
        $html .= '<ul class="navbar-nav">';
        $html .= renderTablerTopNavMenu();
        $html .= '</ul>';
        $html .= '</div>';

        // User dropdown (right side)
        if ($config['show_user_dropdown']) {
            $html .= '<div class="navbar-nav flex-row order-md-last ms-auto">';
            $html .= '<div class="nav-item dropdown">';
            $html .= '<a href="#" class="nav-link d-flex lh-1 text-reset" data-bs-toggle="dropdown" aria-label="Open user menu">';
            $html .= '<span class="avatar avatar-sm" style="background-image: url(' . $config['user_avatar'] . ')"></span>';
            $html .= '<div class="d-none d-xl-block ps-2">';
            $html .= '<div>' . esc($config['user_name']) . '</div>';
            $html .= '<div class="mt-1 small text-secondary">' . esc($config['user_role']) . '</div>';
            $html .= '</div>';
            $html .= '</a>';
            $html .= '<div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">';
            $html .= '<a href="' . base_url('profile') . '" class="dropdown-item">Profile</a>';
            $html .= '<a href="' . base_url('settings') . '" class="dropdown-item">Settings</a>';
            $html .= '<div class="dropdown-divider"></div>';
            $html .= '<a href="' . base_url('logout') . '" class="dropdown-item">Logout</a>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</header>';

        return $html;
    }
}

if (!function_exists('isTablerMenuActive')) {
    /**
     * Check if a menu item is active for Tabler
     */
    function isTablerMenuActive($menuUrl, $currentUrl) {
        if (empty($menuUrl)) {
            return false;
        }

        // Remove leading slash for comparison
        $menuUrl = ltrim($menuUrl, '/');
        $currentUrl = ltrim($currentUrl, '/');

        // Exact match
        if ($menuUrl === $currentUrl) {
            return true;
        }

        // Check if current URL starts with menu URL (for parent paths)
        if (!empty($menuUrl) && strpos($currentUrl, $menuUrl) === 0) {
            // Make sure it's a path segment match, not just a string match
            $nextChar = substr($currentUrl, strlen($menuUrl), 1);
            return $nextChar === '/' || $nextChar === '' || $nextChar === '?';
        }

        return false;
    }
}

if (!function_exists('hasTablerActiveChild')) {
    /**
     * Check if any child menu is active for Tabler
     */
    function hasTablerActiveChild($children, $currentUrl) {
        foreach ($children as $child) {
            if (isTablerMenuActive($child['url'], $currentUrl)) {
                return true;
            }

            if (isset($child['children']) && !empty($child['children'])) {
                if (hasTablerActiveChild($child['children'], $currentUrl)) {
                    return true;
                }
            }
        }
        return false;
    }
}

if (!function_exists('getTablerIconSvg')) {
    /**
     * Convert Tabler icon class to SVG
     */
    function getTablerIconSvg($iconClass) {
        // Map of common Tabler icons to their SVG paths
        $iconMap = [
            'ti ti-home' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>',
            'ti ti-menu-2' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 6l16 0" /><path d="M4 12l16 0" /><path d="M4 18l16 0" /></svg>',
            'ti ti-users' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" /><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" /><path d="M16 3.13a4 4 0 0 1 0 7.75" /><path d="M21 21v-2a4 4 0 0 0 -3 -3.85" /></svg>',
            'ti ti-settings' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" /><path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" /></svg>',
            'ti ti-dashboard' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M13.45 11.55l2.05 -2.05" /><path d="M6.4 20a9 9 0 1 1 11.2 0z" /></svg>',
            'ti ti-file-text' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4" /><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z" /><path d="M9 9l1 0" /><path d="M9 13l6 0" /><path d="M9 17l6 0" /></svg>',
            'ti ti-chart-bar' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" /><path d="M9 8m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" /><path d="M15 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" /><path d="M4 20l14 0" /></svg>',
            'ti ti-edit' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1" /><path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z" /><path d="M16 5l3 3" /></svg>',
            'ti ti-trash' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 7l16 0" /><path d="M10 11l0 6" /><path d="M14 11l0 6" /><path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12" /><path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3" /></svg>',
            'ti ti-plus' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>',
            'ti ti-check' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10" /></svg>',
            'ti ti-x' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12" /><path d="M6 6l12 12" /></svg>',
            'ti ti-hierarchy' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M5 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M19 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M6.5 17.5l5.5 -4.5l5.5 4.5" /><path d="M12 7l0 6" /></svg>',
        ];

        return $iconMap[$iconClass] ?? '<i class="' . $iconClass . '"></i>';
    }
}

if (!function_exists('tabler_stats_card')) {
    /**
     * Generate a Tabler statistics card
     */
    function tabler_stats_card($title, $value, $options = []) {
        $icon = $options['icon'] ?? '';
        $color = $options['color'] ?? 'blue';
        $change = $options['change'] ?? '';
        $changeType = $options['change_type'] ?? 'positive';

        $html = '<div class="card">';
        $html .= '<div class="card-body">';
        $html .= '<div class="d-flex align-items-center">';
        $html .= '<div class="subheader">' . esc($title) . '</div>';

        if ($icon) {
            $html .= '<div class="ms-auto">';
            if (strpos($icon, 'ti ti-') === 0) {
                $iconSvg = getTablerIconSvg($icon);
                $html .= str_replace('class="icon"', 'class="icon text-' . $color . '"', $iconSvg);
            } else {
                $html .= '<i class="' . $icon . ' text-' . $color . '"></i>';
            }
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '<div class="d-flex align-items-baseline">';
        $html .= '<div class="h1 mb-0 me-2">' . esc($value) . '</div>';

        if ($change) {
            $changeColor = $changeType === 'positive' ? 'green' : 'red';
            $html .= '<div class="me-auto">';
            $html .= '<span class="text-' . $changeColor . ' d-inline-flex align-items-center lh-1">';
            $html .= esc($change);
            $html .= '</span>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}

if (!function_exists('tabler_bulk_actions')) {
    /**
     * Generate Tabler bulk action buttons
     */
    function tabler_bulk_actions($actions = []) {
        if (empty($actions)) {
            return '';
        }

        $html = '<div class="mb-3">';
        $html .= '<div class="btn-group" role="group">';

        foreach ($actions as $action) {
            $text = $action['text'] ?? '';
            $class = $action['class'] ?? 'btn-primary';
            $id = $action['id'] ?? '';
            $icon = $action['icon'] ?? '';
            $disabled = $action['disabled'] ?? true; // Bulk actions start disabled

            $html .= '<button type="button" class="btn ' . $class . '"';
            if ($id) {
                $html .= ' id="' . esc($id) . '"';
            }
            if ($disabled) {
                $html .= ' disabled';
            }
            $html .= '>';

            if ($icon) {
                if (strpos($icon, 'ti ti-') === 0) {
                    $iconSvg = getTablerIconSvg($icon);
                    $html .= str_replace('class="icon"', 'class="icon icon-sm me-1"', $iconSvg);
                } else {
                    $html .= '<i class="' . $icon . ' me-1"></i>';
                }
            }

            $html .= esc($text);
            $html .= '</button>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
