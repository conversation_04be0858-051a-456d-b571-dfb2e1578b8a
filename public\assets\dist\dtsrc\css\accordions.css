/*----------------------------------------*/
/*  1.  accordion CSS
/*----------------------------------------*/

.edu-custon-design .panel{
	background:none;
	box-shadow:none;
}
.edu-custon-design .panel-default{
	border:none;
}
.edu-custon-design .panel-collapse .panel-body{
	border-top:none ;
}
.edu-custon-design .panel-heading{
	border-top-left-radius:0px;
	border-top-right-radius:0px;
	background:#006DF0;
	color:#fff;
	font-size:20px;
	padding: 15px 20px;
}
.edu-custon-design .admin-panel-content{
	background:#f5f5f5;
}
.edu-custon-design .admin-panel-content p{
	font-size:14px;
	color:#444;
	line-height:24px;
}
.panel-group.edu-custon-design .accordion-head a:hover, .panel-group.edu-custon-design .accordion-head a:focus, .panel-group.edu-custon-design .accordion-head a:active{
	color:#03a9f4;
}
.panel-group.edu-custon-design{
	margin-bottom:30px;
}
.panel-group.edu-custon-design.edu-ad-mg-bt{
	margin-bottom:40px;
}
.admin-pro-accordion-wrap, .tab-content-details{
    background: #fff;
    padding: 15px;
}
.tab-content-details {
    text-align: center;
    background: #fff;
    padding: 20px 100px;
}
.tab-content-details p {
    font-size: 14px;
    color: #303030;
    line-height: 24px;
}
.admin-pro-accordion-wrap .panel-group.edu-custon-design {
	margin-bottom:0px;
}
.panel-group .panel-heading+.panel-collapse>.list-group, .panel-group .panel-heading+.panel-collapse>.panel-body {
    border-top: 0px solid #ddd;
}
.admin-accordion-mg-bt-40{
	margin-bottom:40px;
}