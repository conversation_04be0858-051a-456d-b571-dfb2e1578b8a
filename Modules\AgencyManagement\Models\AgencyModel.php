<?php

namespace Modules\AgencyManagement\Models;

use CodeIgniter\Model;
use Modules\AuditTrail\Libraries\AuditableTrait;

class AgencyModel extends Model
{
    use AuditableTrait;

    protected $DBGroup = 'default';
    protected $table = 'agencies';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 
        'description',
        'code', 
        'address', 
        'contact_person', 
        'contact_number', 
        'email',
        'is_active',
        'created_at', 
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    public function __construct()
    {
        parent::__construct();
        $this->auditableEvents = ['create', 'update', 'delete'];
        $this->auditableFields = [];
        $this->auditableExcludeFields = ['created_at', 'updated_at'];

        if (method_exists($this, 'initializeAuditLogging')) {
            $this->initializeAuditLogging();
        }
    }

    public function getAgenciesData(){
        return $this->db->table($this->table.' a')
            ->select('*')
            ->orderBy('a.created_at', 'DESC');
    }

    /**
     * Validate data for update operations
     */
    public function validateUpdate($data, $id)
    {
        // Set the ID for validation rules that need it
        $data['id'] = $id;

        $result = $this->validate($data);

        // Remove ID from data after validation
        unset($data['id']);

        return $result;
    }
    /**
     * Get agencies for DataTables with search and pagination
     */
    public function getDatatableData($request)
    {
        $builder = $this->builder();
        
        // Handle search
        if (!empty($request['search']['value'])) {
            $searchValue = $request['search']['value'];
            $builder->groupStart()
                    ->like('name', $searchValue)
                    ->orLike('description', $searchValue)
                    ->orLike('contact_person', $searchValue)
                    ->orLike('contact_number', $searchValue)
                    ->orLike('email', $searchValue)
                    ->orLike('address', $searchValue)
                    ->groupEnd();
        }

        // Handle ordering
        if (!empty($request['order'])) {
            $columnIndex = $request['order'][0]['column'];
            $columnName = $request['columns'][$columnIndex]['data'];
            $columnSortOrder = $request['order'][0]['dir'];
            
            // Map column names to actual database columns
            $columnMap = [
                'name' => 'name',
                'contact_person' => 'contact_person',
                'contact_number' => 'contact_number',
                'email' => 'email',
                'is_active' => 'is_active',
                'created_at' => 'created_at'
            ];
            
            if (isset($columnMap[$columnName])) {
                $builder->orderBy($columnMap[$columnName], $columnSortOrder);
            }
        } else {
            $builder->orderBy('created_at', 'DESC');
        }

        // Get total records count (before filtering)
        $totalRecords = $this->countAll();

        // Get filtered records count
        $filteredRecords = $builder->countAllResults(false);

        // Handle pagination
        if (isset($request['start']) && isset($request['length'])) {
            if ($request['length'] != -1) {
                $builder->limit($request['length'], $request['start']);
            }
        }

        $data = $builder->get()->getResultArray();

        return [
            'draw' => intval($request['draw']),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ];
    }

    /**
     * Get active agencies for dropdowns
     */
    public function getActiveAgencies()
    {
        return $this->where('is_active', 1)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get agency with user count
     */
    public function getAgencyWithUserCount($id)
    {
        $agency = $this->find($id);
        if ($agency) {
            // Count users assigned to this agency
            $userCount = $this->db->table('users')
                                  ->where('agency_id', $id)
                                  ->countAllResults();
            $agency['user_count'] = $userCount;
        }
        return $agency;
    }

    /**
     * Bulk update status
     */
    public function bulkUpdateStatus($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        return $this->whereIn('id', $ids)
                    ->set(['is_active' => $status])
                    ->update();
    }

    /**
     * Bulk delete agencies
     */
    public function bulkDelete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        // Check if any agency has users assigned
        $usersCount = $this->db->table('users')
                               ->whereIn('agency_id', $ids)
                               ->countAllResults();

        if ($usersCount > 0) {
            return ['error' => 'Cannot delete agencies that have users assigned to them.'];
        }

        $deleted = $this->whereIn('id', $ids)->delete();
        return ['success' => $deleted];
    }

    /**
     * Check if agency can be deleted
     */
    public function canDelete($id)
    {
        $userCount = $this->db->table('users')
                              ->where('agency_id', $id)
                              ->countAllResults();
        
        return $userCount === 0;
    }

    /**
     * Get agency statistics
     */
    public function getStatistics()
    {
        return [
            'total' => $this->countAll(),
            'active' => $this->where('is_active', 1)->countAllResults(false),
            'inactive' => $this->where('is_active', 0)->countAllResults(false),
        ];
    }
}
