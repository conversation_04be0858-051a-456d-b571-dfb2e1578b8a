<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;
use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class VFplusModel extends Model
{
    protected $DBGroup          = 'fameplus';
    protected $table            = 'tbl_buyer';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = [];

    // protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // protected array $casts = [];
    // protected array $castHandlers = [];

    // Dates
    // protected $useTimestamps = false;
    // protected $dateFormat    = 'datetime';
    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    // Validation
    // protected $validationRules      = [];
    // protected $validationMessages   = [];
    // protected $skipValidation       = false;
    // protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }


    public function getAttendanceProfile($fair_code){
        $subQuery = $this->db->table('tbl_product_sub_category')
            ->select("GROUP_CONCAT(name SEPARATOR '|')", false)
            ->where("FIND_IN_SET(id, sub_category)", null, false)
            ->getCompiledSelect();

        return $this->db->table($this->table.' a')
            ->select([
                    'a.id as fameplus_id',
                    'a.email',
                    'a.company as co_name',
                    'a.fname as cont_per_fn',
                    'a.lname as cont_per_ln',
                    'a.password',
                    'a.country',
                    'a.date_created as date_apply',
                    'a.designation as title',
                    'a.website as webpage',
                    'a.address as add_st',
                    'a.contact_number as mobile',
                    'a.company_role as job_function',
                    'a.salutation',
                    'a.market_segment',
                    'a.annual_sales',
                    'a.supplier_info',
                    'a.reasons as show_reason',
                    'a.product_interest',
                    'a.learn_about as informthru',
                    'a.nature_of_business as representation',
                    'a.participated',
                    'a.editions',
                    'a.mf_event as attend_event',
                    "($subQuery) as prod_sub_category",
                    "CASE
                        WHEN a.account_type = 1 THEN 'Trade Buyer'
                        WHEN a.account_type = 2 THEN 'Visitor'
                     END as accnt_type",
                    "CASE
                        WHEN a.vtype = 1 THEN 'Invited Guests(promo code)'
                        WHEN a.vtype = 2 THEN 'Government'
                        WHEN a.vtype = 3 THEN 'Media'
                        WHEN a.vtype = 4 THEN 'General Public'
                     END as visitor_type",
                    "CASE
                        WHEN a.status = 0 THEN 'INCOMPLETE TRADE'
                        WHEN a.status = 1 THEN 'APPROVED TRADE'
                        WHEN a.status = 2 THEN 'PENDING TRADE'
                        WHEN a.status = 3 THEN 'REVIEWED TRADE'
                        WHEN a.status = 4 THEN 'DEACTIVATED TRADE'
                        WHEN a.status = 5 THEN 'DISAPPROVED TRADE'
                     END as validation_status",
                     // "IF(a.participated = 'Yes' AND a.vtype = 1 , 'Regular', '') AS visitor_status",
                     "CASE
                        WHEN a.participated = 'Yes' THEN 'REGULAR'
                        WHEN a.participated = 'No' THEN 'NEW'
                        ELSE ''
                     END as visitor_status"
                ])
            // ->select('*')
            // ->join('e_attendance b','a.ff_code=b.ff_code','inner')
            ->where(['mf_event'=>'Yes']);


    }


    public function getAttendanceProfile2($fair_code){
        $subQuery = $this->db->table('tbl_product_sub_category')
            ->select("GROUP_CONCAT(name SEPARATOR '|')", false)
            ->where("FIND_IN_SET(id, sub_category)", null, false)
            ->getCompiledSelect();
        try{
            $builder = $this->db->table($this->table.' a')
            ->select([
                    'a.id as fameplus_id',
                    'a.email',
                    'a.company as co_name',
                    'a.fname as cont_per_fn',
                    'a.lname as cont_per_ln',
                    'a.password',
                    'a.country',
                    'a.date_created as date_apply',
                    'a.designation as title',
                    'a.website as webpage',
                    'a.address as add_st',
                    'a.contact_number as mobile',
                    'a.company_role as job_function',
                    'a.salutation',
                    'a.market_segment',
                    'a.annual_sales',
                    'a.supplier_info',
                    'a.reasons as show_reason',
                    'a.product_interest',
                    'a.learn_about as informthru',
                    'a.nature_of_business as representation',
                    'a.participated',
                    'a.editions',
                    'a.mf_event as attend_event',
                    "($subQuery) as prod_sub_category",
                    "CASE
                        WHEN a.account_type = 1 THEN 'Trade Buyer'
                        WHEN a.account_type = 2 THEN 'Visitor'
                     END as accnt_type",
                    "CASE
                        WHEN a.vtype = 1 THEN 'Invited Guests(promo code)'
                        WHEN a.vtype = 2 THEN 'Government'
                        WHEN a.vtype = 3 THEN 'Media'
                        WHEN a.vtype = 4 THEN 'General Public'
                     END as visitor_type",
                    "CASE
                        WHEN a.status = 0 THEN 'INCOMPLETE TRADE'
                        WHEN a.status = 1 THEN 'APPROVED TRADE'
                        WHEN a.status = 2 THEN 'PENDING TRADE'
                        WHEN a.status = 3 THEN 'REVIEWED TRADE'
                        WHEN a.status = 4 THEN 'DEACTIVATED TRADE'
                        WHEN a.status = 5 THEN 'DISAPPROVED TRADE'
                     END as validation_status",
                     // "IF(a.participated = 'Yes' AND a.vtype = 1 , 'Regular', '') AS visitor_status",
                     "CASE
                        WHEN a.participated = 'Yes' THEN 'REGULAR'
                        WHEN a.participated = 'No' THEN 'NEW'
                        ELSE ''
                     END as visitor_status"
                ])
            // ->select('*')
            // ->join('e_attendance b','a.ff_code=b.ff_code','inner')
            ->where(['mf_event'=>'Yes']);

            // $query = $builder->get();
            // $results['data'] = $query->getResult();
            // $results['data'] = $query->getResultArray();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $results['sql'] = str_replace("\n", " ", $sql);
            // echo $results['sql'];exit();
            // return $results['data'];
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage());
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }

    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }



}
