<?php

namespace Modules\RoleManagement\Models;

use CodeIgniter\Model;

class PermissionModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'permissions';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'description', 'category','is_active'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    // protected $validationRules = [
    //     'name'        => 'required|alpha_numeric_space|min_length[3]|max_length[100]|is_unique[permissions.name,id,{id}]',
    //     'description' => 'permit_empty|max_length[500]',
    // ];
    // protected $validationMessages = [];
    // protected $skipValidation       = false;
    // protected $cleanValidationRules = true;

    /**
     * Get statistics for dashboard
     */
    public function getStatistics()
    {
        $total = $this->countAll();

        // Count distinct categories
        $categories = $this->db->table('permissions')
                              ->select('category')
                              ->where('category IS NOT NULL')
                              ->where('category !=', '')
                              ->distinct()
                              ->countAllResults();

        // Count permissions added this month
        $recent = $this->where('created_at >=', date('Y-m-01 00:00:00'))
                      ->countAllResults(false);

        // Count unused permissions (not assigned to any role)
        $unused = $this->db->table('permissions p')
                          ->select('p.id')
                          ->join('role_permissions rp', 'rp.permission_id = p.id', 'left')
                          ->where('rp.permission_id IS NULL')
                          ->countAllResults();

        return [
            'total' => $total,
            'by_category' => $categories,
            'recent' => $recent,
            'unused' => $unused,
        ];
    }


    /**
     * Checks if a permission can be deleted.
     * For MyISAM, this means checking for related records in the role_permissions table.
     * @param int $permissionId
     * @return bool True if the permission can be deleted, false otherwise.
     */
    public function canDelete(int $permissionId): bool
    {
        // Check for associated role_permissions
        $rolePermissionsCount = $this->db->table('role_permissions')
                                         ->where('permission_id', $permissionId)
                                         ->countAllResults();
        if ($rolePermissionsCount > 0) {
            return false; // Permission is assigned to roles
        }

        return true; // No associated records found, safe to delete
    }

    /**
     * Bulk delete permissions
     */
    public function bulkDelete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return ['error' => 'No permissions selected'];
        }

        // Check if any permissions are being used by roles
        $roleCount = $this->db->table('role_permissions')
                             ->whereIn('permission_id', $ids)
                             ->countAllResults();

        if ($roleCount > 0) {
            return ['error' => "Cannot delete permissions. They are assigned to $roleCount role(s)."];
        }

        try {
            // Delete role permissions first
            $this->db->table('role_permissions')->whereIn('permission_id', $ids)->delete();
            // Delete permissions
            $this->whereIn('id', $ids)->delete();
            return ['success' => true];
        } catch (\Exception $e) {
            return ['error' => 'Failed to delete permissions: ' . $e->getMessage()];
        }
    }

    /**
     * Bulk update status for permissions
     */
    public function bulkUpdateStatus($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        try {
            $this->whereIn('id', $ids)->set(['is_active' => $status])->update();
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Error updating permission status: ' . $e->getMessage());
            return false;
        }
    }

    public function getDistinctCategories()
    {
        return $this->select('category')
                        ->distinct()
                        ->where('category IS NOT NULL')
                        ->where('category !=', '')
                        ->where('is_active', 1)
                        ->orderBy('category', 'ASC')
                        ->findAll();
    }

}
