<?= $this->extend('layouts/tabler-topnav') ?>

<?= $this->section('title') ?>
Sales Dashboard
<?= $this->endSection() ?>

<?= $this->section('description') ?>
Comprehensive sales monitoring and analytics dashboard
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
Sales Monitoring Dashboard
<?= $this->endSection() ?>

<?= $this->section('page_subtitle') ?>
Track your sales performance and manage opportunities
<?= $this->endSection() ?>

<?= $this->section('page_actions') ?>
<a href="<?= base_url('sales/create') ?>" class="btn btn-primary d-none d-sm-inline-block" data-aos="fade-left">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
        <path d="M12 5l0 14"></path>
        <path d="M5 12l14 0"></path>
    </svg>
    New Sales Entry
</a>
<a href="<?= base_url('sales/create') ?>" class="btn btn-primary d-sm-none btn-icon" data-aos="fade-left">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
        <path d="M12 5l0 14"></path>
        <path d="M5 12l14 0"></path>
    </svg>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Sales Statistics Cards -->
<div class="row row-deck row-cards mb-4">
    <div class="col-sm-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="card sales-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Total Export Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-blue-lt">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon text-blue" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                                <path d="M3.6 9h16.8"></path>
                                <path d="M3.6 15h16.8"></path>
                                <path d="M11.5 3a17 17 0 0 0 0 18"></path>
                                <path d="M12.5 3a17 17 0 0 1 0 18"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3">$24,300</div>
                <div class="d-flex mb-2">
                    <div>Completion</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            8%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 17l6 -6l4 4l8 -8"></path>
                                <path d="M14 7l7 0l0 7"></path>
                            </svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-blue" style="width: 75%" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                        <span class="visually-hidden">75% Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="card sales-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Total Domestic Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-green-lt">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon text-green" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M5 12l-2 0l9 -9l9 9l-2 0"></path>
                                <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                                <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3">$18,500</div>
                <div class="d-flex mb-2">
                    <div>Completion</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            4%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 17l6 -6l4 4l8 -8"></path>
                                <path d="M14 7l7 0l0 7"></path>
                            </svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-green" style="width: 75%" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                        <span class="visually-hidden">75% Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="card sales-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Total Retail Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-orange-lt">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon text-orange" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M6.331 8h11.339a2 2 0 0 1 1.977 2.304l-1.255 8.152a3 3 0 0 1 -2.966 2.544h-6.852a3 3 0 0 1 -2.965 -2.544l-1.255 -8.152a2 2 0 0 1 1.977 -2.304z"></path>
                                <path d="M9 11v-5a3 3 0 0 1 6 0v5"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3">$7,890</div>
                <div class="d-flex mb-2">
                    <div>Completion</div>
                    <div class="ms-auto">
                        <span class="text-red d-inline-flex align-items-center lh-1">
                            -2%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 7l6 6l4 -4l8 8"></path>
                                <path d="M14 17l7 0l0 -7"></path>
                            </svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-orange" style="width: 75%" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                        <span class="visually-hidden">75% Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3" data-aos="fade-up" data-aos-delay="400">
        <div class="card sales-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Under Negotiation</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-yellow-lt">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon text-yellow" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
                                <path d="M12 7l0 5l3 3"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3">$32,000</div>
                <div class="d-flex mb-2">
                    <div>Pending</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            12%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 17l6 -6l4 4l8 -8"></path>
                                <path d="M14 7l7 0l0 7"></path>
                            </svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-yellow" style="width: 75%" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                        <span class="visually-hidden">75% Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sales Table -->
<div class="card" data-aos="fade-up" data-aos-delay="500">
    <div class="card-header">
        <h3 class="card-title">Recent Sales Entries</h3>
        <div class="card-actions">
            <a href="<?= base_url('sales/export') ?>" class="btn btn-outline-primary btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                    <path d="M12 17v-6"></path>
                    <path d="M9.5 14.5l2.5 2.5l2.5 -2.5"></path>
                </svg>
                Export
            </a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table card-table table-vcenter">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Client</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th class="w-1"></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2023-11-15</td>
                    <td>
                        <div class="d-flex py-1 align-items-center">
                            <span class="avatar me-2" style="background-image: url(https://ui-avatars.com/api/?name=Global+Imports&background=206bc4&color=fff)"></span>
                            <div class="flex-fill">
                                <div class="font-weight-medium">Global Imports Inc.</div>
                                <div class="text-muted">International Trade</div>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-blue">Export</span></td>
                    <td class="text-muted">$12,500</td>
                    <td><span class="badge bg-green">Booked</span></td>
                    <td>
                        <div class="btn-list flex-nowrap">
                            <a href="#" class="btn btn-white btn-sm">View</a>
                            <div class="dropdown">
                                <button class="btn btn-white btn-sm dropdown-toggle align-text-top" data-bs-toggle="dropdown">Actions</button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="#">Edit</a>
                                    <a class="dropdown-item" href="#">Duplicate</a>
                                    <a class="dropdown-item text-danger" href="#">Delete</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <!-- Additional rows would be dynamically generated -->
            </tbody>
        </table>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Add any page-specific JavaScript here
console.log('Sales Dashboard Enhanced loaded');
</script>
<?= $this->endSection() ?>
