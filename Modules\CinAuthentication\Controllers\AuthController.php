<?php

namespace Modules\CinAuthentication\Controllers;

use App\Controllers\BaseController;
// use Modules\CinAuthentication\Models\UserModel;
use Modules\CinAuthentication\Models\CinModel;
use Mo<PERSON>les\CinAuthentication\Config\Auth as AuthConfig;
use Modules\CinAuthentication\Config\Validation;
use Modules\AuditTrail\Libraries\AuditLogger;
use Modules\RoleManagement\Models\RoleModel;

class AuthController extends BaseController
{
    public function login(){
        if(session()->has('userId')){
            $config = new AuthConfig();
            $route = $config->success_login_route;
            return redirect()->to($route);
        }
        $config = new \Modules\CinAuthentication\Config\Auth();
        $loginField = $config->loginField;
        // $data['loginLabel'] = $loginField === 'email' ? 'Email' : 'Username';
    
        return view('Modules\CinAuthentication\Views\auth\cipLogin',
        [
            'loginField' => $config->loginField,
            'loginLabel' => $loginField === 'email' ? 'Email' : 'Username',
            'title' => $config->title,
            'system_name' => $config->system_name,
        ]);
    }

    public function attemptLogin(){
        $config = new AuthConfig();
        $loginField = $config->loginField; // Get the login field ('email' or 'username')
        
        $userModel = new CinModel();
        $loginValue = $this->request->getPost($loginField);
        $password = $this->request->getPost('password');

        $validation = new Validation();

        // Validate the input
        if(!$this->validate($validation->login, $validation->login_errors)){
            // print_r($this->validator->getErrors());exit();
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Find user by the login field
        $user = $userModel->where('co_email', $loginValue)->first();

        // $user = $userModel->where('email', $loginValue)
        //                 ->orwhere('username', $loginValue)
        //                 ->first();

        if ($user && $user['cip']==$password) {
            $session = session();
            $session->regenerate();

            $this->setUserSession($user);
            //Audit here
            $auditLogger = new AuditLogger();
            $auditLogger->logLogin($user['ff_code']);
            
            $route = $config->success_login_route;
            return redirect()->to($route);
        }
        // echo 'Invalid login credentials.';
        // return redirect()->back()->with('error', 'Invalid login credentials.');
        return redirect()->back()->withInput()->with('warning','Invalid login credentials.') ;
    }

    public function register()
    {
        return view('Modules\CinAuthentication\Views\auth\register');
    }

    public function attemptRegister()
    {
        $userModel = new UserModel();
        $config = new AuthConfig();
        $roleId = $config->default_role_id;

        $data = $this->request->getPost();
        $data['role_id'] = $roleId;
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        $userModel->save($data);

        return redirect()->route('auth.login')->with('success', 'Registration successful. Please login.');
    }

    public function logout()
    {
        session()->destroy();
        return redirect()->route('cinauth.login');
    }

    public function getCurrentUser(){
        if(!session()->has('userId')){
            return null;
        }
    }

    public function isLoggedIn(){
        return session()->has('userId');
    }

    public function forgotPassword()
    {
        return view('Modules\CinAuthentication\Views\auth\forgot_password');
    }

    public function sendResetLink()
    {
        $email = $this->request->getPost('email');
        $userModel = new UserModel();
        $user = $userModel->where('email', $email)->first();

        if (!$user) {
            return redirect()->back()->with('error', 'Email not found.');
        }

        $token = bin2hex(random_bytes(32));
        $resetLink = base_url("auth/reset-password/$token");

        // Save token to the database or use a cache system
        $userModel->update($user['id'], ['reset_token' => $token]);

        // Send email (pseudo-code, integrate with your mailer)
        // mail($user['email'], 'Password Reset', "Click here to reset your password: $resetLink");

        return redirect()->route('auth.login')->with('success', 'Password reset link sent to your email.');
    }

    public function resetPassword($token)
    {
        $userModel = new UserModel();
        $user = $userModel->where('reset_token', $token)->first();

        if (!$user) {
            return redirect()->route('auth.login')->with('error', 'Invalid or expired token.');
        }

        return view('Modules\CinAuthentication\Views\auth\reset_password', ['token' => $token]);
    }

    public function attemptResetPassword($token)
    {
        $userModel = new UserModel();
        $user = $userModel->where('reset_token', $token)->first();

        if (!$user) {
            return redirect()->route('auth.login')->with('error', 'Invalid or expired token.');
        }

        $password = $this->request->getPost('password');
        $userModel->update($user['id'], [
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'reset_token' => null,
        ]);

        return redirect()->route('auth.login')->with('success', 'Password has been reset successfully.');
    }

    private function setUserSession($user)
    {
        // Get user's role IDs for session
        // $roleModel = new \Modules\RoleManagement\Models\RoleModel();
        // $role = $roleModel->getUserRoleNames($user['id']);
        // $role = array_column($role, 'name');

        $role = 'exhibitor';

        $sessionData = [
            'user_id'       => $user['ff_code'],
            'email'         => $user['co_email'],
            'company_name' => $user['co_name'],
            'user_roles'    => $role,
            'isLoggedIn'    => true,
            //'permissions'   => $permissionNames, // Store permission names
        ];
        session()->set($sessionData);
    }


// $session->set([
//                 'isLoggedIn' => true,
//                 'user' => $user['ff_code'],
//                 'company_name' => $user['co_name'],
//                 'role_id' => '', // Store role_id in session
//             ]);


}
