<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;

class EventSchedModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'e_sales_eventsched';
    protected $primaryKey       = 'eventid';
    protected $useAutoIncrement = true;
    protected $returnType       = \Modules\SalesMonitoring\Entities\EventSched::class;
    // protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = ['description','sector_code','edition','faircode','no_of_days','start_date','active','date_added','date_modified'];

    // protected bool $allowEmptyInserts = false;
    // protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'date_added';
    protected $updatedField  = 'date_modified';
    protected $deletedField  = 'deleted_at';

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];



}
