<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>My Profile</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">My Profile</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-body box-profile">
                            <!-- Flash messages -->
                            <?php if (session()->getFlashdata('message')): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <?= session()->getFlashdata('message') ?>
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            <?php endif; ?>
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <?= session()->getFlashdata('error') ?>
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            <?php endif; ?>
                            <div class="text-center">
                                <!-- You can add a profile picture here -->
                                <img class="profile-user-img img-fluid img-circle"
                                     src="https://placehold.co/128x128/007bff/ffffff?text=U"
                                     alt="User profile picture">
                            </div>

                            <h3 class="profile-username text-center"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></h3>
                            <p class="text-muted text-center"><?= esc($user['username']) ?></p>

                            <ul class="list-group list-group-unbordered mb-3">
                                <li class="list-group-item">
                                    <b>Email</b> <a class="float-right"><?= esc($user['email']) ?></a>
                                </li>
                                <li class="list-group-item">
                                    <b>Contact No.</b> <a class="float-right"><?= esc($user['contact_number'] ?? 'N/A') ?></a>
                                </li>
                                <li class="list-group-item">
                                    <b>Office/Division</b> <a class="float-right"><?= esc($user['office_code'] ?? 'N/A') ?></a> <!-- You'll need to fetch office_name -->
                                </li>
                                <li class="list-group-item">
                                    <b>Account Status</b> <a class="float-right"><?= $user['is_active'] ? 'Active' : 'Inactive' ?></a>
                                </li>
                            </ul>

                            <a href="<?= base_url('profile/edit') ?>" class="btn btn-primary btn-block"><b>Edit Profile</b></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Auto-dismiss alerts after 5 seconds
    window.setTimeout(function() {
        $('.alert').fadeTo(500, 0).slideUp(500, function() {
            $(this).remove();
        });
    }, 5000);
});
</script>
<?= $this->endSection() ?>