<?php

namespace Modules\EmailTemplates\Models;

use CodeIgniter\Model;

class EmailTemplatesModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'email_templates';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'slug', 'description', 'category_name', 'subject', 'html_content', 'design_json', 'placeholders','preview_image','is_active', 'created_by', 'updated_by','created_at','updated_at'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // protected array $casts = [];
    // protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    // Validation
    // protected $validationRules      = [];
    // protected $validationMessages   = [];
    // protected $skipValidation       = false;
    // protected $cleanValidationRules = true;

    // Callbacks
    // protected $allowCallbacks = true;
    // protected $beforeInsert   = [];
    // protected $afterInsert    = [];
    // protected $beforeUpdate   = [];
    // protected $afterUpdate    = [];
    // protected $beforeFind     = [];
    // protected $afterFind      = [];
    // protected $beforeDelete   = [];
    // protected $afterDelete    = [];


    
    // public function getActiveTemplates()
    // {
    //     return $this->where('is_active', 1)->orderBy('name', 'ASC')->findAll();
    // }

    // public function getTemplateByName($name)
    // {
    //     return $this->where('name', $name)->first();
    // }

    // public function builder($category = '', $status = '')
    // {
    //     // $builder = $this->select('email_templates.*, u.username as created_by_name')
    //     //                 ->join('users u', 'u.id = email_templates.created_by', 'left');

    //     $builder = $this->db->table($this->table.' et')
    //                     ->select('et.*, u.username as created_by_name')
    //                     ->join('users u', 'u.id = et.created_by', 'left');

    //     if (!empty($category)) {
    //         $builder->where('et.category_name', $category);
    //     }

    //     if (!empty($status)) {
    //         $builder->where('et.is_active', $status);
    //     }

    //     return $builder;
    // }

    public function getTemplates($category = '', $status = '')
    {
        $templates = $this->db->table($this->table)
                            ->select('email_templates.id, email_templates.name, email_templates.description, email_templates.category_name, email_templates.subject, email_templates.is_active, email_templates.updated_at,email_templates.created_at, b.username as created_by_name')
                            ->join('users b', 'b.id = email_templates.created_by', 'left');

        if (!empty($category)) {
            $templates->where('email_templates.category_name', $category);
        }

        if ($status!='') {
            $templates->where('email_templates.is_active', $status);
        }

        // echo $templates->getCompiledSelect();exit();

        return $templates;
    }
}
