<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
Extend Role Expiration
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
Extend Role Expiration
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= site_url('admin/users') ?>">Users</a></li>
<li class="breadcrumb-item"><a href="<?= site_url('admin/users/assign-roles/' . $user['id']) ?>">Assign Roles</a></li>
<li class="breadcrumb-item active">Extend Expiration</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-calendar-plus"></i>
            Extend Role Expiration
        </h3>
    </div>

    <?= form_open('admin/users/extend-role-expiration/' . $user['id'] . '/' . $role['id']) ?>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?= session()->getFlashdata('success') ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?= session()->getFlashdata('error') ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>

        <!-- Current Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="card card-outline card-info">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-user"></i> User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td><?= esc($user['username']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?= esc($user['email']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Full Name:</strong></td>
                                <td><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge badge-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card card-outline card-warning">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-user-tag"></i> Role Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Role Name:</strong></td>
                                <td><?= esc($role['name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Description:</strong></td>
                                <td><?= esc($role['description']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Current Status:</strong></td>
                                <td>
                                    <?php if ($userRole['expires_at'] && strtotime($userRole['expires_at']) <= time()): ?>
                                        <span class="badge badge-danger">
                                            <i class="fas fa-clock"></i> Expired
                                        </span>
                                    <?php elseif ($userRole['is_active']): ?>
                                        <span class="badge badge-success">
                                            <i class="fas fa-check"></i> Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-pause"></i> Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Current Expiry:</strong></td>
                                <td>
                                    <?php if ($userRole['expires_at']): ?>
                                        <span class="badge badge-<?= strtotime($userRole['expires_at']) <= time() ? 'danger' : 'warning' ?>">
                                            <?= date('M j, Y g:i A', strtotime($userRole['expires_at'])) ?>
                                        </span>
                                        <?php if (strtotime($userRole['expires_at']) <= time()): ?>
                                            <br>
                                            <small class="text-danger">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                Expired <?= floor((time() - strtotime($userRole['expires_at'])) / (60 * 60 * 24)) ?> days ago
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge badge-success">Never Expires</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Extension Form -->
        <div class="card card-outline card-success">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-calendar-plus"></i> Extend Expiration
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> The new expiration date must be in the future. 
                    Leave empty to make this role permanent (never expires).
                </div>

                <div class="form-group">
                    <label for="expires_at">
                        <i class="fas fa-calendar-alt"></i> New Expiration Date & Time
                    </label>
                    <input type="datetime-local" 
                           class="form-control" 
                           id="expires_at" 
                           name="expires_at" 
                           min="<?= date('Y-m-d\TH:i', strtotime('+1 hour')) ?>"
                           value="<?= old('expires_at', date('Y-m-d\TH:i', strtotime('+30 days'))) ?>">
                    <small class="form-text text-muted">
                        Default is set to 30 days from now. You can adjust as needed.
                    </small>
                </div>

                <!-- Quick Selection Buttons -->
                <div class="form-group">
                    <label>Quick Selection:</label>
                    <div class="btn-group-toggle" data-toggle="buttons">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setExpiration(7)">
                            +7 Days
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setExpiration(30)">
                            +30 Days
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setExpiration(90)">
                            +90 Days
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setExpiration(365)">
                            +1 Year
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPermanent()">
                            Permanent
                        </button>
                    </div>
                </div>

                <!-- Preview -->
                <div class="form-group">
                    <label>Preview:</label>
                    <div class="alert alert-light" id="expirationPreview">
                        <i class="fas fa-eye"></i> 
                        <span id="previewText">Role will expire on: <strong id="previewDate"></strong></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card-footer">
        <button type="submit" class="btn btn-success">
            <i class="fas fa-calendar-plus"></i> Extend Expiration
        </button>
        <a href="<?= site_url('admin/users/assign-roles/' . $user['id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Role Assignment
        </a>
        <a href="<?= site_url('admin/users/expired-roles/' . $user['id']) ?>" class="btn btn-warning">
            <i class="fas fa-history"></i> View All Expired Roles
        </a>
    </div>
    <?= form_close() ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const expiresInput = document.getElementById('expires_at');
    const previewDiv = document.getElementById('expirationPreview');
    const previewText = document.getElementById('previewText');
    const previewDate = document.getElementById('previewDate');

    // Update preview when input changes
    expiresInput.addEventListener('change', updatePreview);
    
    // Initial preview update
    updatePreview();

    function updatePreview() {
        const value = expiresInput.value;
        
        if (value) {
            const date = new Date(value);
            const now = new Date();
            const diffTime = date - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            previewDate.textContent = date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            if (diffDays > 0) {
                previewText.innerHTML = `Role will expire on: <strong id="previewDate">${previewDate.textContent}</strong> (in ${diffDays} days)`;
                previewDiv.className = 'alert alert-success';
                previewDiv.innerHTML = '<i class="fas fa-check"></i> ' + previewText.innerHTML;
            } else {
                previewText.innerHTML = `<strong class="text-danger">Invalid date - must be in the future</strong>`;
                previewDiv.className = 'alert alert-danger';
                previewDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ' + previewText.innerHTML;
            }
        } else {
            previewDiv.className = 'alert alert-info';
            previewDiv.innerHTML = '<i class="fas fa-infinity"></i> Role will be <strong>permanent</strong> (never expires)';
        }
    }

    // Quick selection functions
    window.setExpiration = function(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        date.setHours(23, 59, 0, 0); // Set to end of day
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        expiresInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        updatePreview();
    };

    window.setPermanent = function() {
        expiresInput.value = '';
        updatePreview();
    };
});
</script>
<?= $this->endSection() ?>
