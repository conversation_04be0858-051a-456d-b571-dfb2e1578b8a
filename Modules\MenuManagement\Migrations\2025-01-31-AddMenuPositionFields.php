<?php

namespace Modules\MenuManagement\Migrations;

use CodeIgniter\Database\Migration;

class AddMenuPositionFields extends Migration
{
    public function up()
    {
        // Add new fields to menus table for top navigation support
        $fields = [
            'menu_position' => [
                'type' => 'ENUM',
                'constraint' => ['sidebar', 'top', 'both'],
                'default' => 'sidebar',
                'null' => false,
                'after' => 'active'
            ],
            'display_style' => [
                'type' => 'ENUM',
                'constraint' => ['default', 'dropdown', 'mega'],
                'default' => 'default',
                'null' => false,
                'after' => 'menu_position'
            ]
        ];

        $this->forge->addColumn('menus', $fields);
    }

    public function down()
    {
        // Remove the added fields
        $this->forge->dropColumn('menus', ['menu_position', 'display_style']);
    }
}
