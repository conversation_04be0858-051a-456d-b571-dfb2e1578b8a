<?php

namespace Modules\CinAuthentication\Models;

use CodeIgniter\Model;

use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class UserModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    // protected $returnType       = \App\Entities\User::class;
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields = [
        'refno', 
        'used', 
        'sector',
        'ff_code', 
        'co_name',
        'co_email',
        'cin',
        'cip',
        'fair_code',
        'marked',
        'survey',
        'role_id',
        'reset_token',
        'created_at', 
        'updated_at'
    ];
    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';


    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }


    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }


}
