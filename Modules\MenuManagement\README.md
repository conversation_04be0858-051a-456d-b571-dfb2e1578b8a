# Menu Management Module

A comprehensive menu management system for CodeIgniter 4 with hybrid session/cache performance optimization and permission-based access control.

## Features

- **Hierarchical Menu Structure**: Support for parent-child menu relationships
- **Permission-Based Access**: Integration with existing role-permission system
- **Hybrid Caching**: Session + cache for optimal performance
- **AdminLTE Integration**: Seamless integration with AdminLTE sidebar
- **CRUD Operations**: Full create, read, update, delete functionality
- **Menu Ordering**: Sortable menu items with drag-and-drop support
- **Icon Support**: FontAwesome icon integration
- **Active State Detection**: Automatic highlighting of active menu items

## Installation

1. **Run Migrations**:
   ```bash
   php spark migrate -n "Modules\MenuManagement"
   ```

2. **Add Permissions**:
   ```bash
   php spark migrate:latest
   ```

3. **Seed Sample Data** (Optional):
   ```bash
   php spark db:seed "Modules\MenuManagement\Database\Seeds\MenuSeeder"
   ```

## Database Schema

The module uses a single `menus` table with the following structure:

```sql
CREATE TABLE menus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    label VARCHAR(100) NOT NULL,
    url VARCHAR(191),
    icon VARCHAR(100),
    permission_id INT,
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    active TINYINT(1) DEFAULT 1,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (permission_id) REFERENCES permissions(id),
    FOREIGN KEY (parent_id) REFERENCES menus(id)
) ENGINE = MyISAM;
```

## Usage

### Accessing Menu Management

Navigate to `/admin/menu-management` to access the menu management interface.

**Required Permission**: `menu.manage`

### Creating Menus

1. Go to Menu Management → Add New Menu
2. Fill in the menu details:
   - **Label**: Display name for the menu
   - **URL**: Target URL (leave empty for parent menus)
   - **Icon**: FontAwesome icon class (e.g., `fas fa-users`)
   - **Parent Menu**: Select a parent for submenu creation
   - **Permission**: Required permission to view the menu
   - **Sort Order**: Display order (lower numbers first)
   - **Active**: Enable/disable the menu

### Rendering Menus in Sidebar

Use the helper function to render menus in your sidebar:

```php
<?= renderSidebarMenu() ?>
```

Or with specific user ID:

```php
<?= renderSidebarMenu($userId) ?>
```

### Getting Breadcrumbs

```php
$breadcrumbs = getMenuBreadcrumb();
foreach ($breadcrumbs as $crumb) {
    echo $crumb['label'];
}
```

## Performance Optimization

The system uses a hybrid caching approach:

1. **Session Storage**: Fastest access for current user session
2. **Cache Layer**: Shared cache for multiple users with same permissions
3. **Database**: Fallback when cache misses occur

### Cache Keys

- `admin_hierarchical_menus`: Admin menu tree (1 hour)
- `user_menus_{hash}`: User-specific menus (30 minutes)
- `menu_stats`: Menu statistics (5 minutes)

### Cache Management

Caches are automatically cleared when:
- Menus are created, updated, or deleted
- Menu order is changed
- Menu status is modified

## API Reference

### MenuModel

#### Key Methods

- `getMenusWithPermissions()`: Get all menus with permission info
- `getHierarchicalMenus()`: Get admin menu tree (cached)
- `getUserMenus($permissions)`: Get user-accessible menus
- `getMenuOptions($excludeId)`: Get parent menu options
- `clearMenuCache()`: Clear all menu caches

### MenuService

#### Key Methods

- `getUserMenus($userId)`: Get user menus with caching
- `renderSidebarMenu($userId)`: Render HTML for sidebar
- `getBreadcrumb($userId)`: Get breadcrumb trail
- `validateMenuHierarchy($menuId, $parentId)`: Validate hierarchy
- `getMenuStats()`: Get menu statistics

### MenuController

#### Routes

- `GET /admin/menu-management`: List all menus
- `GET /admin/menu-management/create`: Create menu form
- `POST /admin/menu-management/store`: Store new menu
- `GET /admin/menu-management/edit/{id}`: Edit menu form
- `POST /admin/menu-management/update/{id}`: Update menu
- `GET /admin/menu-management/delete/{id}`: Delete menu
- `POST /admin/menu-management/reorder`: Reorder menus

## Permissions

The module adds the following permissions:

- `menu.manage`: Full menu management access
- `menu.view`: View menu management interface

## Helper Functions

### renderSidebarMenu($userId = null)

Renders the complete sidebar menu HTML with proper AdminLTE structure.

### getMenuBreadcrumb($userId = null)

Returns an array of menu items representing the breadcrumb trail for the current page.

## Customization

### Styling

The module uses AdminLTE classes. Customize by overriding CSS:

```css
.nav-sidebar .nav-item > .nav-link {
    /* Custom menu item styles */
}
```

### Menu HTML Structure

Override the `buildMenuHtml()` method in MenuService to customize HTML output.

### Cache Duration

Modify cache durations in MenuModel and MenuService:

```php
// Cache for 2 hours instead of 1
$this->cache->save($cacheKey, $menus, 7200);
```

## Troubleshooting

### Menus Not Displaying

1. Check user permissions
2. Verify menu is active
3. Clear cache: `php spark cache:clear`

### Performance Issues

1. Monitor cache hit rates
2. Adjust cache durations
3. Consider Redis for high-traffic sites

### Hierarchy Issues

1. Check for circular references
2. Validate parent-child relationships
3. Use `validateMenuHierarchy()` method

## Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Clear caches after changes

## License

This module follows the same license as the main application.
