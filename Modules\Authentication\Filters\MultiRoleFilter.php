<?php

namespace Modules\Authentication\Filters;

use CodeIgniter\HTTP\RequestInterface;
use <PERSON>Igniter\HTTP\ResponseInterface;
use <PERSON>Igniter\Filters\FilterInterface;
use App\Services\PermissionService;

/**
 * MultiRoleFilter - Check if user has any of the specified roles
 * 
 * Usage in routes:
 * $routes->get('admin/users', 'UserController::index', ['filter' => 'multirole:System Administrator,User Manager']);
 */
class MultiRoleFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $userId = session()->get('user_id');

        if (!$userId) {
            return redirect()->to('/auth/login')->with('error', 'Access denied. Please login first.');
        }

        // Parse the required roles from arguments
        $requiredRoles = [];
        if ($arguments) {
            // Arguments come as a single string with comma-separated roles
            $requiredRoles = array_map('trim', explode(',', $arguments[0]));
        }

        if (empty($requiredRoles)) {
            return redirect()->to('/')->with('error', 'No roles specified for access control.');
        }

        // Use the PermissionService to check if user has any of the required roles
        $permissionService = new PermissionService();

        if (!$permissionService->userHasAnyRole($userId, $requiredRoles)) {
            $rolesList = implode(', ', $requiredRoles);
            return redirect()->to('/')->with('error', "You need one of the following roles to access this page: {$rolesList}");
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Optional post-processing
    }
}
