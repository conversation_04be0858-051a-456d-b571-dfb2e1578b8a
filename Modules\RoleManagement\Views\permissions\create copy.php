<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/permissions') ?>">Permissions</a></li>
<li class="breadcrumb-item active">Create</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="card card-primary">
    <div class="card-header">
        <h3 class="card-title">Create New Permission</h3>
    </div>
    <form action="<?= route_to('permission.store') ?>" method="post">
        <?= csrf_field() ?>
        <div class="card-body">
            <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Validation Error!</h5>
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('success')) : ?>
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Permission Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control <?= isInvalid('name') ?>" id="name" placeholder="e.g., manage_users, view_reports" value="<?= old('name') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('name', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Unique identifier for the permission (use lowercase with underscores)</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="category">Category <span class="text-danger">*</span></label>
                        <input type="text" name="category" class="form-control <?= isInvalid('category') ?>" id="category" placeholder="e.g., User Management, Reports" value="<?= old('category') ?>">
                        <div class="invalid-feedback">
                            <?php echo show_validation_error('category', session("errors")); ?>
                        </div>
                        <small class="form-text text-muted">Category to group related permissions</small>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" class="form-control <?= isInvalid('description') ?>" id="description" rows="4" placeholder="A brief description of what this permission allows..."><?= old('description') ?></textarea>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('description', session("errors")); ?>
                </div>
                <small class="form-text text-muted">Clear description of what this permission grants access to</small>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create Permission
            </button>
            <a href="<?= route_to('permission.index') ?>" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Character counter for description
    $('#description').on('input', function() {
        var maxLength = 500;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' characters remaining');

        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // Auto-format permission name
    // $('#name').on('input', function() {
    //     var value = $(this).val();
    //     var formatted = value.toLowerCase().replace(/[^a-z0-9_]/g, '_').replace(/_+/g, '_');
    //     if (value !== formatted) {
    //         $(this).val(formatted);
    //     }

    //     // Validate format
    //     if (formatted && !/^[a-z][a-z0-9_]*$/.test(formatted)) {
    //         $(this).addClass('is-invalid');
    //         $(this).siblings('.invalid-feedback').text('Permission name must start with a letter and contain only lowercase letters, numbers, and underscores');
    //     } else {
    //         $(this).removeClass('is-invalid');
    //     }
    // });

    // Auto-format category name
    $('#category').on('input', function() {
        var value = $(this).val();
        // Capitalize first letter of each word
        var formatted = value.replace(/\b\w/g, function(l) { return l.toUpperCase(); });
        if (value !== formatted) {
            $(this).val(formatted);
        }
    });

    // Form validation
    // $('form').on('submit', function(e) {
    //     var isValid = true;

    //     // Check required fields
    //     if (!$('#name').val().trim()) {
    //         $('#name').addClass('is-invalid');
    //         $('#name').siblings('.invalid-feedback').text('Permission name is required');
    //         isValid = false;
    //     }

    //     if (!$('#category').val().trim()) {
    //         $('#category').addClass('is-invalid');
    //         $('#category').siblings('.invalid-feedback').text('Category is required');
    //         isValid = false;
    //     }

    //     if (!isValid) {
    //         e.preventDefault();
    //         Swal.fire('Error!', 'Please fill in all required fields.', 'error');
    //     }
    // });
});
</script>
<?= $this->endSection() ?>