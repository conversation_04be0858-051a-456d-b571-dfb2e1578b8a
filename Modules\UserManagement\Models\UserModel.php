<?php

namespace Modules\UserManagement\Models;

use CodeIgniter\Model;

use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class UserModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    // protected $returnType       = \App\Entities\User::class;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields = [
        'username', 
        'email', 
        'password',
        'first_name',
        'last_name',
        'agency_id',
        'is_active',
        'is_approved',
        'created_at', 
        'updated_at',
        'last_login_at',
        'reset_hash',
        'reset_expires_at',
        'activate_hash',
        'status',
        'status_message',

    ];
    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    // protected $validationRules = [
    //     'username' => 'required|alpha_numeric_space|min_length[3]|max_length[100]|is_unique[users.username,id,{id}]',
    //     'email'    => 'required|valid_email|is_unique[users.email,id,{id}]',
    //     'password' => 'required|min_length[8]',
    //     'role_id'  => 'required|integer',
    //     'agency_id'=> 'permit_empty|integer', // agency_id can be null
    // ];
    // protected $validationMessages = [];
    // protected $skipValidation       = true;
    // protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['hashPassword'];
    protected $beforeUpdate   = ['hashPassword'];

    protected $userFields = [
        'a.id',
        'username', 
        'a.email', 
        'password',
        'first_name',
        'last_name',
        'office_id',
        'a.is_active',
        'is_approved',
        'a.created_at', 
        'a.updated_at',
        'last_login_at',
        'reset_hash',
        'reset_expires_at',
        'activate_hash',
        'status',
        'status_message',
        'b.name as role_name',
        'c.code as office_code',

    ];

    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }

    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    // public function getUserWithRoleAndOffice($id)
    // {
    //     $user = $this->select('users.*, offices.name as office_name')
    //                  ->join('offices', 'offices.id = users.office_id', 'left')
    //                  ->find($id);

    //     if ($user) {
    //         $user['roles'] = $this->getUserRoles($id);
    //         // For backward compatibility, set primary role
    //         $user['role_name'] = !empty($user['roles']) ? $user['roles'][0]['role_name'] : null;
    //     }

    //     return $user;
    // }

    public function getAllUsersWithRoleAndOffice()
    {
        $users = $this->select('users.*, c.name as office_name')
                      ->join('offices c', 'c.id = users.office_id', 'left')
                      ->findAll();

        // Add roles for each user
        foreach ($users as &$user) {
            $user['roles'] = $this->getUserRoles($user['id']);
            // For backward compatibility, set primary role
            $user['role_name'] = !empty($user['roles']) ? $user['roles'][0]['role_name'] : null;
        }

        return $users;
    }

    public function getUsersWithRoles(){
        $userRoleModel = new \Modules\RoleManagement\Models\UserRoleModel();
        return $userRoleModel->getUsersWithRoles();
    }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics()
    {
        return [
            'total' => $this->countAll(),
            'active' => $this->where('is_active', 1)->countAllResults(),
            'inactive' => $this->where('is_active', 0)->countAllResults(),
        ];
    }

    /**
     * Get users with role and agency for DataTable builder
     */
    public function getUsersWithRoleAndOfficeBuilder()
    {
        // For DataTable, we'll show primary role (first assigned role)
        return $this->db->table('users a')
                       ->select([
                           'a.id',
                           'a.username',
                           'a.email',
                           'a.first_name',
                           'a.last_name',
                           'a.is_active',
                           'a.is_approved',
                           'a.created_at',
                           'a.updated_at',
                           'a.last_login_at',
                           'a.status',
                           'a.status_message',
                           'c.name as office_name',
                           'c.code as office_code',
                           'GROUP_CONCAT(DISTINCT r.name ORDER BY ur.assigned_at ASC SEPARATOR ", ") as role_names',
                           '(SELECT r2.name FROM user_roles ur2
                             JOIN roles r2 ON r2.id = ur2.role_id
                             WHERE ur2.user_id = a.id AND ur2.is_active = 1
                             ORDER BY ur2.assigned_at ASC LIMIT 1) as primary_role_name'
                       ])
                       ->join('offices c', 'c.id = a.office_id', 'left')
                       ->join('user_roles ur', 'ur.user_id = a.id AND ur.is_active = 1', 'left')
                       ->join('roles r', 'r.id = ur.role_id', 'left')
                       ->groupBy('a.id');
    }

    /**
     * Bulk delete users
     */
    public function bulkDelete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return ['error' => 'No users selected'];
        }

        try {
            $this->whereIn('id', $ids)->delete();
            return ['success' => true];
        } catch (\Exception $e) {
            return ['error' => 'Failed to delete users: ' . $e->getMessage()];
        }
    }

    /**
     * Bulk update status
     */
    public function bulkUpdateStatus($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        return $this->whereIn('id', $ids)
                    ->set(['is_active' => $status])
                    ->update();
    }


    public function getActiveUserWithRoleAndOffice($id)
    {
        return $this->db->table('users a')
                       ->select([
                           'a.id',
                           'a.username',
                           'a.email',
                           'a.first_name',
                           'a.last_name',
                           'a.is_active',
                           'a.is_approved',
                           'a.created_at',
                           'a.updated_at',
                           'a.last_login_at',   
                           'a.status',
                           'a.status_message',
                           'c.name as office_name',
                           'c.code as office_code',
                           'GROUP_CONCAT(DISTINCT r.name ORDER BY ur.assigned_at ASC SEPARATOR ", ") as role_names',
                           '(SELECT r2.name FROM user_roles ur2
                             JOIN roles r2 ON r2.id = ur2.role_id
                             WHERE ur2.user_id = a.id AND ur2.is_active = 1
                             ORDER BY ur2.assigned_at ASC LIMIT 1) as primary_role_name'
                       ])
                       ->join('offices c', 'c.id = a.office_id', 'left')
                       ->join('user_roles ur', 'ur.user_id = a.id AND ur.is_active = 1', 'left')
                       ->join('roles r', 'r.id = ur.role_id', 'left')
                       ->where('a.id', $id)
                       ->where('a.is_active', 1)
                       ->groupBy('a.id')
                       ->get()
                       ->getRowArray();
    }


    /**
     * Get user with role and agency details
     */
    public function getUserWithRoleAndOfficeDetails($id)
    {
        $user = $this->select('users.*, offices.name as office_name, offices.code as office_code')
                     ->join('offices', 'offices.id = users.office_id', 'left')
                     ->find($id);

        if ($user) {
            $user['roles'] = $this->getUserRoles($id);
            // For backward compatibility, set primary role
            $user['role_name'] = !empty($user['roles']) ? $user['roles'][0]['role_name'] : null;
        }

        return $user;
    }

    /**
     * Get active users for dropdowns
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)
                    ->orderBy('username', 'ASC')
                    ->findAll();
    }

    public function getUserWithRoleAndOffice($id)
    {
        return $this->db->table('users a')
                       ->select([
                           'a.id',
                           'a.username',
                           'a.email',
                           'a.first_name',
                           'a.last_name',
                           'a.is_active',
                           'a.is_approved',
                           'a.created_at',
                           'a.updated_at',
                           'a.last_login_at',   
                           'a.status',
                           'a.status_message',
                           'c.name as office_name',
                           'c.code as office_code',
                           'GROUP_CONCAT(DISTINCT r.name ORDER BY ur.assigned_at ASC SEPARATOR ", ") as role_names',
                           '(SELECT r2.name FROM user_roles ur2
                             JOIN roles r2 ON r2.id = ur2.role_id
                             WHERE ur2.user_id = a.id AND ur2.is_active = 1
                             ORDER BY ur2.assigned_at ASC LIMIT 1) as primary_role_name'
                       ])
                       ->join('offices c', 'c.id = a.office_id', 'left')
                       ->join('user_roles ur', 'ur.user_id = a.id AND ur.is_active = 1', 'left')
                       ->join('roles r', 'r.id = ur.role_id', 'left')
                       ->where('a.id', $id)
                       ->where('a.is_active', 1)
                       ->groupBy('a.id')
                       ->get()
                       ->getRowArray();
    }

    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }




}