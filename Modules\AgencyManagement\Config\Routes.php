<?php

namespace Modules\AgencyManagement\Config;

$routes->group('admin', function($routes) {
    $routes->group('agencies', [
        'namespace' => 'Modules\AgencyManagement\Controllers',
        // 'filter' => 'auth'
    ], function ($routes) {
        // Main CRUD routes
        $routes->get('/', 'AgencyController::index', ['as' => 'agencies.index']);
        $routes->get('create', 'AgencyController::create', ['as' => 'agencies.create']);
        $routes->post('store', 'AgencyController::store', ['as' => 'agencies.store']);
        $routes->get('edit/(:segment)', 'AgencyController::edit/$1', ['as' => 'agencies.edit']);
        $routes->post('update/(:segment)', 'AgencyController::update/$1', ['as' => 'agencies.update']);
        $routes->get('delete/(:segment)', 'AgencyController::delete/$1', ['as' => 'agencies.delete']);

        // AJAX endpoints for DataTables
        $routes->post('datatable', 'AgencyController::datatable', ['as' => 'agencies.datatable']);
        // $routes->get('datatable', 'AgencyController::datatable', ['as' => 'agencies.datatable']);
        $routes->post('ajax-delete', 'AgencyController::ajaxDelete', ['as' => 'agencies.ajax_delete']);
        $routes->get('show/(:segment)', 'AgencyController::show/$1', ['as' => 'agencies.show']);

        // Bulk operations
        $routes->post('bulk-delete', 'AgencyController::bulkDelete', ['as' => 'agencies.bulk_delete']);
        $routes->post('bulk-activate', 'AgencyController::bulkActivate', ['as' => 'agencies.bulk_activate']);
        $routes->post('bulk-deactivate', 'AgencyController::bulkDeactivate', ['as' => 'agencies.bulk_deactivate']);
    });
});
