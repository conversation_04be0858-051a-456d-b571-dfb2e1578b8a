<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/permissions') ?>">Permissions</a></li>
<li class="breadcrumb-item active">Edit</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Display flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<!-- Display validation errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <strong>Validation Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-edit"></i> Edit Permission: <?= esc($permission['name']) ?></h3>
            </div>
            <form action="<?= site_url('admin/permissions/update/' . $permission['id']) ?>" method="post" id="permissionEditForm">
                <?= csrf_field() ?>
                <div class="card-body">
                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag"></i> Permission Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" class="form-control <?= session('errors.name') ? 'is-invalid' : '' ?>"
                               value="<?= old('name', $permission['name']) ?>" required placeholder="Enter permission name">
                        <?php if (session('errors.name')): ?>
                            <div class="invalid-feedback"><?= session('errors.name') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Enter a unique name for this permission</small>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left"></i> Description</label>
                        <textarea name="description" id="description" class="form-control <?= session('errors.description') ? 'is-invalid' : '' ?>"
                                  rows="4" placeholder="Enter permission description"><?= old('description', $permission['description']) ?></textarea>
                        <?php if (session('errors.description')): ?>
                            <div class="invalid-feedback"><?= session('errors.description') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Provide a brief description of what this permission allows</small>
                    </div>

                    <div class="form-group">
                        <label for="category"><i class="fas fa-folder"></i> Category</label>
                        <input list="categoryOptions"
                            type="text"
                            name="category"
                            id="category"
                            class="form-control <?= session('errors.category') ? 'is-invalid' : '' ?>"
                            value="<?= old('category', $permission['category']) ?>"
                            placeholder="Enter category name">
                        <datalist id="categoryOptions">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['category'] ?>"><?= $category['category'] ?></option>
                            <?php endforeach; ?>
                        </datalist>
                        <?php if (session('errors.category')): ?>
                            <div class="invalid-feedback"><?= session('errors.category') ?></div>
                        <?php endif; ?>
                        <small class="form-text text-muted">Group related permissions together</small>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1"
                                   <?= old('is_active', $permission['is_active']) ? 'checked' : '' ?>>
                            <label class="custom-control-label" for="is_active">
                                Active Status
                            </label>
                        </div>
                        <small class="form-text text-muted">Inactive permissions cannot be assigned to roles</small>
                    </div>
                </div>

                <div class="card-footer bg-light">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update Permission
                    </button>
                    <a href="<?= site_url('admin/permissions') ?>" class="btn btn-secondary ml-2">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-info-circle"></i> Permission Information</h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td><?= date('M d, Y H:i', strtotime($permission['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Updated:</strong></td>
                        <td><?= date('M d, Y H:i', strtotime($permission['updated_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <?php if ($permission['is_active']): ?>
                                <span class="badge badge-success">Active</span>
                            <?php else: ?>
                                <span class="badge badge-danger">Inactive</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Category:</strong></td>
                        <td>
                            <?php if (!empty($permission['category'])): ?>
                                <span class="badge badge-info"><?= esc($permission['category']) ?></span>
                            <?php else: ?>
                                <span class="text-muted">No category</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Tip:</strong> Make sure the permission name follows the <code>module.action</code> naming convention for consistency.
                </div>

                <div class="btn-group-vertical w-100">
                    <a href="<?= site_url('admin/permissions/show/' . $permission['id']) ?>" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('#permissionEditForm').on('submit', function(e) {
        var name = $('#name').val().trim();

        if (name === '') {
            e.preventDefault();
            $('#name').addClass('is-invalid');
            if (!$('#name').next('.invalid-feedback').length) {
                $('#name').after('<div class="invalid-feedback">Permission name is required.</div>');
            }
            return false;
        } else {
            $('#name').removeClass('is-invalid');
            $('#name').next('.invalid-feedback').remove();
        }
    });

    // Real-time validation
    $('#name').on('input', function() {
        var name = $(this).val().trim();
        if (name !== '') {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>
<?= $this->endSection() ?>