<?php

namespace Modules\OfficeManagement\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public $create_office = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[offices.name,id,{id}]',
        'code' => 'required|is_unique[offices.code,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        // 'address' => 'permit_empty|max_length[255]',
        // 'contact_person' => 'permit_empty|max_length[100]',
        // 'contact_number' => 'permit_empty|max_length[20]',
        'email' => 'permit_empty|valid_email|max_length[255]|is_unique[agencies.email,id,{id}]',
        'is_active' => 'permit_empty|in_list[0,1]',
    ];

    public $create_office_errors = [
        'name' => [
            'required' => 'Office name is required.',
            'min_length' => 'Office name must be at least 3 characters long.',
            'max_length' => 'Office name cannot exceed 255 characters.',
            'is_unique' => 'This office name already exists.',
        ],
        'code' => [
            'required' => 'Office code is required.',
            'is_unique' => 'This office code already exists.',
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.',
            'is_unique' => 'This email address is already registered.',
        ],
    ];

    public $update_office = [
        'name' => 'required|min_length[3]|max_length[255]|is_unique[offices.name,id,{id}]',
        'code' => 'required|is_unique[offices.code,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        'email' => 'permit_empty|valid_email|max_length[255]|is_unique[offices.email,id,{id}]',
        'is_active' => 'permit_empty|in_list[0,1]',
    ];

    public $update_office_errors = [
        'name' => [
            'required' => 'Office name is required.',
            'min_length' => 'Office name must be at least 3 characters long.',
            'max_length' => 'Office name cannot exceed 255 characters.',
            'is_unique' => 'This office name already exists.',
        ],
        'code' => [
            'required' => 'Office code is required.',
            'is_unique' => 'This office code already exists.',
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.',
            'is_unique' => 'This email address is already registered.',
        ],
    ];
    

}
