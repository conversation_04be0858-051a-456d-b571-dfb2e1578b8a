<?php

namespace Modules\AuditTrail\Libraries;

use Modules\AuditTrail\Models\AuditLogModel;

class AuditLogger
{
    protected $auditLogModel;
    protected $request;
    protected $session;
    protected $db;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
        $this->request = \Config\Services::request();
        $this->session = \Config\Services::session();
        $this->db = \Config\Database::connect();
    }

    /**
     * Logs an audit event.
     *
     * @param string $event          The event name (e.g., 'user_login', 'product_created').
     * @param string|null $auditableType The type of entity being audited (e.g., 'User', 'Product').
     * @param int|null $auditableId  The ID of the entity being audited.
     * @param array|null $oldValues  Array of old values (for updates/deletes).
     * @param array|null $newValues  Array of new values (for creates/updates).
     * @return bool                  True on success, false on failure.
     */
    public function log(
        string $event,
        ?string $auditableType = null,
        ?int $auditableId = null,
        ?array $oldValues = null,
        ?array $newValues = null
    ): bool {
        try {
            // Get current user ID (adjust this based on your authentication system)
            // Assuming you store user ID in session, e.g., $_SESSION['user_id']
            $userId = $this->session->get('user_id'); // Or wherever your user ID is stored

            $data = [
                'user_id'        => $userId,
                'event'          => $event,
                'auditable_type' => $auditableType,
                'auditable_id'   => $auditableId,
                'old_values'     => $oldValues ? json_encode($oldValues) : null,
                'new_values'     => $newValues ? json_encode($newValues) : null,
                'ip_address'     => $this->request->getIPAddress(),
                'user_agent'     => $this->request->getUserAgent()->getAgentString(),
                'url'            => current_url(),
                'method'         => $this->request->getMethod(),
                'created_at'     => date('Y-m-d H:i:s'),
            ];

            return $this->auditLogModel->insert($data);
        } catch (\Exception $e) {
            // Log the error for debugging
            log_message('error', 'Audit Trail Logging Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Helper method to log a 'create' event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $newValues
     * @return bool
     */
    public function logCreate(string $auditableType, int $auditableId, array $newValues): bool
    {
        return $this->log('create', $auditableType, $auditableId, null, $newValues);
    }

    /**
     * Helper method to log an 'update' event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $oldValues
     * @param array $newValues
     * @return bool
     */
    public function logUpdate(string $auditableType, int $auditableId, array $oldValues, array $newValues): bool
    {
        return $this->log('update', $auditableType, $auditableId, $oldValues, $newValues);
    }

    /**
     * Helper method to log a 'delete' event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param array $oldValues
     * @return bool
     */
    public function logDelete(string $auditableType, int $auditableId, array $oldValues): bool
    {
        return $this->log('delete', $auditableType, $auditableId, $oldValues, null);
    }

    /**
     * Helper method to log a 'login' event.
     *
     * @param int $userId
     * @return bool
     */
    public function logLogin(int $userId): bool
    {
        // For login, we might not have auditable_type/id, but we have user_id
        return $this->log('login', 'User', $userId, null, ['user_id' => $userId]);
    }

    /**
     * Helper method to log a 'logout' event.
     *
     * @param int $userId
     * @return bool
     */
    public function logLogout(int $userId): bool
    {
        return $this->log('logout', 'User', $userId, ['user_id' => $userId], null);
    }

    /**
     * Helper method to log a 'view' event.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @return bool
     */
    public function logView(string $auditableType, int $auditableId): bool
    {
        return $this->log('view', $auditableType, $auditableId, null, null);
    }

    /**
     * Helper method to log bulk operations.
     *
     * @param string $action
     * @param string $auditableType
     * @param array $auditableIds
     * @param array|null $additionalData
     * @return bool
     */
    public function logBulkOperation(string $action, string $auditableType, array $auditableIds, ?array $additionalData = null): bool
    {
        $data = [
            'action' => $action,
            'entity_ids' => $auditableIds,
            'count' => count($auditableIds)
        ];

        if ($additionalData) {
            $data = array_merge($data, $additionalData);
        }

        return $this->log('bulk_' . $action, $auditableType, null, null, $data);
    }

    /**
     * Helper method to log file operations.
     *
     * @param string $action
     * @param string $filename
     * @param array|null $metadata
     * @return bool
     */
    public function logFileOperation(string $action, string $filename, ?array $metadata = null): bool
    {
        $data = ['filename' => $filename];
        if ($metadata) {
            $data = array_merge($data, $metadata);
        }

        return $this->log($action, 'File', null, null, $data);
    }

    /**
     * Helper method to log system events.
     *
     * @param string $event
     * @param array|null $data
     * @return bool
     */
    public function logSystemEvent(string $event, ?array $data = null): bool
    {
        return $this->log($event, 'System', null, null, $data);
    }

    /**
     * Helper method to log failed login attempts.
     *
     * @param string $username
     * @param string $reason
     * @return bool
     */
    public function logFailedLogin(string $username, string $reason = ''): bool
    {
        return $this->log('failed_login', 'User', null, null, [
            'username' => $username,
            'reason' => $reason
        ]);
    }

    /**
     * Helper method to log permission changes.
     *
     * @param string $action
     * @param int $userId
     * @param array $permissions
     * @return bool
     */
    public function logPermissionChange(string $action, int $userId, array $permissions): bool
    {
        return $this->log('permission_' . $action, 'User', $userId, null, [
            'permissions' => $permissions
        ]);
    }

    /**
     * Helper method to log role changes.
     *
     * @param string $action
     * @param int $userId
     * @param array $roles
     * @return bool
     */
    public function logRoleChange(string $action, int $userId, array $roles): bool
    {
        return $this->log('role_' . $action, 'User', $userId, null, [
            'roles' => $roles
        ]);
    }

    /**
     * Get audit logs for a specific entity.
     *
     * @param string $auditableType
     * @param int $auditableId
     * @param int $limit
     * @return array
     */
    public function getEntityLogs(string $auditableType, int $auditableId, int $limit = 50): array
    {
        return $this->auditLogModel->getEntityLogs($auditableType, $auditableId, $limit);
    }

    /**
     * Get audit logs for a specific user.
     *
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function getUserLogs(int $userId, int $limit = 50): array
    {
        return $this->auditLogModel->getUserLogs($userId, $limit);
    }

    /**
     * Get recent audit logs.
     *
     * @param int $limit
     * @return array
     */
    public function getRecentLogs(int $limit = 100): array
    {
        return $this->auditLogModel->getRecentLogs($limit);
    }

    /**
     * Get audit statistics.
     *
     * @return array
     */
    public function getStatistics(): array
    {
        return $this->auditLogModel->getStatistics();
    }

    /**
     * Clean old audit logs.
     *
     * @param int $daysToKeep
     * @return int
     */
    public function cleanOldLogs(int $daysToKeep = 365): int
    {
        return $this->auditLogModel->cleanOldLogs($daysToKeep);
    }
}
