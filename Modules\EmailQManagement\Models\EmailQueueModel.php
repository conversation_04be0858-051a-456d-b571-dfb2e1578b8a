<?php

namespace Modules\EmailQManagement\Models;

use CodeIgniter\Model;


class EmailQueueModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'email_queue';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['to_email', 'subject', 'template', 'template_data', 'body',
        'status', 'attempts', 'error', 'sent_at', 'created_at'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';


    public function getStats(string $period = 'weekly'): array
    {
        $builder = $this->builder();
        $dateRange = match ($period) {
            'weekly' => date('Y-m-d', strtotime('-7 days')),
            'monthly' => date('Y-m-d', strtotime('-30 days')),
            default => date('Y-m-d', strtotime('-7 days')),
        };

        $builder->select("status, COUNT(*) as total")
                ->where('created_at >=', $dateRange)
                ->groupBy('status');

        $results = $builder->get()->getResultArray();

        $stats = ['sent' => 0, 'failed' => 0, 'pending' => 0];

        foreach ($results as $row) {
            $stats[$row['status']] = (int) $row['total'];
        }

        return $stats;
    }


}
