<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Office Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<?php if (hasPermission('office.dashboard')) : ?>
<div class="row mb-3">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?= $statistics['total'] ?></h3>
                <p>Total Offices</p>
            </div>
            <div class="icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?= $statistics['active'] ?></h3>
                <p>Active Offices</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?= $statistics['inactive'] ?></h3>
                <p>Inactive Offices</p>
            </div>
            <div class="icon">
                <i class="fas fa-times-circle"></i>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Office List</h3>
        <div class="card-tools">
            <?php if (hasPermission('office.manage')) : ?>
                <a href="<?= route_to('offices.create') ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Office
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <?php if (hasPermission('office.manage')) : ?>
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                        <i class="fas fa-check"></i> Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                        <i class="fas fa-times"></i> Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <small class="text-muted ml-2">Select offices to enable bulk actions</small>
            </div>
        </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table id="officesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <?php if (hasPermission('office.manage')) : ?>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <?php endif; ?>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- View Office Modal -->
<div class="modal fade" id="viewOfficeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Office Details</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="office-details">
                <!-- Office details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables JS -->
<script src="<?php echo base_url("assets/plugins/datatables/jquery.dataTables.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/jszip/jszip.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/pdfmake.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/vfs_fonts.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js"); ?>"></script>

<script>
$(document).ready(function() {
    var officeTable = $('#officesTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "csv", "excel", "pdf", "print"],
        ajax: {
            url: '<?= site_url('admin/offices/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[<?= hasPermission('office.manage') ? '2' : '1' ?>, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('office.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'name' }
            ,{ data: 'email' }
            ,{ data: 'status_badge', orderable: false, searchable: false }
            ,{ data: 'created_date' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
        
        
    });

    // Add buttons to table
    officeTable.buttons().container().appendTo('#officesTable_wrapper .col-md-6:eq(0)');

    // Select all checkbox
    $('#select-all').on('click', function() {
        var checked = this.checked;
        $('.select-item').prop('checked', checked);
        toggleBulkActions();
    });

    // Individual checkbox
    $(document).on('change', '.select-item', function() {
        toggleBulkActions();
        
        // Update select all checkbox
        var totalItems = $('.select-item').length;
        var checkedItems = $('.select-item:checked').length;
        $('#select-all').prop('checked', totalItems === checkedItems);
    });

    // Toggle bulk action buttons
    function toggleBulkActions() {
        var checkedItems = $('.select-item:checked').length;
        $('#bulk-activate, #bulk-deactivate, #bulk-delete').prop('disabled', checkedItems === 0);
    }

    // Bulk actions
    $('#bulk-activate').on('click', function() {
        bulkAction('activate', 'Are you sure you want to activate the selected offices?');
    });

    $('#bulk-deactivate').on('click', function() {
        bulkAction('deactivate', 'Are you sure you want to deactivate the selected offices?');
    });

    $('#bulk-delete').on('click', function() {
        bulkAction('delete', 'Are you sure you want to delete the selected offices? This action cannot be undone.');
    });

    function bulkAction(action, message) {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select at least one office.');
            return;
        }
        swal.fire({
            title: 'Are you sure?',
            text: message,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, proceed!'
        }).then((result) => {
            if (result.isConfirmed) {
                var url = '';
                switch(action) {
                    case 'activate':
                        url = '<?= route_to('offices.bulk_activate') ?>';
                        break;
                    case 'deactivate':
                        url = '<?= route_to('offices.bulk_deactivate') ?>';
                        break;
                    case 'delete':
                        url = '<?= route_to('offices.bulk_delete') ?>';
                        break;
                }
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        ids: selectedIds,
                        action: action,
                        [csrfName]: csrfHash
                    },
                    success: function(response) {
                        if (response.success) {
                            swal.fire(
                                'Success!',
                                response.message,
                                'success'
                            );
                            $('#select-all').prop('checked', false);
                            officeTable.ajax.reload(function() {
                                toggleBulkActions();
                            });
                        } else {
                            swal.fire(
                                'Error!',
                                response.message,
                                'error'
                            );
                        }
                    },
                    error: function() {
                        swal.fire(
                            'Error!',
                            'An error occurred. Please try again.',
                            'error'
                        );
                    }
                });
            }
        });   
    }

    // delete office
    $(document).on('click', '#deleteOffice', function() {
        var id = $(this).data('id');
        deleteOffice(id);  
    });


    window.setTimeout(function() {
        $('.alert').fadeTo(500, 0).slideUp(500, function() {
            $(this).remove();
        });
    }, 5000);



});



// View agency function
function viewOffice(id) {
    $.ajax({
        url: '<?= site_url('admin/offices/show/') ?>/' + id,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                var office = response.data;
                var html = response.html;
                $('#office-details').html(html);
                $('#viewOfficeModal').modal('show');
            } else {
                alert(response.error);
            }
        },
        error: function() {
            alert('An error occurred while loading agency details.');
        }
    });
}

// Delete office function
function deleteOffice(id) {
    swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= route_to('offices.ajax_delete') ?>',
                type: 'POST',
                data: {
                    id: id,
                    [csrfName]: csrfHash
                },
                success: function(response) {
                    if (response.success) {
                        swal.fire(
                            'Deleted!',
                            'Office has been deleted.',
                            'success'
                        );
                        $('#officesTable').DataTable().ajax.reload();
                    } else {
                        swal.fire(
                            'Error!',
                            response.message,
                            'error'
                        );
                    }
                },
                error: function() {
                    swal.fire(
                        'Error!',
                        'An error occurred. Please try again.',
                        'error'
                    );
                }
            });
        }
    });


    
}

// CSRF token variables
var csrfName = '<?= csrf_token() ?>';
var csrfHash = '<?= csrf_hash() ?>';
</script>
<?= $this->endSection() ?>
