<?php

namespace Modules\UserManagement\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    public array $user = [
        'username' => 'required|min_length[3]|max_length[50]',
        'email'    => 'required|valid_email|max_length[100]',
        // 'password' => 'required|min_length[4]',
        'first_name' => 'required|alpha_numeric_space|min_length[3]|max_length[50]',
        'last_name' => 'required|alpha_numeric_space|min_length[3]|max_length[50]',
        // Note: role_ids validation is handled in controller since it's an array
    ];

    public array $user_errors = [
        'username' => [
            'required'    => 'The username is required.',
            'min_length'  => 'The username must be at least 3 characters.',
            'max_length'  => 'The username must not exceed 50 characters.',
        ],
        'email' => [
            'required'    => 'The email is required.',
            'valid_email' => 'The email must be a valid email address.',
            'max_length'  => 'The email must not exceed 100 characters.',
        ],
        // 'password' => [
        //     'required'   => 'The password is required.',
        //     'min_length' => 'The password must be at least 8 characters.',
        // ],
        'first_name' => [
            'required' => 'The first name is required.',
            'alpha_numeric_space' => 'The last name must be a valid name.',
            'min_length' => 'The first name must be at least 3 characters.',
            'max_length' => 'The first name must not exceed 50 characters.',

        ],        
        'last_name' => [
            'required' => 'The last name is required.',
            'alpha_numeric_space' => 'The last name must be a valid name.',
            'min_length' => 'The last name must be at least 3 characters.',
            'max_length' => 'The last name must not exceed 50 characters.',
            
        ],
        // role_ids validation is handled in controller
    ];
}
