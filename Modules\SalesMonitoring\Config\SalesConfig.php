<?php

namespace Modules\SalesMonitoring\Config;

use CodeIgniter\Config\BaseConfig;

class SalesConfig extends BaseConfig
{
    public $updating        = false;
    public $ifDashboard     = true;
    public $dateAsCalendar  = false;
    public $fair_code       = 'mfio2023';
    public $fair_year       = '2023';
    public $activeFair      = '4';
    // public $eventName       = 'Manila Fame 2023';
    public $productFairCode = 'FAMEDTCP2023';
    public $sectorCode      = '02';

    public $survey = array(
        'active' => 'false',
        'float'  => 'true',
        'sample' => 'test',
    );

    public $sale_status       = array('Booked','Under Negotiation');

    public $saleUpdate_status = array('Booked','Adjusted','Cancelled');

    public $pageDefault       = 1;

    public $pageSizeDefault   = 10;

    public $productFair = array(
        '01' => 'IFEXDTCP2024',
        '02' => 'DTCP2023',//check?FAMEDTCP2023
        '20' => 'CREATEDTCP2024',
        '25' => 'SSXDTCP2021',
    );

    public $sectorFairCode = array(
        '01' => 'FOOD',
        '02' => 'HOME',
        '20' => 'CREATE',
        '25' => 'SSX',
    );

    public $switch = array(
        'jobFunction'            => 'JOB_F',
        'businessOwnership'      => '2',
        'annualSales'            => 'MN2',
        'marketSegment'          => 'MN3',
        'showReason'             => 'MN10',
        'technique'              => '??',
        'informThru'             => 'B1',
        'tradeExperience'        => 'B2I',
        'targetBuyers'           => 'C1',
        'industryRepresentation' => 'B2',//representation
        'natureOfBusiness'       => 'B2',
        'representation'         => 'B2',
        'certification'          => 'B12',
        'orderTypes'             => 'B18',
    );


    public $pageSizeDefault1_sample = array(
    'rep_code','barcode','item_code','fair_code','remarks',
    );
}
