<?php

namespace Modules\Authentication\Controllers;

use App\Controllers\AdminController;
use Modules\UserManagement\Models\UserModel;
use Modules\RoleManagement\Models\RoleModel;
use Modules\UserManagement\Models\AgencyModel;
use Modules\Authentication\Services\PermissionService;
use Modules\Authentication\Config\Auth as AuthConfig;
use Modules\AuditTrail\Libraries\AuditLogger;
use Modules\Authentication\Config\Validation;

class AuthController extends AdminController
{
    public function login()
    {
        if(session()->has('user_id')){
            $config = new AuthConfig();
            $route = $config->success_login_route;
            return redirect()->to($route);
        }
        $config = new \Modules\Authentication\Config\Auth();
        $loginField = $config->loginField;
        // $data['loginLabel'] = $loginField === 'email' ? 'Email' : 'Username';
        return view('Modules\Authentication\Views\auth\login',[
            'title' => $config->title,
            'system_name' => $config->system_name,
            'loginField' => $config->loginField,
            'loginLabel' => $loginField === 'email' ? 'Email' : 'Username',
        ]);
    }

    public function attemptLogin()
    {
        $config = new AuthConfig();
        $loginField = $config->loginField; // Get the login field ('email' or 'username')
        

        $userModel = new UserModel();
        $loginValue = $this->request->getPost($loginField);
        $password = $this->request->getPost('password');

        $validation = new Validation();
        // dd($validation);
        // Validate the input
        if(!$this->validate($validation->login, $validation->login_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // $rules = load_rules('login');
        // if($this->validate($rules)===false){
        //     return redirect()->back()->withInput()->with('errors',$this->validator->getErrors());
        // }

        // Find user by the login field
        $user = $userModel->where($loginField, $loginValue)->first();
        // $user = $userModel->where('email', $loginValue)
        //                 ->orwhere('username', $loginValue)
        //                 ->first();

        if ($user && password_verify($password, $user['password'])) {

            //use permission service
            $permissionService = new PermissionService();
            $permissionNames = $permissionService->getPermissionNamesByUserId($user['id']);

            // Get user's permissions
                // $roleModel = new RoleModel();
                // $permissions = $roleModel->getRolePermissions($user['role_id']);
                // $userPermissions = array_column($permissions, 'permission_id'); // Get an array of permission IDs

                // $permissionModel = new \Modules\RoleManagement\Models\PermissionModel();
                // $permissionNames = [];
                // foreach ($userPermissions as $permId) {
                //     $permission = $permissionModel->find($permId);
                //     if ($permission) {
                //         $permissionNames[] = $permission['name'];
                //     }
                // }

                
            $session = session();
            $session->regenerate();
            $this->setUserSession($user,$permissionNames);
            //Audit here
            $auditLogger = new AuditLogger();
            $auditLogger->logLogin($user['id']);

            $route = $config->success_login_route;
            return redirect()->to($route);
        }
        return redirect()->back()->withInput()->with('warning','Invalid login credentials.') ;
    }

    public function register()
    {
        return view('Modules\Authentication\Views\auth\register');
    }

    public function attemptRegister()
    {
        $userModel = new UserModel();
        $config = new AuthConfig();
        $roleId = $config->default_role_id;

        $data = $this->request->getPost();
        $data['role_id'] = $roleId;
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        $userModel->save($data);

        return redirect()->route('auth.login')->with('success', 'Registration successful. Please login.');
    }

    public function logout()
    {
        session()->destroy();
        return redirect()->route('auth.login');
    }

    public function getCurrentUser(){
        if(!session()->has('user_id')){
            return null;
        }
        $userModel = new UserModel();
        return $userModel->find(session()->get('user_id'));
    }

    public function isLoggedIn(){
        return session()->has('user_id');
    }

    public function forgotPassword()
    {
        return view('Modules\Authentication\Views\auth\forgot_password');
    }

    public function sendResetLink()
    {
        $email = $this->request->getPost('email');
        $userModel = new UserModel();
        $user = $userModel->where('email', $email)->first();

        if (!$user) {
            return redirect()->back()->with('error', 'Email not found.');
        }

        $token = bin2hex(random_bytes(32));
        $resetLink = base_url("auth/reset-password/$token");

        // Save token to the database or use a cache system
        $userModel->update($user['id'], ['reset_token' => $token]);

        // Send email (pseudo-code, integrate with your mailer)
        // mail($user['email'], 'Password Reset', "Click here to reset your password: $resetLink");

        return redirect()->route('auth.login')->with('success', 'Password reset link sent to your email.');
    }

    public function resetPassword($token)
    {
        $userModel = new UserModel();
        $user = $userModel->where('reset_token', $token)->first();

        if (!$user) {
            return redirect()->route('auth.login')->with('error', 'Invalid or expired token.');
        }

        return view('Modules\Authentication\Views\auth\reset_password', ['token' => $token]);
    }

    public function attemptResetPassword($token)
    {
        $userModel = new UserModel();
        $user = $userModel->where('reset_token', $token)->first();

        if (!$user) {
            return redirect()->route('auth.login')->with('error', 'Invalid or expired token.');
        }

        $password = $this->request->getPost('password');
        $userModel->update($user['id'], [
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'reset_token' => null,
        ]);

        return redirect()->route('auth.login')->with('success', 'Password has been reset successfully.');
    }


    private function setUserSession($user,$permissionNames)
    {
        // Get user's role IDs for session
        $roleModel = new RoleModel();
        $role = $roleModel->getUserRoleNames($user['id']);
        $role = array_column($role, 'name');

        $sessionData = [
            'user_id'       => $user['id'],
            'username'      => $user['username'],
            'email'         => $user['email'],
            'user_roles'    => $role,
            'agency_id'     => $user['agency_id'],
            'first_name'    => $user['first_name'],
            'last_name'     => $user['last_name'],
            'isLoggedIn'    => true,
            //'permissions'   => $permissionNames, // Store permission names
        ];
        session()->set($sessionData);
    }




}
