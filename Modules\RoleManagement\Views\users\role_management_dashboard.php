<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
Role Management Dashboard
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
Role Management Dashboard
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Role Management</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-cog"></i> Role Management Dashboard
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info"></i> Role Assignment with Expiration</h5>
                    This dashboard provides quick access to role management features including role expiration functionality.
                </div>

                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-6">
                        <div class="card card-outline card-success">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-user-tag"></i> User Role Assignment
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>Assign roles to users with optional expiration dates.</p>
                                
                                <div class="form-group">
                                    <label for="userSelect">Select User to Assign Roles:</label>
                                    <select class="form-control" id="userSelect">
                                        <option value="">-- Select a User --</option>
                                        <?php if (isset($users)): ?>
                                            <?php foreach ($users as $user): ?>
                                                <option value="<?= $user['id'] ?>">
                                                    <?= esc($user['username']) ?> (<?= esc($user['email']) ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <button type="button" class="btn btn-success" onclick="assignRoles()">
                                    <i class="fas fa-user-tag"></i> Assign Roles
                                </button>
                                
                                <button type="button" class="btn btn-warning" onclick="viewExpiredRoles()">
                                    <i class="fas fa-history"></i> View Expired Roles
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- System Management -->
                    <div class="col-md-6">
                        <div class="card card-outline card-warning">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-cogs"></i> System Management
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>System-wide role management and cleanup tools.</p>
                                
                                <div class="btn-group-vertical w-100">
                                    <button type="button" class="btn btn-info mb-2" onclick="runExpiredRoleCleanup()">
                                        <i class="fas fa-broom"></i> Cleanup Expired Roles
                                    </button>
                                    
                                    <a href="<?= site_url('admin/roles') ?>" class="btn btn-primary mb-2">
                                        <i class="fas fa-list"></i> Manage Roles
                                    </a>
                                    
                                    <a href="<?= site_url('admin/permissions') ?>" class="btn btn-secondary mb-2">
                                        <i class="fas fa-key"></i> Manage Permissions
                                    </a>
                                    
                                    <a href="<?= site_url('admin/users') ?>" class="btn btn-success">
                                        <i class="fas fa-users"></i> Manage Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Access Links -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card card-outline card-info">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-link"></i> Quick Access Links
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h6>Role Assignment</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="<?= site_url('admin/users/assign-roles/1') ?>">Assign Roles to User #1</a></li>
                                            <li><a href="<?= site_url('admin/users/assign-roles/2') ?>">Assign Roles to User #2</a></li>
                                            <li><a href="<?= site_url('admin/users/assign-roles/3') ?>">Assign Roles to User #3</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3">
                                        <h6>Expired Roles</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="<?= site_url('admin/users/expired-roles/1') ?>">User #1 Expired Roles</a></li>
                                            <li><a href="<?= site_url('admin/users/expired-roles/2') ?>">User #2 Expired Roles</a></li>
                                            <li><a href="<?= site_url('admin/users/expired-roles/3') ?>">User #3 Expired Roles</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3">
                                        <h6>Role Management</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="<?= site_url('admin/roles') ?>">All Roles</a></li>
                                            <li><a href="<?= site_url('admin/roles/create') ?>">Create New Role</a></li>
                                            <li><a href="<?= site_url('admin/permissions') ?>">All Permissions</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3">
                                        <h6>User Management</h6>
                                        <ul class="list-unstyled">
                                            <li><a href="<?= site_url('admin/users') ?>">All Users</a></li>
                                            <li><a href="<?= site_url('admin/users/create') ?>">Create New User</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card card-outline card-secondary">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-question-circle"></i> How to Access Role Assignment
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6>Method 1: From User Management</h6>
                                <ol>
                                    <li>Go to <strong>Admin → Users</strong></li>
                                    <li>In the user list, click the <span class="badge badge-success"><i class="fas fa-user-tag"></i></span> button in the Actions column</li>
                                    <li>This will take you to the role assignment page with expiration functionality</li>
                                </ol>

                                <h6>Method 2: From User Edit Page</h6>
                                <ol>
                                    <li>Go to <strong>Admin → Users</strong></li>
                                    <li>Click the <span class="badge badge-warning"><i class="fas fa-edit"></i></span> button to edit a user</li>
                                    <li>In the user edit page header, click <strong>"Assign Roles"</strong> button</li>
                                </ol>

                                <h6>Method 3: Direct URL</h6>
                                <p>You can directly access the role assignment page using:</p>
                                <code><?= site_url('admin/users/assign-roles/{user_id}') ?></code>
                                <p class="mt-2">Replace <code>{user_id}</code> with the actual user ID.</p>

                                <div class="alert alert-warning mt-3">
                                    <strong>Note:</strong> The role assignment form includes:
                                    <ul class="mb-0">
                                        <li>Expiration date picker for each role</li>
                                        <li>Active/Inactive status toggle</li>
                                        <li>Validation to ensure users have at least one active role</li>
                                        <li>Visual indicators for expired roles</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function assignRoles() {
    const userId = document.getElementById('userSelect').value;
    if (!userId) {
        alert('Please select a user first.');
        return;
    }
    window.location.href = '<?= site_url('admin/users/assign-roles/') ?>' + userId;
}

function viewExpiredRoles() {
    const userId = document.getElementById('userSelect').value;
    if (!userId) {
        alert('Please select a user first.');
        return;
    }
    window.location.href = '<?= site_url('admin/users/expired-roles/') ?>' + userId;
}

function runExpiredRoleCleanup() {
    if (confirm('This will deactivate all expired roles system-wide. Continue?')) {
        // This would typically be an AJAX call to a cleanup endpoint
        alert('Cleanup functionality would be implemented here.\n\nFor now, you can run: php spark roles:deactivate-expired');
    }
}
</script>
<?= $this->endSection() ?>
