<div class="row">
    <div class="col-md-6">
        <h6><strong>Role Information</strong></h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Name:</strong></td>
                <td><?= esc($role['name']) ?></td>
            </tr>
            <tr>
                <td><strong>Description:</strong></td>
                <td><?= esc($role['description'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Status:</strong></td>
                <td>
                    <?php if ($role['is_active']): ?>
                        <span class="badge badge-success">Active</span>
                    <?php else: ?>
                        <span class="badge badge-danger">Inactive</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Users Assigned:</strong></td>
                <td><?= $user_count ?></td>
            </tr>
            <tr>
                <td><strong>Created:</strong></td>
                <td><?= date('M d, Y H:i', strtotime($role['created_at'])) ?></td>
            </tr>
            <tr>
                <td><strong>Updated:</strong></td>
                <td><?= date('M d, Y H:i', strtotime($role['updated_at'])) ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6><strong>Assigned Permissions (<?= count($permissions) ?>)</strong></h6>
        <?php if (!empty($permissions)): ?>
            <div class="permissions-list" style="max-height: 300px; overflow-y: auto;">
                <?php 
                $groupedPermissions = [];
                foreach ($permissions as $permission) {
                    $category = $permission['category'] ?? 'General';
                    $groupedPermissions[$category][] = $permission;
                }
                ?>
                <?php foreach ($groupedPermissions as $category => $categoryPermissions): ?>
                    <div class="mb-2">
                        <h6 class="text-primary"><?= esc($category) ?></h6>
                        <?php foreach ($categoryPermissions as $permission): ?>
                            <span class="badge badge-info mr-1 mb-1"><?= esc($permission['name']) ?></span>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p class="text-muted">No permissions assigned to this role.</p>
        <?php endif; ?>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="btn-group">
            <a href="<?= site_url('admin/roles/edit/' . $role['id']) ?>" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit Role
            </a>
            <a href="<?= site_url('admin/roles/assign-permissions/' . $role['id']) ?>" class="btn btn-info btn-sm">
                <i class="fas fa-key"></i> Manage Permissions
            </a>
        </div>
    </div>
</div>
