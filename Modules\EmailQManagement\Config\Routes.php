<?php

namespace Modules\UserManagement\Config;

// $routes->group('users', ['namespace' => 'Modules\UserManagement\Controllers', 'filter' => 'login'], function 
// ($routes) {


$routes->group('admin', function($routes) {

    $routes->group('email-queue', ['namespace' => 'Modules\EmailQManagement\Controllers'], function
    ($routes) {
        $routes->GET('/', 'EmailQueueController::index', ['as' => 'email.queue']);
        $routes->POST('datatable', 'EmailQueueController::datatable', ['as' => 'email.datatable']);
        $routes->GET('retry/(:num)', 'EmailQueueController::retry/$1', ['as' => 'email.retry']);
        $routes->GET('retry-now/(:num)', 'EmailQueueController::retryNow/$1', ['as' => 'email.retry_now']);
        $routes->GET('retry-later/(:num)', 'EmailQueueController::retryLater/$1', ['as' => 'email.retry_later']);
        $routes->GET('delete/(:num)', 'EmailQueueController::delete/$1', ['as' => 'email.delete']);
        $routes->POST('bulk-action', 'EmailQueueController::bulkAction', ['as' => 'email.bulk-action']);
        $routes->POST('bulk-retry', 'EmailQueueController::bulkRetry', ['as' => 'email.bulk_retry']);
        $routes->POST('bulk-delete', 'EmailQueueController::bulkDelete', ['as' => 'email.bulk_delete']);
        $routes->POST('bulk-resend', 'EmailQueueController::bulkResend', ['as' => 'email.bulk_resend']);
        $routes->GET('run-queue', 'EmailQueueController::runQueueNow', ['as' => 'email.run_queue']);
        $routes->GET('preview/(:num)', 'EmailQueueController::preview/$1', ['as' => 'email.preview']);
        $routes->GET('test-email', 'EmailQueueController::testEmail', ['as' => 'email.test']);
        $routes->GET('test-email-placeholder', 'EmailQueueController::testEmailPlaceholder', ['as' => 'email.test.placeholder']);

    });

});
