<?php

namespace Modules\Authentication\Filters;

use <PERSON>Igniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;
use App\Services\PermissionService;

class RoleFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $requiredRole = $arguments[0]; // e.g., "System Administrator"
        $userId = session()->get('user_id');

        if (!$userId) {
            return redirect()->to('/auth/login')->with('error', 'Access denied. Please login first.');
        }

        // Use the PermissionService to check user roles
        $permissionService = new PermissionService();

        if (!$permissionService->userHasRole($userId, $requiredRole)) {
            return redirect()->to('/')->with('error', 'You do not have the required role to access this page.');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Optional post-processing
    }
}
