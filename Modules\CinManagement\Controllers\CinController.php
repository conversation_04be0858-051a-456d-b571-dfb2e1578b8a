<?php

namespace Modules\CinManagement\Controllers;

use App\Controllers\BaseController;
use Modules\CinManagement\Models\CinModel;
// use Modules\CinManagement\Models\RoleModel;
use Modules\CinManagement\Config\Validation;

class CinController extends BaseController
{
    public function index()
    {

        $cinModel = new CinModel();
        $cinList = $cinModel->where('co_email!=','')->findAll();
        // $data['cin'] = $cinModel->getUsersWithRoles();
        // dd($data['cin']);
        return view('Modules\CinManagement\Views\users\index', [
            'cinList' => $cinList,
        ]);
    }

    public function create()
    {
        $roleModel = new RoleModel();
        $data['roles'] = $roleModel->findAll();
        return view('Modules\CinManagement\Views\users\create', $data);
    }

    public function store()
    {
        $validation = new Validation();

        // Validate the input
        if(!$this->validate($validation->user, $validation->user_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());

        }

        $userModel = new UserModel();
        $data = $this->request->getPost();
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        $userModel->insert($data);
        return redirect()->route('user.index')->with('success', 'User created successfully.');
    }

    public function edit($id)
    {
        $userModel = new UserModel();
        $roleModel = new RoleModel();
        $data['user'] = $userModel->find($id);
        $data['roles'] = $roleModel->findAll();
        return view('Modules\UserManagement\Views\users\edit', $data);
    }

    public function update($id)
    {
        $validation = new Validation();

        // Validate the input
        if(!$this->validate($validation->user, $validation->user_errors)){
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userModel = new UserModel();
        $data = $this->request->getPost();
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        $userModel->update($id, $data);
        return redirect()->route('user.index')->with('success', 'User updated successfully.');
    }

    public function delete($id)
    {
        $userModel = new UserModel();
        $userModel->delete($id);
        return redirect()->route('user.index');
    }
}
