<?php

namespace Modules\EmailTemplates\Examples;

use Modules\EmailTemplates\Libraries\TemplateEngine;

class Usage
{
    protected TemplateEngine $templateEngine;
    
    public function __construct()
    {
        $this->templateEngine = new TemplateEngine();
    }
    
    /**
     * Example 1: Send Password Reset Email
     * 
     * Template slug: 'password-reset'
     * Required placeholders: user_name, reset_link, expiry_time
     */
    public function sendPasswordResetEmail(string $userEmail, string $userName, string $resetToken): bool
    {
        $resetLink = base_url("auth/reset-password?token={$resetToken}");
        
        $placeholders = [
            'user_name' => $userName,
            'reset_link' => $resetLink,
            'expiry_time' => '24 hours'
        ];
        
        try {
            return $this->templateEngine->sendEmail('password-reset', $userEmail, $placeholders, $userName);
        } catch (\Exception $e) {
            log_message('error', 'Password reset email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Example 2: Send Welcome Email
     * 
     * Template slug: 'welcome'
     * Required placeholders: user_name, user_email, activation_link
     */
    public function sendWelcomeEmail(string $userEmail, string $userName, string $activationToken): bool
    {
        $activationLink = base_url("auth/activate?token={$activationToken}");
        
        $placeholders = [
            'user_name' => $userName,
            'user_email' => $userEmail,
            'activation_link' => $activationLink
        ];
        
        try {
            return $this->templateEngine->sendEmail('welcome', $userEmail, $placeholders, $userName);
        } catch (\Exception $e) {
            log_message('error', 'Welcome email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Example 3: Send Order Confirmation Email
     * 
     * Template slug: 'order-confirmation'
     * Custom placeholders: order_number, order_total, order_items, delivery_date
     */
    public function sendOrderConfirmationEmail(array $orderData): bool
    {
        $placeholders = [
            'user_name' => $orderData['customer_name'],
            'order_number' => $orderData['order_number'],
            'order_total' => '$' . number_format($orderData['total'], 2),
            'order_items' => $this->formatOrderItems($orderData['items']),
            'delivery_date' => date('F j, Y', strtotime($orderData['delivery_date'])),
            'tracking_link' => base_url("orders/track/{$orderData['order_number']}")
        ];
        
        try {
            return $this->templateEngine->sendEmail(
                'order-confirmation', 
                $orderData['customer_email'], 
                $placeholders, 
                $orderData['customer_name']
            );
        } catch (\Exception $e) {
            log_message('error', 'Order confirmation email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Example 4: Send Notification Email
     * 
     * Template slug: 'notification'
     * Custom placeholders: notification_title, notification_message, action_link
     */
    public function sendNotificationEmail(string $userEmail, string $userName, array $notification): bool
    {
        $placeholders = [
            'user_name' => $userName,
            'notification_title' => $notification['title'],
            'notification_message' => $notification['message'],
            'action_link' => $notification['action_url'] ?? base_url('dashboard'),
            'notification_date' => date('F j, Y \a\t g:i A')
        ];
        
        try {
            return $this->templateEngine->sendEmail('notification', $userEmail, $placeholders, $userName);
        } catch (\Exception $e) {
            log_message('error', 'Notification email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Example 5: Preview Template Before Sending
     * 
     * Useful for testing or showing previews to users
     */
    public function previewTemplate(string $templateSlug, array $placeholders = []): array
    {
        try {
            return $this->templateEngine->previewTemplate($templateSlug, $placeholders);
        } catch (\Exception $e) {
            log_message('error', 'Template preview failed: ' . $e->getMessage());
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Example 6: Bulk Email Sending
     * 
     * Send the same template to multiple recipients with different placeholders
     */
    public function sendBulkEmails(string $templateSlug, array $recipients): array
    {
        $results = [];
        
        foreach ($recipients as $recipient) {
            try {
                $result = $this->templateEngine->sendEmail(
                    $templateSlug,
                    $recipient['email'],
                    $recipient['placeholders'] ?? [],
                    $recipient['name'] ?? null
                );
                
                $results[] = [
                    'email' => $recipient['email'],
                    'success' => $result,
                    'error' => null
                ];
                
            } catch (\Exception $e) {
                $results[] = [
                    'email' => $recipient['email'],
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Example 7: Using the API Endpoint
     * 
     * Send email via HTTP API call (useful for external integrations)
     */
    public function sendEmailViaAPI(string $templateSlug, string $toEmail, array $placeholders = []): array
    {
        $client = \Config\Services::curlrequest();
        
        $data = [
            'template_slug' => $templateSlug,
            'to_email' => $toEmail,
            'placeholders' => json_encode($placeholders)
        ];
        
        try {
            $response = $client->post(base_url('api/email-templates/send'), [
                'form_params' => $data,
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ]
            ]);
            
            return json_decode($response->getBody(), true);
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Helper method to format order items for email
     */
    protected function formatOrderItems(array $items): string
    {
        $html = '<ul>';
        foreach ($items as $item) {
            $html .= "<li>{$item['name']} - Qty: {$item['quantity']} - \${$item['price']}</li>";
        }
        $html .= '</ul>';
        
        return $html;
    }
}

/**
 * Integration Examples for Controllers
 */

// Example: In your Auth Controller
class AuthController extends BaseController
{
    public function forgotPassword()
    {
        // ... validation logic ...
        
        $user = $this->userModel->where('email', $email)->first();
        $resetToken = bin2hex(random_bytes(32));
        
        // Save reset token to database
        $this->passwordResetModel->insert([
            'email' => $email,
            'token' => $resetToken,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
        ]);
        
        // Send password reset email
        $emailService = new \Modules\EmailTemplateManager\Examples\UsageExamples();
        $result = $emailService->sendPasswordResetEmail($email, $user['name'], $resetToken);
        
        if ($result) {
            return $this->response->setJSON(['success' => true, 'message' => 'Password reset email sent']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to send email']);
        }
    }
}

// Example: In your User Registration Controller
class RegisterController extends BaseController
{
    public function register()
    {
        // ... registration logic ...
        
        $userId = $this->userModel->insert($userData);
        $activationToken = bin2hex(random_bytes(32));
        
        // Save activation token
        $this->userModel->update($userId, ['activation_token' => $activationToken]);
        
        // Send welcome email
        $emailService = new \Modules\EmailTemplateManager\Examples\UsageExamples();
        $emailService->sendWelcomeEmail($userData['email'], $userData['name'], $activationToken);
        
        return redirect()->to('/login')->with('message', 'Registration successful! Check your email to activate your account.');
    }
}

// Example: In your Order Controller
class OrderController extends BaseController
{
    public function confirmOrder($orderId)
    {
        $order = $this->orderModel->getOrderWithItems($orderId);
        
        // Send order confirmation email
        $emailService = new \Modules\EmailTemplateManager\Examples\UsageExamples();
        $emailService->sendOrderConfirmationEmail($order);
        
        return view('order/confirmation', ['order' => $order]);
    }
}

/**
 * Command Line Usage Example
 * 
 * Create a command to send bulk emails or maintenance notifications
 */
class EmailCommand extends BaseCommand
{
    protected $group = 'Email';
    protected $name = 'email:send';
    protected $description = 'Send emails using templates';
    
    public function run(array $params)
    {
        $templateSlug = $params[0] ?? null;
        $recipientFile = $params[1] ?? null;
        
        if (!$templateSlug || !$recipientFile) {
            CLI::error('Usage: php spark email:send <template_slug> <recipients_csv_file>');
            return;
        }
        
        // Read recipients from CSV
        $recipients = $this->readRecipientsFromCSV($recipientFile);
        
        // Send bulk emails
        $emailService = new \Modules\EmailTemplateManager\Examples\UsageExamples();
        $results = $emailService->sendBulkEmails($templateSlug, $recipients);
        
        // Display results
        $successful = array_filter($results, fn($r) => $r['success']);
        $failed = array_filter($results, fn($r) => !$r['success']);
        
        CLI::write("Emails sent: " . count($successful), 'green');
        CLI::write("Emails failed: " . count($failed), 'red');
        
        if (!empty($failed)) {
            CLI::write("Failed emails:");
            foreach ($failed as $failure) {
                CLI::write("  - {$failure['email']}: {$failure['error']}", 'red');
            }
        }
    }
    
    protected function readRecipientsFromCSV(string $file): array
    {
        // Implementation to read CSV file and return recipients array
        // Format: email, name, placeholder1, placeholder2, etc.
        return [];
    }
}
